- config:
    name: 保存资源归属
#    base_url: ${ENV(saas_common_manage_url)}


- test:
    name: 保存资源归属
    api: api/rpc/batchSaveResourceBelong.yml
    variables:
      deptPath: 97919
      directDeptId: 97919
      reason: "[{\"participantType\":\"initiator\",\"personOid\":\"391d0b87c284491eba58aba6dc505c86\"},{\"participantType\":\"sign\",\"personOid\":\"391d0b87c284491eba58aba6dc505c86\"}]"
      resourceId: 84ba26cba94244af94f15b498ab8d53a,
      subjectGid: d30d458edb024f45a6055628fa72eea6,
      subjectOid: 9980e26eb8ab4ab9aa8f5a84bdda63b5,
      resourceType: 0
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.count", 1]