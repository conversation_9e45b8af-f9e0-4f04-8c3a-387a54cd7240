name: 编辑接口转发及参数替换配置
request:
  url: ${ENV(saas_common_manage_url)}/v1/saas-common/urimanage/forwards/$id/update
  method: POST
  headers:
    x-timevale-jwtcontent: eyJhbGlhcyI6Iuemu-atjCIsImlkIjoibGlnZSJ9
    X-Tsign-Open-App-Id: ${ENV(BZQ_appId)}
    X-Tsign-Open-Operator-Id: ${ENV(lige_orgId)}
  json:
    {
      "defender": {
        "body": $body,
        "header": $header,
        "query": $query
      },
      "desc": $desc,
      "forwardMethod": $forwardMethod,
      "forwardType": $forwardType,
      "forwardUri": $forwardUri,
      "method": $method,
      "tags": $tags,
      "uri": $uri
    }
