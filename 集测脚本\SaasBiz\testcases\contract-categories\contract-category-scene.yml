- config:
    name: 合同类型串场景
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      categoryName1: 合同类型1
      fieldName1: 手机号
      categoryName2: 合同类型2
      flowTemplateId1: ac8ae0af434f4488992ed25f0c65170f
      flowTemplateName1: 集测脚本专用模板

- test:
    name: 校验合同类型名称是否已存在-名称不存在
    api: api/contract-categories/check-name-existed.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryName: $categoryName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", false]

- test:
    name: 获取系统推荐的合同类型列表
    api: api/contract-categories/system-list.yml
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.categories.0.categories.1.extractFields", []]

- test:
    name: 保存合同类型-添加了字段
    api: api/contract-categories/save-contract-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryName: $categoryName1
      extractFields:
        [
        {
          "fieldDesc": "",
          "fieldName": "$fieldName1",
          "fieldType": "NUMBER"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - categoryId1: content.data.categoryId

- test:
    name: 查询合同类型列表-验证合同类型创建成功
    api: api/contract-categories/contract-categories-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      categoryId: $categoryId1
      categoryName: ""
      fieldId: ""
      flowTemplateId: ""
      flowTemplateName: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.categories.0.enable", false]
      - eq: ["content.data.categories.0.categoryName", $categoryName1]
      - eq: ["content.data.categories.0.extractFields.0", $fieldName1]

- test:
    name: 查询合同类型详情
    api: api/contract-categories/contract-category-detail.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.categoryName", $categoryName1]
      - eq: ["content.data.extractFields.0.fieldName", $fieldName1]

- test:
    name: 校验合同类型名称是否已存在-名称已存在
    api: api/contract-categories/check-name-existed.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryName: $categoryName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", true]

- test:
    name: 编辑合同类型-修改合同类型名称，去掉添加的字段
    api: api/contract-categories/update-contract-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
      categoryName: $categoryName2
      extractFields: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.categoryId", $categoryId1]

- test:
    name: 查询合同类型列表-验证合同类型更新成功
    api: api/contract-categories/contract-categories-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      categoryId: $categoryId1
      categoryName: ""
      fieldId: ""
      flowTemplateId: ""
      flowTemplateName: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.categories.0.categoryName", $categoryName2]
      - eq: ["content.data.categories.0.extractFields", null]

- test:
    name: 批量获取流程模板文件基本信息列表
    api: api/flowTemplates/batch-query-simple-files.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateIds: [$flowTemplateId1]
      withContractCategory: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId1: content.data.flowTemplateFiles.0.files.0.fileId

- test:
    name: 关联流程模板
    api: api/contract-categories/relate-flow-templates.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
      relateFlowTemplates:
        [
        {
          "flowTemplateId":"$flowTemplateId1",
          "flowTemplateName":"$flowTemplateName1",
          "relateFileIds":[
            "$fileId1"
          ]
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询合同类型列表-验证关联模板成功
    api: api/contract-categories/contract-categories-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      categoryId: $categoryId1
      categoryName: ""
      fieldId: ""
      flowTemplateId: ""
      flowTemplateName: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.categories.0.flowTemplates.0", $flowTemplateName1]

- test:
    name: 基于合同类型id查询已关联的流程模板列表
    api: api/contract-categories/query-related-flow-templates.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.relateFlowTemplates", []]

- test:
    name: 基于流程模板查询已关联的合同类型列表
    api: api/contract-categories/query-related-categories.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.categories.0.categoryId", $categoryId1]

- test:
    name: 更新合同类型状态-启用
    api: api/contract-categories/update-status.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryIds: [$categoryId1]
      enable: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除合同类型-删除失败
    api: api/contract-categories/delete-contract-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryIds: [$categoryId1]
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 启用状态的合同类型不支持删除]

- test:
    name: 更新合同类型状态-禁用
    api: api/contract-categories/update-status.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryIds: [$categoryId1]
      enable: false
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除合同类型-删除成功
    api: api/contract-categories/delete-contract-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryIds: [$categoryId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 基于流程模板查询已关联的合同类型列表-无数据
    api: api/contract-categories/query-related-categories.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.categories", []]
