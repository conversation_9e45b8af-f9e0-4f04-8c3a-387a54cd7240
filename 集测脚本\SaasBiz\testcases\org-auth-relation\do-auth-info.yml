- config:
    name: 给授权企业分配应用数量
    variables:
      tenantId: 9db4774259964744914a19f09f1f1e4d
      authGid: dd599a1bee394b4ab538206df24ea682
      orderId: aedbd0b4-2969-408d-97c8-64cef0b77322
      configKey: shareAppCount
      configValue1: 1
      configValue2: 200000
      configValue3: 0
      operatorId: 475955db49aa4289a8cb9422e200988c

- test:
    name: 获取当前企业应用分享的详细信息
    api: api/org-auth-relation/can-auth-infos.yml
    variables:
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 给其他企业分配应用数量成功
    #共100000份，如果送完了就在特殊审批赠送重新送一下，商品：智能合同-集成应用拓展包
    api: api/org-auth-relation/do-auth-info.yml
    variables:
      configValue: $configValue1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.result", true]

- test:
    name: 给其他企业分配应用数量失败-数量为0
    api: api/org-auth-relation/do-auth-info.yml
    variables:
      configValue: $configValue3
    validate:
      - eq: ["content.code", 120000004]
      - eq: ["content.message", 分配的应用数量至少为1个]
      - eq: ["content.data", null]

- test:
    name: 给其他企业分配应用数量失败-数量超出上限
    api: api/org-auth-relation/do-auth-info.yml
    variables:
      configValue: $configValue2
    validate:
      - eq: ["content.code", 120000001]
      - eq: ["content.message", 超过可分配应用数量]
      - eq: ["content.data", null]

- test:
    name: 获取分配给其他企业的分配信息
    api: api/org-auth-relation/get-auth-info.yml
    variables:
      authType: shareAppCount
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]