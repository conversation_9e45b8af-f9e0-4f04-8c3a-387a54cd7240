- config:
    name: 复制表单(已完成)
    variables:
        - code: 0
        - message: 成功

- test:
    name: 复制表单
    api: api/info-collect/form_copy.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d "
        copyKey: "b447b63caef94a2cb77b5f62ec01dde8"
        operatorId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
    validate:
        - eq: ["content.code", 31300001]
        - eq: ["content.message", "复制凭证已失效，请重新生成"]


- test:
    name: 复制表单，无operatorId
    api: api/info-collect/form_copy.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d "
        copyKey: "b447b63caef94a2cb77b5f62ec01dde8"
        operatorId: ""
    validate:
        - eq: ["content.code", 10000007]
        - eq: ["content.message", '请求访问失败：鉴权用户或主体账号缺失']