- config:
    name: 获取用户可查看的企业天印流程列表

- test:
    name: 获取用户可查看的企业天印流程列表-Y
    api: api/saas_tianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      flowName: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      status: ""
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-N
    api: api/saas_tianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      flowName: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      status: ""
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-flowName模糊查询
    api: api/saas_tianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      flowName: "个人"
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      status: ""
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态签署中
    api: api/saas_tianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      flowName: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      status: 1
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态完成
    api: api/saas_tianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      flowName: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      status: 2
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态撤回
    api: api/saas_tianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      flowName: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      status: 3
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态拒签
    api: api/saas_tianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      flowName: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      status: 4
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态过期
    api: api/saas_tianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      flowName: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      status: 5
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态作废
    api: api/saas_tianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      flowName: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      status: 6
      code: 0
      message: "成功"