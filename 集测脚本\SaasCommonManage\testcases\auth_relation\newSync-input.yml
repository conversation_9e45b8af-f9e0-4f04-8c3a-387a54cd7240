- config:
    name: 同步钉签的增量子企业数据
    variables:
      - orgId1: "634f518fe6834f21a0a226ff326e9770"
      - accountId1: "276b5a8f2a964142bb1ebcf0dd82bf72"
      - orgId: $orgId1
      - accountId: $accountId1
      - orgCode2: 91000000C78LE4U306
      - orgName2: esigntest明绣测试1206

- test:
    name: 同步钉签的增量子企业数据-parentTenantGid为空
    api: api/auth_relation/newSync-input.yml
    variables:
      parentTenantGid: ""  # esigntest明绣测试1205
      parentTenantOid: "634f518fe6834f21a0a226ff326e9770"
      childTenantGid: "3b76cf9ba17c4f4f8c99524c223f933c"  # esigntest明绣测试1206
      childTenantOid: "d667e01dfe35437cbfd753bc824a5d75"
      expireTime: "*************"
    validate:
      - eq: ["content.success", false]
      - contains: ["content.message", "请传入 parentTenantGid"]
      - contains: ["content.exception.message", "请传入 parentTenantGid"]

- test:
    name: 同步钉签的增量子企业数据-parentTenantOid
    api: api/auth_relation/newSync-input.yml
    variables:
      parentTenantGid: "6423df9651e844aab633e9e32d6ad1eb"  # esigntest明绣测试1205
      parentTenantOid: ""
      childTenantGid: "3b76cf9ba17c4f4f8c99524c223f933c"  # esigntest明绣测试1206
      childTenantOid: "d667e01dfe35437cbfd753bc824a5d75"
      expireTime: "*************"
    validate:
      - eq: [ "content.success", false ]
      - contains: [ "content.message", "请传入 parentTenantOid" ]
      - contains: [ "content.exception.message", "请传入 parentTenantOid" ]

- test:
    name: 同步钉签的增量子企业数据-childTenantGid为空
    api: api/auth_relation/newSync-input.yml
    variables:
      parentTenantGid: "6423df9651e844aab633e9e32d6ad1eb"  # esigntest明绣测试1205
      parentTenantOid: "634f518fe6834f21a0a226ff326e9770"
      childTenantGid: ""  # esigntest明绣测试1206
      childTenantOid: "d667e01dfe35437cbfd753bc824a5d75"
      expireTime: "*************"
    validate:
      - eq: [ "content.success", false ]
      - contains: [ "content.message", "请传入 childTenantGid" ]
      - contains: [ "content.exception.message", "请传入 childTenantGid" ]

- test:
    name: 同步钉签的增量子企业数据-childTenantOid为空
    api: api/auth_relation/newSync-input.yml
    variables:
      parentTenantGid: "6423df9651e844aab633e9e32d6ad1eb"  # esigntest明绣测试1205
      parentTenantOid: "634f518fe6834f21a0a226ff326e9770"
      childTenantGid: "3b76cf9ba17c4f4f8c99524c223f933c"  # esigntest明绣测试1206
      childTenantOid: ""
      expireTime: "*************"
    validate:
      - eq: [ "content.success", false ]
      - contains: [ "content.message", "请传入 childTenantOid" ]
      - contains: [ "content.exception.message", "请传入 childTenantOid" ]

#- test:
#    name: 同步钉签的增量子企业数据-子企业被其它企业关联
#    api: api/auth_relation/newSync-input.yml
#    variables:
#      parentTenantGid: "3b76cf9ba17c4f4f8c99524c223f933c"  # esigntest明绣测试1206
#      parentTenantOid: "d667e01dfe35437cbfd753bc824a5d75"
#      childTenantGid: "336c2c9eef2845a9b7f91329a44f8500"  # esigntest测试智能抽取商业化
#      childTenantOid: "a691f4f3bcdd41c380956856a5ff0d7d"
#      expireTime: "${todayEnd_getTimeStamp_ms()}"
#    validate:
#      - eq: [ "content.failureReason", "该公司已经是其它企业的关联企业，不可再添加" ]

- test:
    name: 同步钉签的增量子企业数据-主子企业是同一个
    api: api/auth_relation/newSync-input.yml
    variables:
      parentTenantGid: "6423df9651e844aab633e9e32d6ad1eb"  # esigntest明绣测试1205
      parentTenantOid: "634f518fe6834f21a0a226ff326e9770"
      childTenantGid: "6423df9651e844aab633e9e32d6ad1eb"  # esigntest明绣测试1206
      childTenantOid: "634f518fe6834f21a0a226ff326e9770"
      expireTime: "*************"
    validate:
      - eq: [ "content.failureReason", "不可添加自己为关联企业" ]
