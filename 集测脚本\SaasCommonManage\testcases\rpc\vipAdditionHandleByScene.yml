- config:
    name: 会员增配功能续费

- test:
    name: 会员增配功能续费
    api: api/rpc/vipAdditionHandleByScene.yml
    variables:
      "funcCodes": [
        "contract_category_manage",
        "relation_contract_manage"
      ]
      "subjectGid": "096a6271fa01492796a4bd53b6c0ef35"
      "effectiveStartTime": "1732032000"
      "effectiveEndTime": "1921334400"
      "sceneCode": 1
      "executeAction": 1
    validate:
      - eq: ["status_code", 200]