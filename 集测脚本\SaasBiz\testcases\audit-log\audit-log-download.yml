- config:
    name: 下载审计日志


- test:
    name: 下载审计日志
    api: api/audit-log/audit-log-download.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        "accountIds": []
        "appid": "**********"
        "endTime": *************
        "event": "直接发起"
        "firstModule": "saas_process_start"
        "innerCall": false
        "resourceId": ""
        "startTime": *************
        "tagName": ""
        "tenantId": ""
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]

- test:
    name: 下载审计日志-innerCall为空
    api: api/audit-log/audit-log-download.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        "accountIds": []
        "appid": "**********"
        "endTime": *************
        "event": "直接发起"
        "firstModule": "saas_process_start"
        "innerCall":
        "resourceId": ""
        "startTime": *************
        "tagName": ""
        "tenantId": ""
    validate:
        - eq: ["content.code", *********]
        - contains: ["content.message", "参数错误: 是否是内部调用不能为空"]

- test:
    name: 下载审计日志-非企业成员
    api: api/audit-log/audit-log-download.yml
    variables:
        orgId_lige: "00ccae92ea86444ca5fbadb42026c943"
        "accountIds": []
        "appid": "**********"
        "endTime": *************
        "event": "直接发起"
        "firstModule": "saas_process_start"
        "innerCall": false
        "resourceId": ""
        "startTime": *************
        "tagName": ""
        "tenantId": ""
    validate:
        - eq: ["content.code", ********]
        - contains: ["content.message", "您不是该企业成员，请联系企业管理员加入企业。"]