 #创建企业法人图片印章
    variables:
        appid: ${ENV(appid)}
    request:
        url: ${ENV(inner_open_url)}/v1/saas-common/organizations/seals/create-legal-image
        method: POST
        headers: ${gen_headers($appid, X-Tsign-Open-Tenant-Id=$tenantid, X-Tsign-Open-Operator-Id=$operatorid)}
        json:
          {
            "alias": $alias,
            "data": $data,
            "downloadFlag": $downloadFlag,
            "height": $height,
            "notifyUrl": $notifyUrl,
            "operateType": $operateType,
            "sealType": $sealType,
            "transparentFlag": $transparentFlag,
            "type": $type,
            "uploadFileMd5": $uploadFileMd5,
            "width": $width
          }
