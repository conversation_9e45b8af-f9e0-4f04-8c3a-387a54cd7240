- config:
    name: 流程模板批量禁用-异步
    variables:
        - code: 0
        - message: 成功

- test:
      name: 流程模板批量禁用-异步
      variables:
        flowTemplateIds: "b6b0701b37684bb687fb34f1430be8d0"
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        operatorId : "a690089d48a14707a1cb78c453a0d991"

      api: api/flowTemplates/flowTemplate_batch_disable_async.yml
      validate:
          - eq: ["content.code", 0]
          - eq: ["content.message", "成功"]