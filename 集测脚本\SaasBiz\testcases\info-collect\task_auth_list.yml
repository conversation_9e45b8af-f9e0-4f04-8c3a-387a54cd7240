- config:
    name: 查询已授权的列表(已完成)
    variables:
        - code: 0
        - message: 成功

- test:
    name: 查询已授权的列表
    api: api/info-collect/task_auth_list.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d "
        operatorId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        formId: "form6500106ae4b0b004e41cfff2"
        taskId : "468"
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]
        - ne: ["content.data", Null]

- test:
    name: 查询已授权的列表，无formId，taskId
    api: api/info-collect/task_auth_list.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d "
        operatorId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        formId: ""
        taskId : ""
    validate:
        - eq: ["content.code", 31300001]
        - eq: ["content.message", "must not be blank"]
#        - ne: ["content.data", Null]