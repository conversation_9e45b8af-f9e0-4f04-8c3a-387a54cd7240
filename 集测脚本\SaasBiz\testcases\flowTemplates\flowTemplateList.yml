- config:
    name: 根据模板名称查询流程模板（服务异常）
    variables:
      orgOid1: 714902c36f2440e281b2ff984b6e29f4         #发起主体企业
      accountOid4: 3d02a7c203fc436d8f5baf86a5481235     #发起主体企业的管理员
      templateNameKey1: 两人仅签模板


- test:
    name: 根据模板名称查询流程模板
    api: api/flowTemplates/flowTemplateList.yml
    variables:
      tenantId: $orgOid1
      operatorId: $accountOid4
      pageNum: 1
      pageSize: 1
      flowTemplateName: $templateNameKey1
      status: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
#    extract:
#      - flowTemplateId1: content.data.list.0.flowTemplateId
