- config:
    name: 企业模版数据源渠道
    variables:
      appId: ${ENV(appid)}
      tenantId: 08486d61823d4c2086122b26fb3e615a
      operatorId: 0bc26b27ede64cc48e7a800f4c5cbac7

- test:
    name: 获取企业的模板数据源渠道信息
    api: api/dataSource/DataSourceChannel.yml
    variables:
      appId: $appId
      tenantId: $tenantId
      operatorId: $operatorId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.channels.0.dataSourceChannel", "ESIGN"]