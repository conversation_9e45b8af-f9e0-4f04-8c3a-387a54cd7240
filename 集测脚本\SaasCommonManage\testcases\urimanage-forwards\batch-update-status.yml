- config:
    name: 批量更新接口转发及参数替换配置状态
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      uri1: "/testsuite/batchUpdateForwardsStatus"


- test:
    skipIf: ${queryForwards($uri1)}
    name: 新增接口转发及参数替换配置
    api: api/urimanage-forwards/addForward.yml
    variables:
      desc: 集测批量修改转发及参数替换状态
      forwardMethod: GET
      forwardType: 0
      forwardUri: $uri1
      method: GET
      tags: ""
      uri: $uri1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取参数替换接口列表
    api: api/urimanage-forwards/queryForward.yml
    variables:
      uri: $uri1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - id1: content.data.uris.0.id

- test:
    name: 批量更新接口转发及参数替换配置状态
    api: api/urimanage-forwards/batch-update-status.yml
    variables:
      ids: [$id1]
      status: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
