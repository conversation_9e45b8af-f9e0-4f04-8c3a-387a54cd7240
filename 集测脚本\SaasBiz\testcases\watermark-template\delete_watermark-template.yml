- config:
    name: 删除水印模板


- test:
    name: 删除水印模板-不存在的水印模板id
    api: api/watermark-template/delete_watermark-template.yml
    variables:
      watermarkId: 1
      tenantId: ${ENV(orgId2)}
      operatorid: ${ENV(accountId1)}
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", ********]
#      - eq: ["content.message", "水印模板不存在或已被删除"]



- test:
    name: 删除水印模板-删除系统水印
    api: api/watermark-template/delete_watermark-template.yml
    variables:
      watermarkId: 4efaea0251b14dc88d674a4573a5b4b4
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", ********]
      - eq: ["content.message", "系统默认水印模板不允许删除!"]



- test:
    name: 保存水印模板
    api: api/watermark-template/save_watermark-template.yml
    variables:
      "color": "rgb(217,217,217)"
      "fontFamily": 1
      "size": 16
      "imgHeight": 44
      "imgWidth": 44
      "opacity": 0.7
      "floatMode": "DOWN"
      "leftX": 0
      "pages": [ ]
      "positionType": "TILE"
      "tileDensity": 0.5
      "topY": 0
      "rotate": 30
      "content": "集测新增水印测试"
      "contentType": 1
      "name": "集测新增水印"
      "type": 1
      "watermarkId": ""
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - watermarkId: content.data.watermarkId

- test:
    name: 删除水印模板-删除系统水印
    api: api/watermark-template/delete_watermark-template.yml
    variables:
      watermarkId: watermarkId=$watermarkId
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]