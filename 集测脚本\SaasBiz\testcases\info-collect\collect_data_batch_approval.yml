- config:
    name: 批量审核(404)
    variables:
        - code: 0
        - message: 成功

- test:
    name: 批量审核
    api: api/info-collect/collect_data_batch_approval.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        operatorId: "b6366e3a555048f79d12c5fead854124"
        approvalData : [
        {
            "infoCollectTaskId":514,
            "infoCollectRecordOuterId":"1702603881087868928"
        }
        ]
        formId : "form65011b2be4b0aae76a7432f0"
    validate:
        - eq: ["content.code", 31300001]
        - eq: ["content.message", "reason"]
        - ne: ["content.data", None]