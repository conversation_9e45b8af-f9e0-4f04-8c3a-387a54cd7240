- config:
    name: 创建有填写的静态epaas模板，使用模板发起流程并填写
    variables:
      fileMd5: m49PzxWxAG8sMwNkZrlJDw==
      fileType1: application/pdf
      fileName1: "test.pdf"
      fileSize1: 99580
      filePath1: "data/test.pdf"
      templateName1: 有填写的静态epaas模板-${getTimeStamp_ms()}
      orgId1: 2963014d2c9a4d0ca78ee05cde06f835
      orgName1: esigntest测试企业epaas模板专用1
      accountId1: ${ENV(mx_accountId)}
      account1: <EMAIL>
      accountName1: 明绣
      db_name1: doc-cooperation
      db_name2: epaas_doc_template
      taskName1: 使用有填写的静态epaas模板发起-${getTimeStamp_ms()}


- test:
    name: 文件直传创建文件
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      contentMd5: $fileMd5
      contentType: $fileType1
      convert2Pdf: false
      fileName: $fileName1
      fileSize: $fileSize1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId1: content.data.fileId
      - uploadUrl1: content.data.uploadUrl

- test:
    name: 上传文件到oss
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl1
      contentType: $fileType1
      contentMd5: $fileMd5
      filePath: $filePath1
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 创建epaas文件模板
    api: api/footstone-doc/create-doc-template.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fileId: $fileId1
      docTemplateName: $fileName1
      docTemplateType: 0
      epaasTag: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - docTemplateId1: content.data.docTemplateId

- test:
    name: 创建有填写的静态epaas模板
    api: api/contract-manager2/save_flowTemplate.yml
    variables:
      tenantId: $orgId1
      businessType: 0
      ccs: []
      files:
        [
        {
          "fileId": $docTemplateId1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $fileName1,
          "from":4,                 #文件来自 1-模板文件 2-合同文件 3-动态文件 4-epaas文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: ""
      initiatorAccountId: $accountId1
      participants:
        [
        {
          "participantSubjectType":0,
          "role":"1",
          "sealType":null,
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":null,
          "instances":null,
          "willTypes":[]
        },
        {
          "participantSubjectType":1,
          "role":"3",
          "sealType":null,
          "signRequirements":"1",
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方2",
          "participantId":null,
          "instances":null,
          "willTypes":[]
        }
        ]
      previewType: 0
      scene: 3
      taskName: $templateName1
      epaasTag: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.processId", null]
    extract:
      - flowTemplateId1: content.data.flowTemplateId

- test:
    name: epaas获取文档编辑地址-模板控件设置页获取tplToken
    api: api/epaas-doc-template/getDocumentsEditUrl.yml
    variables:
      json: {
        "serviceId": "doc-cooperation",
        "businessContext": {
          "clientId": "WEB",
          "flowTemplateId": $flowTemplateId1,
          "limitLoginUserOid": $accountId1,
          "batchDropSeal": "true",
          "bizScene": "FLOW"
        },
        pageParams: {
          "template.global.showWatermark": true
        },
        "fieldOwner": {
          "oid": $orgId1,
          "appId": "${ENV(app_id)}"
        },
        "expireAt": "${todayEnd_getTimeStamp_ms()}",
        "ids": [$docTemplateId1],
        "invalidAfterSave": false,
        "needLogin": true,
        "tenantId": $orgId1,
        "validationConfig": {
          "allFieldBindRole": true,
          "sealType": true
        }
      }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - tplToken1: content.data.tplToken

- test:
    name: 获取resourceId
    api: api/epaas-doc-template/content-draft.yml
    variables:
      tenantId: $orgId1
      token: $tplToken1
      sql1: "select content_id from test_epaas_doc_template.content where entity_id='$docTemplateId1';"
      contentId: ${select_sql($sql1, $db_name2)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - resourceId1: content.data.originFile.resourceId

- test:
    name: 获取templateRoleId
    api: api/epaas-doc-template/list-template-role.yml
    variables:
      tenantId: $orgId1
      token: $tplToken1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - templateRoleId1: content.data.0.id
      - templateRoleId2: content.data.1.id

- test:
    name: epaas批量保存草稿
    api: api/epaas-doc-template/batch-save-draft.yml
    variables:
      tenantId: $orgId1
      token: $tplToken1
      sql1: "select content_id from test_epaas_doc_template.content where entity_id='$docTemplateId1';"
      data:
        [
        {
          "contentId": "${select_sql($sql1, $db_name2)}",
          "baseFile": {
            "resourceId": $resourceId1,
            "fileType": "PDF"
          },
          "fields": [
          {
            "label": "单行文本1",
            "custom": false,
            "type": "TEXT",
            "sort": 1,
            "style": {
              "font": 1,
              "fontSize": 12,
              "textColor": "#000",
              "width": 160,
              "height": 15,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": 1,
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "ext": "{}"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": null,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "dataSource": null,
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": 20,
              "overflowType": 1,
              "minFontSize": 8,
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "signRequirements": "",
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-text",
                "assignedPosbean": null,
                "fastCheck": null,
                "addSealRule": ""
              },
              "sealTypes": [

              ]
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "",
            "templateRoleId": $templateRoleId1,
            "fieldKey": null,
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 53.18811881188119,
              "y": 725.1557707833772,
              "page": 1,
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "label": "企业章",
            "custom": false,
            "type": "SIGN",
            "sort": 1,
            "style": {
              "font": 1,
              "fontSize": 12,
              "textColor": "#000",
              "width": 150,
              "height": 150,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": 1,
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "ext": "{}"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": "yyyy-MM-dd HH:mm:ss",
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "dataSource": null,
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": null,
              "overflowType": 2,
              "minFontSize": 8,
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": "1",
              "tickOptions": null,
              "configExt": {
                "signRequirements": "1",
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-stamp",
                "assignedPosbean": false,
                "fastCheck": true,
                "addSealRule": "followSeal",
                "keyPosX": 0,
                "keyPosY": 0
              },
              "sealTypes": [
                "ORG"
              ]
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "",
            "templateRoleId": $templateRoleId2,
            "fieldKey": null,
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 194.84158415841586,
              "y": 688.*************,
              "page": 2,
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          }
          ],
          "pageFormatInfoParam": null
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]

- test:
    name: 启用流程模板
    api: api/flowTemplates/enable_flowTemplate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 流程发起详情信息
    api: api/contract-manager2/flowTemplateInfo.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - participantId1: content.data.participants.0.participantId
      - participantId2: content.data.participants.1.participantId
      - fileId2: content.data.files.0.fileId
      - fileName2: content.data.files.0.fileName

- test:
    name: 使用模板异步发起
    api: api/contract-manager2/start-async.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      businessType: 0
      epaasTag: true
      ccs: []
      files:
        [
        {
          "fileId": $fileId2,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $fileName2,
          "from":4,                 #文件来自 1-模板文件 2-合同文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: $flowTemplateId1
      initiatorAccountId: $accountId1
      participants:
        [
        {
          "participantSubjectType":0,
          "role":"1",
          "sealType":null,
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":$participantId1,
          "instances":[
          {
            "account":$account1,
            "accountOid":$accountId1,
            "accountName":$accountName1,
            "accountRealName":true,
            "comment":"",
            "subjectId":$accountId1,
            "subjectName":$accountName1,
            "subjectRealName":true,
            "subjectType":0,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[]
        },
        {
          "participantSubjectType":1,
          "role":"3",
          "sealType":null,
          "signRequirements":"1",
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方2",
          "participantId":$participantId2,
          "instances":[
          {
            "account":$account1,
            "accountOid":$accountId1,
            "accountName":$accountName1,
            "accountRealName":true,
            "comment":"",
            "subjectId":$orgId1,
            "subjectName":$orgName1,
            "subjectRealName":true,
            "subjectType":1,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[]
        }
        ]
      scene: 2
      taskName: $taskName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - processId1: content.data.processId

- test:
    name: 查询异步发起结果
    api: api/contract-manager2/process-result-poll.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: $processId1
    setup_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.done", true]

- test:
    name: 获取cooperationId
    api: api/contract-manager2/dispatch-url.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: $processId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.status", 1]
      - ne: ["content.data.url", null]
    extract:
      - cooperationId1: content.data.subProcessId

- test:
    name: 获取epaas填写地址-获取tplToken
    api: api/docmanager/getEpaasFillUrl.yml
    variables:
      sql1: "SELECT id FROM doc_cooperation.cooperation_task where cooperation_id='$cooperationId1';"
      sql2: "SELECT ext FROM doc_cooperation.cooperation_task where cooperation_id='$cooperationId1';"
      ext: ${select_sql($sql2, $db_name1)}
      taskId1: ${getValueFromJson($ext, epaasTaskId)}
      json: {
        "serviceId": "doc-cooperation",
        "businessContext": {
          "processId": $processId1,
          "cooperationId": $cooperationId1,
          "taskId": "${select_sql($sql1, $db_name1)}",
          "realNameSign": "true",
          "checkSigner": "false",
          "loginAccountId": $accountId1,
          "tenantId": $orgId1,
          "bizScene": "XY",
          "appId": "${ENV(app_id)}",
          "loginAccount": $account1,
          "secureMode": "true",
          "needSaasLogin": "true",
          "innerSkipLoginScene": "processFill",
          "subjectId": $orgId1,
          "expireTime": "${todayEnd_getTimeStamp_ms()}",
          "restrictLoginAccount": $accountId1,
          "finalTask": "true"
        },
        pageParams: {
          "template.preview.showFields": "fill",
          "template.global.tab.title": "合同填写",
          "template.global.showWatermark": true
        },
        "expireAt": "${todayEnd_getTimeStamp_ms()}",
        "fillTaskId": $taskId1,
        "mergeFillTaskIdList": [$taskId1],
        "checkUnlimitedParties": true,
        "needLogin": true,
        "tenantId": $orgId1,
        "restrictLoginAccount": $account1
      }
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 执行成功]
    extract:
      - tplToken2: content.data.tplToken

- test:
    name: 填写页展示的按钮
    api: api/cooperation/behavior-buttons.yml
    variables:
      sql1: "SELECT id FROM doc_cooperation.cooperation_task where cooperation_id='$cooperationId1';"
      tenantId: $orgId1
      operatorId: $accountId1
      cooperationId: $cooperationId1
      processId: $processId1
      taskId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - contained_by: ["content.data.supportButtons.0.code", ["REVOKE", "RUSH"]]
      - contained_by: ["content.data.supportButtons.1.code", ["REVOKE", "RUSH"]]

- test:
    name: 获取contentId
    api: api/epaas-doc-template/get-fill-task-info.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      token: $tplToken2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - contentId1: content.data.contents.0.contentId

- test:
    name: 获取fieldId
    api: api/epaas-doc-template/content-detail.yml
    variables:
      tenantId: $orgId1
      token: $tplToken2
      contentId: $contentId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - fieldIds1: content.data.fields

- test:
    name: 提交填写
    api: api/epaas-doc-template/submit-fill-task.yml
    variables:
      fieldId1: ${getValue($fieldIds1, type, TEXT, contentFieldId)}
      tenantId: $orgId1
      operatorId: $accountId1
      token: $tplToken2
      fillData:
        [
        {
          "fieldId": $fieldId1,
          "fillValue": "测试"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]

- test:
    name: 查询异步发起结果2
    api: api/contract-manager2/process-result-poll.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: $processId1
    setup_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
 #     - eq: ["content.data.done", true]

- test:
    name: 获取flowId
    api: api/contract-manager2/dispatch-url.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: $processId1
    setup_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.status", 2]
      - ne: ["content.data.url", null]
    extract:
      - flowId1: content.data.subProcessId

- test:
    name: 查询企业合同列表，验证填写完成流程状态为签署中
    api: api/offline_contract/grouping_lists.yml
    variables:
      tenantId: $orgId1
      OperatorId: $accountId1
      pageSize: 10
      pageNum: 1
      menuId: null
      matching: '[{"key":"processId","value":["$processId1"],"sort":"","isPublic":false},{"key":"processCreateTime","value":null,"sort":"","isPublic":false},{"key":"personName","value":[""],"sort":"","isPublic":false}]'
      withApproving: true
    setup_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.groupingProcessList.0.processStatus", 2]

- test:
    name: 撤回流程
    api: api/contract-manager/revoke-process.yml
    variables:
      operatorId: $accountId1
      processId: $processId1
      revokeReason: 集测撤回
      subjectId: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

#清理测试数据
- test:
    name: 删除流程模板
    api: api/contract-manager2/delete_flowTemplate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      sql1: "SELECT flow_template_id FROM doc_cooperation.flow_template where oid='$orgId1' and type=1 and flow_template_name='$templateName1' limit 1;"
      flowTemplateId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]