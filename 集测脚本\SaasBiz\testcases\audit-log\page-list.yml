- config:
    name: 查询审计日志列表


- test:
    name: 查询下载审计日志结果
    api: api/audit-log/page-list.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        "tenantId": ""
        "cursor": ""
        "firstModule": ""
        "startTime": ${get_next_month_x3_timestamp()}
        "endTime": ${getTimeStamp_ms()}
        "event": ""
        "resourceId": ""
        "accountIds": [ ]
        "pageNo": 1
        "pageSize": 10
        "innerCall": false
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]

- test:
    name: 查询下载审计日志结果-innerCall为空
    api: api/audit-log/page-list.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        "tenantId": ""
        "cursor": ""
        "firstModule": ""
        "startTime": *************
        "endTime": *************
        "event": ""
        "resourceId": ""
        "accountIds": [ ]
        "pageNo": 1
        "pageSize": 10
        "innerCall":
    validate:
        - eq: ["content.code", *********]
        - contains: ["content.message", "参数错误: 是否是内部调用不能为空"]

- test:
    name: 查询下载审计日志结果-非企业成员
    api: api/audit-log/page-list.yml
    variables:
        orgId_lige: "00ccae92ea86444ca5fbadb42026c943"
        "tenantId": ""
        "cursor": ""
        "firstModule": ""
        "startTime": *************
        "endTime": *************
        "event": ""
        "resourceId": ""
        "accountIds": [ ]
        "pageNo": 1
        "pageSize": 10
        "innerCall": false
    validate:
        - eq: ["content.code", ********]
        - contains: ["content.message", "您不是该企业成员，请联系企业管理员加入企业。"]