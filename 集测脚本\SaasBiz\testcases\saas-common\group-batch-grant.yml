- config:
    name: 集团批量印章授权
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}

- test:
    name: 集团批量印章授权
    api: api/saas-common/group-batch-grant.yml
    variables:
      tenantId: 01c319f1c2764eaba361d42e59daa983
      operatorId: 77b2130de6c3423483d54e44012e3ee2
      "effectiveTime": *************
      "expireTime": *************
      "grantType": 2
      "grantedAccountIds": [ "cf5d9223a5c84a1698c4437bdc6890f6" ]
      "grantedResourceIds": [ "56bb1698-e2eb-4540-a9a9-3cd38255fc0e", "ed93b38f-e534-494c-9506-4da818116fd9" ]
      "roleKey": "SEAL_USER"
      "scopeList": [ "ALL" ]
      "fallType": 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.needRedirect", true]
