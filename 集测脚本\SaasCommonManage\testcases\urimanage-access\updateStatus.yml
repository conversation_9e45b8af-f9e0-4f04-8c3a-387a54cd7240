- config:
    name: 更新接口访问限制配置状态
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      uri1: "/testsuite/updateLimitStatus"


- test:
    skipIf: ${queryLimit($uri1)}
    name: 添加接口访问限制配置
    api: api/urimanage-access/addLimit.yml
    variables:
      bizDomain: ""
      desc: 集测单个修改访问限制状态
      limits: ["SKIP_LOGIN","BLACK_LIST","MEMBER_SKIP_CHECK"]
      owner: testsuite
      uri: $uri1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取指定接口访问限制配置
    api: api/urimanage-access/queryLimit.yml
    variables:
      uri: $uri1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - id1: content.data.uriLimit.id

- test:
    name: 更新接口访问限制配置状态
    api: api/urimanage-access/updateStatus.yml
    variables:
      id: $id1
      status: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
