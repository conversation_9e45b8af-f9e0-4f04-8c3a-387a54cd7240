 #新增印章授权（兼容二次授权）
    variables:
        appid: ${ENV(appid)}
    request:
        url: ${ENV(inner_open_url)}/v1/saas-common/rules-grant/seals/add-rule-grants
        method: POST
        headers: ${gen_headers($appid, X-Tsign-Open-Tenant-Id=$tenantid, X-Tsign-Open-Operator-Id=$operatorid)}
        json:
          {
            "appScheme": $appScheme,
#            "applyOid": $applyOid,
            "autoFall": $autoFall,
            "effectiveTime": $effectiveTime,
#            "expireReason": $expireReason,
            "expireTime": $expireTime,
            "fallType": $fallType,
#            "grantFileTemplateKey": $grantFileTemplateKey,
#            "grantFlowSendNotice": $grantFlowSendNotice,
            "grantRedirectUrl": $grantRedirectUrl,
#            "grantRoleCheck": $grantRoleCheck,
            "grantType": $grantType,
            "grantedAccountIds": $grantedAccountIds,
#            "grantedUser": $grantedUser,
            "grantedUserCode": $grantedUserCode,
            "grantedUserName": $grantedUserName,
            "grantedUserRole": $grantedUserRole,
#            "granter": $granter,
#            "granterCode": $granterCode,
#            "granterName": $granterName,
            "h5": $h5,
            "notifySetting": $notifySetting,
            "notifyUrl": $notifyUrl,
            "orgId": $orgId,
            "resourceId": $resourceId,
            "roleKey": $roleKey,
            "scopeList": $scopeList,
            "token": $token
          }
