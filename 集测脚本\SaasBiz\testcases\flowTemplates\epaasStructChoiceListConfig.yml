
- config:
    name: 实名控件可选内容
    variables:
        orgId1: 9c6b87f1e2004a37bb5a3a9220d6e103
        accountId1: 80ff3354fac947cdae1555fc4cc25c7f

- test:
    name: 实名控件可选内容---个人空间
    api: api/flowTemplates/epaasStructChoiceListConfig.yml
    variables:
        tenantId: 80ff3354fac947cdae1555fc4cc25c7f
        operatorId: 80ff3354fac947cdae1555fc4cc25c7f
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]
        - ne: ["content.data.structsConfig", null]

- test:
    name: 实名控件可选内容---企业空间
    api: api/flowTemplates/epaasStructChoiceListConfig.yml
    variables:
        tenantId: 9c6b87f1e2004a37bb5a3a9220d6e103
        operatorId: 80ff3354fac947cdae1555fc4cc25c7f
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]
        - ne: ["content.data.structsConfig", null]