- config:
    name: 合同备注相关场景
    variables:
      tenantId: 80ff3354fac947cdae1555fc4cc25c7f
      operatorId: 80ff3354fac947cdae1555fc4cc25c7f


- test:
    name: 创建合同比对记录
    api: api/approval/contract_analysis_create.yml
    variables:
      json: {"accountOid":"80ff3354fac947cdae1555fc4cc25c7f","compareFileHash":"6vNfFicBZgc1W0G1n3ZDNQ==","originFileId":"4fb36c2c517146dbb19262b3135c473c","compareFileId":"7f7fd3b86689435cad9ecfa1bb3fc63f","originFileName":"个人借贷合同.doc","originFileHash":"","approvalId":"AF-ada3bba20a7046548c5161cf3a260ab7","analysisBizType":"SEAL","compareFileName":"转授权证明书.docx","analysisBizId":"c14b39ad8f9543adaf222c73a7be781b","taskId":""}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查看比对详情
    api: api/approval/contract_analysis_detail.yml
    variables:
      compareId: 31f50fc92509481a8c8474158e3e576f
      analysisBizType: SEAL
      analysisBizId: c14b39ad8f9543adaf222c73a7be781b
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

#- test:
#    name: 查询比对文件url
#    api: api/approval/contract_analysis_file_url.yml
#    variables:
#      compareId: 31f50fc92509481a8c8474158e3e576f
#      analysisBizType: SEAL
#      analysisBizId: c14b39ad8f9543adaf222c73a7be781b
#    validate:
#      - eq: [ "content.code", 0 ]
#      - eq: [ "content.message", 成功 ]

- test:
    name: 分页查询合同比对结果
    api: api/approval/contract_analysis_list.yml
    variables:
      accountOid: $operatorId
      analysisBizType: SEAL
      analysisBizId: c2e9c6d624b54f50a25237c5d3187762
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]