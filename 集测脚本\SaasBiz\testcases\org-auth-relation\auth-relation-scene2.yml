- config:
    name: 关联组织其他场景
    variables:
      accountId1: 475955db49aa4289a8cb9422e200988c
      orgId1: 3c28ba6286b640a4b5480f4758584c54
      orgName1: esigntest集测多组织测试企业F
      orgId2: c4955b323a8447aab32c1aa06156ac2a
      orgName2: esigntest集测多组织测试企业G
      orgCode2: 9100000043123441A3
      fileKey1: ${ENV(fileKey1)}
      db_name1: saas_base_manage
      key1: "saas-biz:authRelationBatchAddTask:v2:ddf321698383429f88f84ad26804ceca"
      key2: "saas-biz:authRelationBatchAddDetail:v2:ddf321698383429f88f84ad26804ceca"

#场景一：撤回授权协议书流程
- test:
    name: 获取订单信息
    api: api/org-auth-relation/can-auth-infos.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.authInfoOrder.1.orderList.0.effectiveEndTime", "${getTimeStamp_ms()}"]
    extract:
      - orderId1: content.data.authInfoOrder.1.orderList.0.orderId
      - margin1: content.data.authInfoOrder.1.orderList.0.margin
    setup_hooks:
      - ${redis_val(key1)}
      - ${redis_val(key2)}

- test:
    name: 添加前获取添加条件
    api: api/org-auth-relation/auth-relation-config.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.batchAddExcelUrl", null]
      - gt: ["content.data.onlineMaxAddCount", 0]
    extract:
      - authReason1: content.data.authReasonList.0.value

- test:
    name: 添加关联企业前校验企业是否符合规则-F-G校验通过
    api: api/org-auth-relation/check-tenant-when-add.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      parentTenantOid: $orgId1
      childTenantList:
        [
        {
          "orderId": "$orderId1",
          "authResources":[
            "100"
          ],
          "shareConfigs":[

          ],
          "childTenantName":"$orgName2",
          "childUsccCode":"$orgCode2",
          "childTenantOid":"$orgId2"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.pass", true]

- test:
    name: 添加关联组织-F-G
    api: api/org-auth-relation/add-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authReason: $authReason1
      parentTenantOid: $orgId1
      childTenantList:
        [
        {
          "orderId": "$orderId1",
          "authResources":[
            "100"
          ],
          "shareConfigs":[

          ],
          "childTenantName":"$orgName2",
          "childUsccCode":"$orgCode2",
          "childTenantOid":"$orgId2"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data", null]

- test:
    name: 获取批量任务进度-有进行中的任务
    api: api/org-auth-relation/batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data", null]

#- test:
#    name: 执行批量添加授权任务
#    api: api/org-auth-relation/testBatchTask.yml
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content", success]

- test:
    name: 获取批量任务进度-任务完成发起成功
    api: api/org-auth-relation/batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      url1: "/v2/org-auth-relation/batch-add-task"
      params1:
        {

        }
      headers1:
        {
          "X-Tsign-Open-App-Id": "${ENV(bzq_appid)}",
          "X-Tsign-Service-Group": "${ENV(service_group)}",
          "X-Tsign-Open-Auth-Mode": simple,
          "X-Tsign-Open-Tenant-Id": $orgId1,
          "X-Tsign-Open-Operator-Id": $accountId1
        }
    setup_hooks:
      - ${waitTillDone2($url1,$params1,$headers1,5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]

- test:
    name: 处理删除过完成的任务
    api: api/org-auth-relation/delete-batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为待授权
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 1]
    extract:
      - authRelationId1: content.data.list.0.authRelationId

- test:
    name: 授权关系修改配置-开启签署流量共享
    api: api/org-auth-relation/share-config.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId1
      configKey: shareSign
      open: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为待授权
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: shareSign
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]

- test:
    name: 查询授权关系最新的签署信息
    api: api/org-auth-relation/get-auth-relation-last-process.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId1
      authRelationLogId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - processId1: content.data.processId
      - flowId1: content.data.flowId

- test:
    name: 撤回授权协议流程
    api: api/footstone/flow-revoke.yml
    variables:
      flowId: $flowId1
      operatorId: $accountId1
      revokeReason: 取消授权
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为失败
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    setup_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 4]

- test:
    name: 查询关联企业授权记录列表
    api: api/org-auth-relation/auth-relation-log-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      authRelationId: $authRelationId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - eq: ["content.data.list.0.authRelationStatus", 4]

- test:
    name: 获取订单信息-验证冻结的订单商品数退回
    api: api/org-auth-relation/can-auth-infos.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.authInfoOrder.1.orderList.0.margin", $margin1]

- test:
    name: 删除授权-删除F-G
    api: api/org-auth-relation/delete-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

#场景二：授权协议流程发起解约
- test:
    name: 添加关联企业前校验企业是否符合规则-F-G校验通过
    api: api/org-auth-relation/check-tenant-when-add.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      parentTenantOid: $orgId1
      childTenantList:
        [
        {
          "orderId": "$orderId1",
          "authResources":[
            "100"
          ],
          "shareConfigs":[

          ],
          "childTenantName":"$orgName2",
          "childUsccCode":"$orgCode2",
          "childTenantOid":"$orgId2"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.pass", true]

- test:
    name: 添加关联组织-F-G
    api: api/org-auth-relation/add-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authReason: $authReason1
      parentTenantOid: $orgId1
      childTenantList:
        [
        {
          "orderId": "$orderId1",
          "authResources":[
            "100"
          ],
          "shareConfigs":[

          ],
          "childTenantName":"$orgName2",
          "childUsccCode":"$orgCode2",
          "childTenantOid":"$orgId2"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data", null]

- test:
    name: 获取批量任务进度-有进行中的任务
    api: api/org-auth-relation/batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data", null]

#- test:
#    name: 执行批量添加授权任务
#    api: api/org-auth-relation/testBatchTask.yml
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content", success]

- test:
    name: 获取批量任务进度-任务完成发起成功
    api: api/org-auth-relation/batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      url1: "/v2/org-auth-relation/batch-add-task"
      params1:
        {

        }
      headers1:
        {
          "X-Tsign-Open-App-Id": "${ENV(bzq_appid)}",
          "X-Tsign-Service-Group": "${ENV(service_group)}",
          "X-Tsign-Open-Auth-Mode": simple,
          "X-Tsign-Open-Tenant-Id": $orgId1,
          "X-Tsign-Open-Operator-Id": $accountId1
        }
    setup_hooks:
      - ${waitTillDone2($url1,$params1,$headers1,5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]

- test:
    name: 处理删除过完成的任务
    api: api/org-auth-relation/delete-batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为待授权
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 1]
    extract:
      - authRelationId2: content.data.list.0.authRelationId

- test:
    name: 授权关系修改配置-开启会员版本共享
    api: api/org-auth-relation/share-config.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId1
      configKey: shareVip
      open: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为待授权
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: shareVip
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]

- test:
    name: 查询授权关系最新的签署信息
    api: api/org-auth-relation/get-auth-relation-last-process.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId2
      authRelationLogId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - processId2: content.data.processId
      - flowId2: content.data.flowId

- test:
    name: 查询签署流程详情
    api: api/footstone/flowDetail.yml
    variables:
      flowId: $flowId2
      authorizerIds: ${str_append($orgId1,$orgId2)}
      queryAccountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - signfields1: content.data.signDocs.0.signfields

- test:
    name: 查询签署可用印章列表-获取企业F的印章
    api: api/footstone/getSignSeals.yml
    variables:
      flowId: $flowId2
      accountId: $accountId1
      batchSign: false
      signerAccountId: $accountId1
      authorizerIds: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - sealId1: content.data.officialSeals.0.organSeals.0.sealId

- test:
    name: 查询签署可用印章列表-获取企业G的印章
    api: api/footstone/getSignSeals.yml
    variables:
      flowId: $flowId2
      accountId: $accountId1
      batchSign: false
      signerAccountId: $accountId1
      authorizerIds: $orgId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - sealId2: content.data.officialSeals.0.organSeals.0.sealId

- test:
    name: 发起验证码
    api: api/footstone/sendSms.yml
    variables:
      accountId: $accountId1
      flowIds: [$flowId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - bizId1: content.data.bizId

- test:
    name: 校验验证码认证
    api: api/footstone/checkSms.yml
    variables:
      accountId: $accountId1
      authCode: 123456
      bizId: $bizId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.passed", true]

- test:
    name: 提交签署
    api: api/footstone/add-Update-Execute.yml
    variables:
      flowId: $flowId2
      accountId: $accountId1
      addSignfields: []
      async: true
      signfieldIds: []
      signfieldId1: ${getValue($signfields1,authorizedAccountId,$orgId1,signfieldId)}
      posBean1: ${getValue($signfields1,authorizedAccountId,$orgId1,posBean)}
      signfieldId2: ${getValue($signfields1,authorizedAccountId,$orgId2,signfieldId)}
      posBean2: ${getValue($signfields1,authorizedAccountId,$orgId2,posBean)}
      updateSignfields:
        [
        {
          "signfieldId": $signfieldId1,
          "sealId": $sealId1,
          "signerOperatorAuthorizerId": $orgId1,
          "signerOperatorId": $accountId1,
          "authorizedAccountId": $orgId1,
          "crossEnterpriseSeal": false,
          "fieldType": 0,
          "posBean": $posBean1
        },
        {
          "signfieldId": $signfieldId2,
          "sealId": $sealId2,
          "signerOperatorAuthorizerId": $orgId2,
          "signerOperatorId": $accountId1,
          "authorizedAccountId": $orgId2,
          "crossEnterpriseSeal": false,
          "fieldType": 0,
          "posBean": $posBean2
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]

- test:
    name: 获取原流程待解约文档列表
    api: api/contract-manager/rescindFileList.yml
    variables:
      processId: $processId2
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId1: content.data.files.0.fileId

- test:
    name: 生成解约流程合同文件
    api: api/contract-manager/generateRescindFile.yml
    variables:
      processId: $processId2
      tenantId: $orgId1
      operatorId: $accountId1
      rescindFileIds: [$fileId1]
      rescindRemark: 解除关联关系
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - files1: content.data.files

- test:
    name: 解约流程发起页面原流程信息回填
    api: api/contract-manager/rescindBackfill.yml
    variables:
      processId: $processId2
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - participants1: content.data.participants

- test:
    name: 授权协议书流程发起解约
    api: api/contract/startProcess.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      businessType: 1
      ccs: []
      files: $files1
      flowTemplateId: null
      initiatorAccountId: $accountId1
      originProcessId: $processId2
      participants: $participants1
      rescindFileIds: [$fileId1]
      scene: 1
      taskName: "组织关联确认书-解除协议"
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - flowId3: content.data.flowId

- test:
    name: 查询签署流程详情
    api: api/footstone/flowDetail.yml
    variables:
      flowId: $flowId3
      authorizerIds: ${str_append($orgId1,$orgId2)}
      queryAccountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - signfields2: content.data.signDocs.0.signfields

- test:
    name: 发起验证码
    api: api/footstone/sendSms.yml
    variables:
      accountId: $accountId1
      flowIds: [$flowId3]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - bizId1: content.data.bizId

- test:
    name: 校验验证码认证
    api: api/footstone/checkSms.yml
    variables:
      accountId: $accountId1
      authCode: 123456
      bizId: $bizId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.passed", true]

- test:
    name: 提交签署
    api: api/footstone/add-Update-Execute.yml
    variables:
      flowId: $flowId3
      accountId: $accountId1
      addSignfields: []
      async: true
      signfieldIds: []
      signfieldId1: ${getValue($signfields2,authorizedAccountId,$orgId1,signfieldId)}
      posBean1: ${getValue($signfields2,authorizedAccountId,$orgId1,posBean)}
      signfieldId2: ${getValue($signfields2,authorizedAccountId,$orgId2,signfieldId)}
      posBean2: ${getValue($signfields2,authorizedAccountId,$orgId2,posBean)}
      updateSignfields:
        [
        {
          "signfieldId": $signfieldId1,
          "sealId": $sealId1,
          "signerOperatorAuthorizerId": $orgId1,
          "signerOperatorId": $accountId1,
          "authorizedAccountId": $orgId1,
          "crossEnterpriseSeal": false,
          "fieldType": 0,
          "posBean": $posBean1
        },
        {
          "signfieldId": $signfieldId2,
          "sealId": $sealId2,
          "signerOperatorAuthorizerId": $orgId2,
          "signerOperatorId": $accountId1,
          "authorizedAccountId": $orgId2,
          "crossEnterpriseSeal": false,
          "fieldType": 0,
          "posBean": $posBean2
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为已失效
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 3]

- test:
    name: 查询关联企业授权记录列表
    api: api/org-auth-relation/auth-relation-log-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      authRelationId: $authRelationId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - eq: ["content.data.list.0.authRelationStatus", 4]

- test:
    name: 获取订单信息-验证冻结的订单商品数退回
    api: api/org-auth-relation/can-auth-infos.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.authInfoOrder.1.orderList.0.margin", $margin1]

- test:
    name: 查询历史有效关联企业列表
    api: api/org-auth-relation/search-history-effective-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      searchTenantName: $orgName2
      bizScene: contractManage
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]

#场景三：续签后关联关系换绑授权记录
- test:
    name: 运营后台线下批量添加授权记录-F-G，授权资源选组织管理
    api: api/org-auth-relation/crm/add-auth-relation.yml
    variables:
      authReason: $authReason1
      authTenantOid: $orgId1
      childTenantList:
        [
        {
          "fileKey":"$fileKey1",
          "orderId":"$orderId1",
          "childTenantOid":"$orgId2",
          "childUsccCode":"$orgCode2",
          "childTenantName":"$orgName2",
          "authResources":[
            "100"
          ],
          "shareConfigs":[

          ]
        }
        ]
      operatorAlias: 明绣
      operatorName: 谢佳
      parentTenantOid: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.success", true]

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]
      - eq: ["content.data.list.0.authResources", ["100"]]
    extract:
      - authRelationId3: content.data.list.0.authRelationId

- test:
    name: 运营后台线下批量添加授权记录-F-G续签，授权资源选组织管理和企业合同
    api: api/org-auth-relation/crm/add-auth-relation.yml
    variables:
      authReason: $authReason1
      authTenantOid: $orgId1
      childTenantList:
        [
        {
          "fileKey":"$fileKey1",
          "orderId":"$orderId1",
          "childTenantOid":"$orgId2",
          "childUsccCode":"$orgCode2",
          "childTenantName":"$orgName2",
          "authResources":[
            "100",
            "200"
          ],
          "shareConfigs":[

          ]
        }
        ]
      operatorAlias: 明绣
      operatorName: 谢佳
      parentTenantOid: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.success", true]

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]
      - eq: ["content.data.list.0.authResources", ["100","200"]]

- test:
    name: 查询关联企业授权记录列表
    api: api/org-auth-relation/auth-relation-log-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      authRelationId: $authRelationId3
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]

- test:
    name: 主动解除授权-解除F-G
    api: api/org-auth-relation/rescind-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId3
      sql1: "SELECT id FROM saas_base_manage.auth_relation_log where auth_relation_id='$authRelationId3' order by id desc limit 1;"
      authRelationLogId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]
      - eq: ["content.data.list.0.authResources", ["100"]]

- test:
    name: 主动解除授权-再次解除F-G
    api: api/org-auth-relation/rescind-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId3
      authRelationLogId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 查询关联企业列表-F下查询下级组织G，状态为已失效
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 3]
