config:
  name: 信息采集-excel导入信息发起流程
  variables:
    orgId1: 08486d61823d4c2086122b26fb3e615a
    accountId1: 0bc26b27ede64cc48e7a800f4c5cbac7
    contentMd5_1: "t4I11ouVJSItveQUGNgz6w=="
    contentType_1: "application/octet-stream"
    pageNum_1: 1
    pageSize_1: 10
    pageSize_2: 100
    templateId_1: "c2be002881b0473a91eb1313b87e93cf"
    templateName_1: "人工审核单位实名认证授权委托书"
    queryType_1: 0
    filePath_1: "data/信息采集数据导入模版.xlsx"
    fileId_1: a96ea4bd4a1846b28ec3904d6afaaa69
    fileName_1: "人工审核单位实名认证授权委托书.pdf"
teststeps:
  - name: 解析信息登记表
    api: api/info_collect/form_analysis.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      json: {
        "formJson":"{\"config\":{\"customClass\":\"\",\"hideErrorMessage\":false,\"hideLabel\":false,\"labelPosition\":\"top\",\"labelWidth\":100,\"layout\":\"horizontal\",\"size\":\"default\",\"ui\":\"element\",\"width\":\"100%\",\"version\":\"2.1\",\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]},\"list\":[{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"33.33%\",\"widthType\":\"1/3\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"企业名称\",\"key\":\"p1ufgvhl\",\"model\":\"input_p1ufgvhl\",\"alias\":\"input_p1ufgvhl\",\"rules\":[],\"row\":0,\"col\":0},{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"33.33%\",\"widthType\":\"1/3\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"委托人姓名\",\"key\":\"l7slmses\",\"model\":\"input_l7slmses\",\"alias\":\"input_l7slmses\",\"rules\":[],\"row\":0,\"col\":1},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"33.33%\",\"widthType\":\"1/3\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"委托人手机号\",\"key\":\"zpeeo51s\",\"model\":\"cellphone_zpeeo51s\",\"alias\":\"cellphone_zpeeo51s\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":0,\"col\":2},{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"50%\",\"widthType\":\"1/2\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"被委托人姓名\",\"key\":\"9m2owx6o\",\"model\":\"input_9m2owx6o\",\"alias\":\"input_9m2owx6o\",\"rules\":[],\"row\":1,\"col\":0},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"50%\",\"widthType\":\"1/2\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"被委托人手机号\",\"key\":\"50pcr3os\",\"model\":\"cellphone_50pcr3os\",\"alias\":\"cellphone_50pcr3os\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":1,\"col\":1},{\"type\":\"date\",\"showNameText\":\"日期\",\"availableInTable\":true,\"options\":{\"customClass\":\"\",\"dataBind\":true,\"dateType\":\"date\",\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"editable\":true,\"eventConfigEnabled\":true,\"format\":\"yyyy-MM-dd\",\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"placeholder\":\"请选择\",\"placeholderText\":\"输入提示\",\"readonly\":false,\"required\":false,\"requiredMessage\":\"\",\"supportFileName\":true,\"timestamp\":true,\"tips\":\"\",\"type\":\"date\",\"validator\":\"\",\"validatorCheck\":false,\"validateFormatValue\":\"\",\"valueFormat\":\"yyyy-MM-dd HH:mm:ss\",\"width\":\"100%\",\"widthType\":\"1\",\"validateFormat\":[{\"value\":\"yyyy\",\"label\":\"年\"},{\"value\":\"yyyy-Q\",\"label\":\"年-季度\"},{\"value\":\"yyyy-MM\",\"label\":\"年-月\"},{\"value\":\"yyyy-MM-dd\",\"label\":\"年月日\"},{\"value\":\"yyyy-MM-dd HH:mm\",\"label\":\"年月日-时分\"},{\"value\":\"yyyy-MM-dd HH:mm:ss\",\"label\":\"年月日-时分秒\"}],\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"日期\",\"key\":\"i0crkwqn\",\"model\":\"date_i0crkwqn\",\"alias\":\"date_i0crkwqn\",\"rules\":[],\"row\":2,\"col\":0}],\"remoteComponents\":{\"matter-form_input\":\"https://asset.esign.cn/lowcode/test/components/matter-form/input/1.0.58/input.umd.js\",\"matter-form_cellphone\":\"https://asset.esign.cn/lowcode/test/components/matter-form/cellphone/1.0.59/cellphone.umd.js\",\"matter-form_date\":\"https://asset.esign.cn/lowcode/test/components/matter-form/date/1.0.58/date.umd.js\"}}"
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "status_code", 200 ]

  - name: 保存登记表
    api: api/info_collect/form_save.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formJson: "{\"config\":{\"customClass\":\"\",\"hideErrorMessage\":false,\"hideLabel\":false,\"labelPosition\":\"top\",\"labelWidth\":100,\"layout\":\"horizontal\",\"size\":\"default\",\"ui\":\"element\",\"width\":\"100%\",\"version\":\"2.1\",\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]},\"list\":[{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"33.33%\",\"widthType\":\"1/3\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"企业名称\",\"key\":\"p1ufgvhl\",\"model\":\"input_p1ufgvhl\",\"alias\":\"input_p1ufgvhl\",\"rules\":[],\"row\":0,\"col\":0},{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"33.33%\",\"widthType\":\"1/3\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"委托人姓名\",\"key\":\"l7slmses\",\"model\":\"input_l7slmses\",\"alias\":\"input_l7slmses\",\"rules\":[],\"row\":0,\"col\":1},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"33.33%\",\"widthType\":\"1/3\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"委托人手机号\",\"key\":\"zpeeo51s\",\"model\":\"cellphone_zpeeo51s\",\"alias\":\"cellphone_zpeeo51s\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":0,\"col\":2},{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"50%\",\"widthType\":\"1/2\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"被委托人姓名\",\"key\":\"9m2owx6o\",\"model\":\"input_9m2owx6o\",\"alias\":\"input_9m2owx6o\",\"rules\":[],\"row\":1,\"col\":0},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"50%\",\"widthType\":\"1/2\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"被委托人手机号\",\"key\":\"50pcr3os\",\"model\":\"cellphone_50pcr3os\",\"alias\":\"cellphone_50pcr3os\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":1,\"col\":1},{\"type\":\"date\",\"showNameText\":\"日期\",\"availableInTable\":true,\"options\":{\"customClass\":\"\",\"dataBind\":true,\"dateType\":\"date\",\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"editable\":true,\"eventConfigEnabled\":true,\"format\":\"yyyy-MM-dd\",\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"placeholder\":\"请选择\",\"placeholderText\":\"输入提示\",\"readonly\":false,\"required\":false,\"requiredMessage\":\"\",\"supportFileName\":true,\"timestamp\":true,\"tips\":\"\",\"type\":\"date\",\"validator\":\"\",\"validatorCheck\":false,\"validateFormatValue\":\"\",\"valueFormat\":\"yyyy-MM-dd HH:mm:ss\",\"width\":\"100%\",\"widthType\":\"1\",\"validateFormat\":[{\"value\":\"yyyy\",\"label\":\"年\"},{\"value\":\"yyyy-Q\",\"label\":\"年-季度\"},{\"value\":\"yyyy-MM\",\"label\":\"年-月\"},{\"value\":\"yyyy-MM-dd\",\"label\":\"年月日\"},{\"value\":\"yyyy-MM-dd HH:mm\",\"label\":\"年月日-时分\"},{\"value\":\"yyyy-MM-dd HH:mm:ss\",\"label\":\"年月日-时分秒\"}],\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"日期\",\"key\":\"i0crkwqn\",\"model\":\"date_i0crkwqn\",\"alias\":\"date_i0crkwqn\",\"rules\":[],\"row\":2,\"col\":0}],\"remoteComponents\":{\"matter-form_input\":\"https://asset.esign.cn/lowcode/test/components/matter-form/input/1.0.58/input.umd.js\",\"matter-form_cellphone\":\"https://asset.esign.cn/lowcode/test/components/matter-form/cellphone/1.0.59/cellphone.umd.js\",\"matter-form_date\":\"https://asset.esign.cn/lowcode/test/components/matter-form/date/1.0.58/date.umd.js\"}}"
      name: "测试信息采集"
      operations: [ {
        "code": "lc_eform_collection_list",
        "label": "登记信息表",
        "type": "SYSTEM",
        "key": "collection_list"
      }, {
        "code": "lc_eform_collection_add",
        "label": "新建登记表",
        "type": "SYSTEM",
        "key": "collection_add"
      }, {
        "code": "lc_eform_collection_task_add",
        "label": "新建采集任务",
        "type": "SYSTEM",
        "key": "collection_task_add"
      }, {
        "code": "lc_eform_collection_template_relation",
        "label": "关联业务模版",
        "type": "CUSTOM",
        "key": "collection_template_relation"
      }, {
        "code": "lc_eform_collection_edit",
        "label": "编辑登记表",
        "type": "SYSTEM",
        "key": "collection_edit"
      }, {
        "code": "lc_eform_collection_view",
        "label": "查看登记表",
        "type": "SYSTEM",
        "key": "collection_view"
      }, {
        "code": "lc_eform_collection_delete",
        "label": "删除登记表",
        "type": "SYSTEM",
        "key": "collection_delete"
      }, {
        "code": "lc_eform_collection_task_start_list",
        "label": "我发起的",
        "type": "SYSTEM",
        "key": "collection_task_start_list"
      }, {
        "code": "lc_eform_collection_task_manage_list",
        "label": "我管理的",
        "type": "SYSTEM",
        "key": "collection_task_manage_list"
      }, {
        "code": "lc_eform_collection_task_view",
        "label": "任务详情",
        "type": "SYSTEM",
        "key": "collection_task_view"
      }, {
        "code": "lc_eform_collection_task_enable",
        "label": "启用",
        "type": "SYSTEM",
        "key": "collection_task_enable"
      }, {
        "code": "lc_eform_collection_task_disable",
        "label": "停用",
        "type": "SYSTEM",
        "key": "collection_task_disable"
      }, {
        "code": "lc_eform_collection_task_delete",
        "label": "删除",
        "type": "SYSTEM",
        "key": "collection_task_delete"
      }, {
        "code": "lc_eform_collection_task_url",
        "label": "复制链接",
        "type": "SYSTEM",
        "key": "collection_task_url"
      }, {
        "code": "lc_eform_collection_data_start_list",
        "label": "我发起的",
        "type": "SYSTEM",
        "key": "collection_data_start_list"
      }, {
        "code": "lc_eform_collection_data_manage_list",
        "label": "我管理的",
        "type": "SYSTEM",
        "key": "collection_data_manage_list"
      }, {
        "code": "lc_eform_collection_data_sign",
        "label": "选择数据发起签署",
        "type": "SYSTEM",
        "key": "collection_data_sign"
      }, {
        "code": "lc_eform_collection_data_export",
        "label": "导出数据",
        "type": "SYSTEM",
        "key": "collection_data_export"
      }, {
        "code": "lc_eform_collection_data_download",
        "label": "下载",
        "type": "SYSTEM",
        "key": "collection_data_download"
      }, {
        "code": "lc_eform_collection_data_view",
        "label": "查看",
        "type": "SYSTEM",
        "key": "collection_data_view"
      }, {
        "code": "lc_eform_collection_data_approve",
        "label": "审核",
        "type": "SYSTEM",
        "key": "collection_data_approve"
      }, {
        "code": "lc_eform_collection_data_delete",
        "label": "删除",
        "type": "SYSTEM",
        "key": "collection_data_delete"
      }, {
        "code": "lc_eform_collection_data_edit",
        "label": "编辑",
        "type": "SYSTEM",
        "key": "collection_data_edit"
      } ]
      pageConfig: {
        "fields": [ {
          "fieldName": "id",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "data_id_",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": false,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": true,
          "fieldType": "input",
          "order": 0
        }, {
          "fieldName": "企业名称",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "input_p1ufgvhl",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": true,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": true,
          "fieldType": "input",
          "order": 1
        }, {
          "fieldName": "委托人姓名",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "input_l7slmses",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": true,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": true,
          "fieldType": "input",
          "order": 2
        }, {
          "fieldName": "委托人手机号",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "cellphone_zpeeo51s",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": true,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": true,
          "fieldType": "cellphone",
          "order": 3
        }, {
          "fieldName": "被委托人姓名",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "input_9m2owx6o",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": true,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": true,
          "fieldType": "input",
          "order": 4
        }, {
          "fieldName": "被委托人手机号",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "cellphone_50pcr3os",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": true,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": true,
          "fieldType": "cellphone",
          "order": 5
        }, {
          "fieldName": "日期",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "date_i0crkwqn",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": true,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": true,
          "fieldType": "date",
          "order": 6
        }, {
          "fieldName": "状态",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "status_",
          "multiFuzzySelect": false,
          "isSelect": true,
          "isShow": true,
          "detailPageShow": false,
          "multiFuzzySelectEnable": false,
          "options": [ {
            "label": "待审核",
            "value": "WAIT_AUDIT"
          }, {
            "label": "审核通过",
            "value": "AUDIT_PASS"
          }, {
            "label": "审核驳回",
            "value": "AUDIT_REJECT"
          }, {
            "label": "无需审核",
            "value": "UN_AUDIT"
          } ],
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": true,
          "fieldType": "select",
          "order": 7
        }, {
          "fieldName": "创建人",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "creator_",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": false,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": false,
          "fieldType": "user",
          "order": 8
        }, {
          "fieldName": "修改人",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "modifier_",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": false,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": false,
          "fieldType": "user",
          "order": 9
        }, {
          "fieldName": "创建时间",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "create_time_",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": false,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": true,
          "fieldType": "date",
          "order": 10
        }, {
          "fieldName": "更新时间",
          "childFields": [ ],
          "editPageStatus": 1,
          "fieldKey": "modify_time_",
          "multiFuzzySelect": false,
          "isSelect": false,
          "isShow": true,
          "detailPageShow": false,
          "multiFuzzySelectEnable": false,
          "detailPageStatus": 2,
          "listPageShow": true,
          "selectEnable": true,
          "fieldType": "date",
          "order": 11
        } ]
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "status_code", 200 ]
    extract:
      formId_1: content.data.formId

  - name: 获取登记表列表
    api: api/info_collect/form_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: $pageNum_1
      pageSize: $pageSize_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "status_code", 200 ]
    extract:
      - creatorOid_1: content.data.list.0.creatorOid

  - name: 获取登记表字段
    api: api/info_collect/form_fields.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "status_code", 200 ]

  - name: 获取关联模板的参与方信息
    api: api/info_collect/template_participant.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      templateId: $templateId_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "status_code", 200 ]
    extract:
      - participantId_1: content.data.participants.0.participantId
      - participantId_2: content.data.participants.1.participantId

  - name: 保存模板和登记表的关联关系
    api: api/info_collect/form_temp_relation_creat.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
      templateName: $templateName_1
      templateId: $templateId_1
      participantId1: $participantId_1
      participantId2: $participantId_2
      fileId: $fileId_1
      json: {
        "formId": $formId,
        "templates": [ {
          "templateId": $templateId,
          "templateName": $templateName,
          "fileStructMappingList": [ {
            "fileId": $fileId,
            "fieldStructMapping": [ {
              "field": "input_p1ufgvhl",
              "structId": "ebdcb58ecc184a28b7a241214da88623",
              "structType": 1
            }, {
              "field": "input_l7slmses",
              "structId": "3aebe859456048ad81aa51c62716620e",
              "structType": 1
            }, {
              "field": "cellphone_zpeeo51s",
              "structId": "afff3e73738446bc86caccbf25cbe4da",
              "structType": 1
            }, {
              "field": "input_9m2owx6o",
              "structId": "8732dafe0f83431284cdce1511de8d6b",
              "structType": 1
            }, {
              "field": "cellphone_50pcr3os",
              "structId": "3ef563430bc849b9a2b46f945deba86a",
              "structType": 1
            }, {
              "field": "date_i0crkwqn",
              "structId": "cc23074a179e46f7b5caa7673d465e85",
              "structType": 3
            } ]
          } ]
        } ],
        "templateId": $templateId_1,
        "participants": [ {
          "participantId": $participantId1,
          "relation": [ {
            "relationElement": "name",
            "formControlId": "input_l7slmses"
          }, {
            "relationElement": "contract",
            "formControlId": "cellphone_zpeeo51s"
          }, {
            "relationElement": "subjectName",
            "formControlId": "input_p1ufgvhl"
          } ]
        }, {
          "participantId": $participantId2,
          "relation": [ {
            "relationElement": "name",
            "formControlId": "input_9m2owx6o"
          }, {
            "relationElement": "contract",
            "formControlId": "cellphone_50pcr3os"
          } ]
        } ]
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "content.data.success", true ]

  - name: 创建采集任务
    api: api/info_collect/collect_task_create.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      initiatorOid: $creatorOid_1
      formId: $formId_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "status_code", 200 ]
    extract:
      - infoCollectTaskId_1: content.data.infoCollectTaskId

  - name: 获取信息库列表
    api: api/info_collect/library_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: $pageNum_1
      pageSize: $pageSize_2
      queryType: $queryType_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "status_code", 200 ]

  - name: 下载信息采集模板
    api: api/info_collect/download_template.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "status_code", 200 ]

  - name: 获取文档fileKey直传地址
    api: api/info_collect/getFileKeyUploadUrl.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      contentMd5: $contentMd5_1
      contentType: $contentType_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "status_code", 200 ]
    extract:
      - fileKey_1: content.data.fileKey
      - uploadUrl_1: content.data.uploadUrl

  - name: 上传文件到oss
    api: api/info_collect/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl_1
      filePath: $filePath_1
      contentType: $contentType_1
      contentMd5: $contentMd5_1
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]

  - name: 信息采集导入数据
    api: api/info_collect/import_data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fileKey: $fileKey_1
      infoCollectTaskId: $infoCollectTaskId_1
      formId: $formId_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "content.data", null ]
    teardown_hooks:
      - ${hook_sleep_n_secs(10)}

  - name: 获取采集数据列表
    api: api/info_collect/data_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
      pageNum: $pageNum_1
      pageSize: $pageSize_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "status_code", 200 ]
    extract:
      - infoCollectRecordOuterId_1: content.data.list.0.infoCollectRecordOuterId
      - infoCollectTaskOuterId_1: content.data.list.0.infoCollectTaskOuterId
  - name: 批量审核采集数据
    api: api/info_collect/data_batch_approval.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
      reason: "批量审核通过"
      infoCollectTaskId: $infoCollectTaskId_1
      infoCollectRecordOuterId: $infoCollectRecordOuterId_1
      json: {
        "pass": true,
        "reason": $reason,
        "formId": $formId,
        "approvalData": [ {
          "infoCollectTaskId": $infoCollectTaskId,
          "infoCollectRecordOuterId": $infoCollectRecordOuterId
        } ]
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "content.data", null ]

  - name: 获取模板和表单关联关系
    api: api/info_collect/form_relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "status_code", 200 ]

  - name: 发起前查看发起方数据
    api: api/info_collect/participant_data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
      templateId: $templateId_1
      infoCollectTaskId: $infoCollectTaskId_1
      infoCollectRecordOuterId: $infoCollectRecordOuterId_1
      infoCollectTaskOuterId: $infoCollectTaskOuterId_1
      json: {
        "formId": $formId,
        "templateId": $templateId,
        "recordData": [ {
          "infoCollectRecordOuterId": $infoCollectRecordOuterId,
          "infoCollectTaskOuterId": $infoCollectTaskOuterId
        } ]
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "status_code", 200 ]

  - name: 信息采集发起
    api: api/info_collect/info_collect_start.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
      templateId: $templateId_1
      infoCollectTaskId: $infoCollectTaskId_1
      infoCollectRecordOuterId: $infoCollectRecordOuterId_1
      infoCollectTaskOuterId: $infoCollectTaskOuterId_1
      json: {
        "formId": $formId,
        "templateId": $templateId,
        "recordList": [ {
          "infoCollectRecordOuterId": $infoCollectRecordOuterId,
          "infoCollectTaskOuterId": $infoCollectTaskOuterId
        } ]
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "status_code", 200 ]
    extract:
      - requireId_1: content.data.requireId

  - name: 获取签署方信息excel文件fileKey
    api: api/info_collect/getExcelFileKey.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      requireId: $requireId_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "status_code", 200 ]
    extract:
      - fileKey_2: content.data.fileKey

  - name: 解析批量导入表格
    api: api/info_collect/excel_parse.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $templateId_1
      fileKey: $fileKey_2
      json: {
        "flowTemplateId": $flowTemplateId,
        "participantSubjectType": -1,
        "participantLabel": null,
        "excelTemplateType": 1,
        "fileKey": $fileKey
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "status_code", 200 ]

  - name: 信息采集发起
    api: api/info_collect/processes_start.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $templateId_1
      fileKey: $fileKey_2
      participantId1: $participantId_1
      participantId2: $participantId_2
      time_stamp: ${getTimeStamp_ms()}
      fileId: $fileId_1
      fileName: $fileName_1
      json: {
        "signMode": "NORMAL",
        "approveTemplateId": null,
        "refFlowTemplateId": null,
        "flowTemplateId": $flowTemplateId,
        "scene": 2,
        "ccs": [ ],
        "files": [ {
          "fileId": $fileId,
          "fileType": 1,
          "fileKey": $fileKey,
          "fileName": $fileName,
          "from": 2,
          "fileSecret": false,
          "supportReplace": false,
          "originalFileId": "",
          "order": 1,
          "contractNoType": 1
        } ],
        "participants": [ {
          "participantSubjectType": 1,
          "role": "1,3",
          "sealType": null,
          "signRequirements": "1",
          "roleSet": 1,
          "fillOrder": 1,
          "signOrder": 1,
          "participantLabel": "签署方1",
          "participantId": $participantId1,
          "instances": [ {
            "account": "***********",
            "accountOid": "0bc26b27ede64cc48e7a800f4c5cbac7",
            "accountNick": "",
            "accountName": "测试吴四",
            "accountRealName": true,
            "comment": "",
            "subjectId": null,
            "subjectName": "esigntest自动化测试企业11测试吴四",
            "subjectRealName": false,
            "subjectType": 1,
            "preFillValues": {
              "8732dafe0f83431284cdce1511de8d6b": "测试吴四",
              "ebdcb58ecc184a28b7a241214da88623": "esigntest自动化测试企业11测试吴四",
              "3aebe859456048ad81aa51c62716620e": "测试吴四",
              "afff3e73738446bc86caccbf25cbe4da": "***********",
              "cc23074a179e46f7b5caa7673d465e85": "2023-12-26",
              "3ef563430bc849b9a2b46f945deba86a": "***********"
            },
            "fileValues": null,
            "subTaskName": "",
            "subTaskBizId": "1739977067402104832",
            "subTaskBizType": 1,
            "ccs": [ ]
          } ],
          "willTypes": [ ],
          "type": 1,
          "signSealType": 1,
          "signSeal": "",
          "forceReadEnd": false,
          "forceReadTime": "",
          "attachmentConfigs": null,
          "participantMode": "",
          "signTipsTitle": null,
          "signTipsContent": null,
          "authWay": "REAL_NAME_AUTH",
          "accessToken": null
        }, {
          "participantSubjectType": 0,
          "role": "3",
          "sealType": "0,1,2",
          "signRequirements": null,
          "roleSet": 1,
          "fillOrder": 2,
          "signOrder": 1,
          "participantLabel": "签署方2",
          "participantId": $participantId2,
          "instances": [ {
            "account": "***********",
            "accountOid": "0bc26b27ede64cc48e7a800f4c5cbac7",
            "accountNick": "",
            "accountName": "测试吴四",
            "accountRealName": true,
            "comment": "",
            "subjectId": "0bc26b27ede64cc48e7a800f4c5cbac7",
            "subjectName": "测试吴四",
            "subjectRealName": true,
            "subjectType": 0,
            "preFillValues": {
              "8732dafe0f83431284cdce1511de8d6b": "测试吴四",
              "ebdcb58ecc184a28b7a241214da88623": "esigntest自动化测试企业11测试吴四",
              "3aebe859456048ad81aa51c62716620e": "测试吴四",
              "afff3e73738446bc86caccbf25cbe4da": "***********",
              "cc23074a179e46f7b5caa7673d465e85": "2023-12-26",
              "3ef563430bc849b9a2b46f945deba86a": "***********"
            },
            "fileValues": null,
            "subTaskName": "",
            "subTaskBizId": "1739977067402104832",
            "subTaskBizType": 1,
            "ccs": [ ]
          } ],
          "willTypes": [ ],
          "type": 1,
          "signSealType": "",
          "signSeal": "",
          "forceReadEnd": false,
          "forceReadTime": "",
          "attachmentConfigs": null,
          "participantMode": "",
          "signTipsTitle": null,
          "signTipsContent": null,
          "authWay": "REAL_NAME_AUTH",
          "accessToken": null
        } ],
        "initiatorAccountId": $accountId1,
        "taskName": 人工审核+$time_stamp,
        "signEndTime": null,
        "fileEndTime": null,
        "signValidityConfig": {
          "validityType": 3,
          "durationYear": null,
          "durationMonth": null,
          "durationDay": null
        },
        "fileValidityConfig": {
          "validityType": 3,
          "durationYear": null,
          "durationMonth": null,
          "durationDay": null
        },
        "platform": 5,
        "redirectUrl": "https://testfront.tsign.cn:8887/process/contract/start/-/10/1",
        "skipFill": false,
        "previewType": 0,
        "visibleAccounts": null,
        "supportAddDoc": false,
        "secretType": 1,
        "requestNo": null,
        "repeatSign": false,
        "initiatorDeptId": "",
        "useWatermarkTemplateId": "",
        "subjectCcNum": 0,
        "personCcNum": 0
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "status_code", 200 ]
    extract:
      - processId_1: content.data.processId

#  - name: 合同撤回
#    api: api/info_collect/batchRevoke.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId1
#      processId: $processId_1
#      subjectId: $orgId1
#      json: {
#        "accountId":"sys_accountId",
#        "processIds":[$processId],
#        "subjectId":$subjectId,
#        "revokeReason":"1"
#      }
#    validate:
#      - eq: [ "content.code", 0 ]
#      - eq: [ "content.message", 成功 ]
#      - eq: [ "content.data", null ]


  - name: 删除采集任务
    api: api/info_collect/collect_task_delete.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      infoCollectTaskId: $infoCollectTaskId_1
      json: {
        "infoCollectTaskId": $infoCollectTaskId
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "content.data", null ]

  - name: 删除采集信息库
    api: api/info_collect/collect_library_delete.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
      json: {
        "formId": $formId
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "content.data", null ]

  - name: 删除登记表
    api: api/info_collect/form_delete.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "content.data", null ]

