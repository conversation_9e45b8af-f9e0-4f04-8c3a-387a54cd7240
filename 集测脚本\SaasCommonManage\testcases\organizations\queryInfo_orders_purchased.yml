- config:
    name: 根据orgId查询企业当前用户所有已支付的订单列表
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      orgId1: "1c3216ca94a24773ba3538a7b4186046"  # 余量不足百分之30的企业
      gid1: "4eb242d0dcb345e4b622c31463d66f9f"
      orgId2: "39a75ce7129a4fabb20453f6bbe35e37" # 余量不足百分之5的企业
      gid2: "45f34572e22d4d85a498a279ebbbadea"
      orgId3: "dedd095f0e96419ea6f10a7ea0e95de3"  # 余量0的企业
      gid3: "c93158bb95a64aa09c449ced0b0a4a91"
      orgId4: "e8db242fb2d74153b3895dd5eb795a9e"  # 余量+套餐超期未续购的企业
      gid4: "847e0239c7f44ddc822b2d5394c2195d"
      operatorId: "276b5a8f2a964142bb1ebcf0dd82bf72"  # 明绣 <EMAIL>


- test:
    name: 根据orgId查询企业当前用户所有已支付的订单列表-orgId不存在
    api: api/organizations/queryInfo_orders_purchased.yml
    variables:
      orgId: 123
      operatorId: ${ENV(accountId1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 根据orgId查询企业当前用户所有已支付的订单列表-成功
    api: api/organizations/queryInfo_orders_purchased.yml
    variables:
      orgId: ${ENV(orgId1)}
      operatorId: ${ENV(accountId1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data", null]
