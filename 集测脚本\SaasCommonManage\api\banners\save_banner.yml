name: banner新增/更新
variables:
  bannerName: "测试"
  fileKey: ${ENV(fileKey1)}
  guideText: ""
  linkUrl: "https://www.esign.cn"
  notifyVersions: []
  orderNo: 0
  validityEndTime: ""
  validityStartTime: ""
  hasSatisfy: false

request:
  url: ${ENV(saas_common_manage_url)}/v1/saas-common/banners/save
  method: POST
  headers:
    x-timevale-jwtcontent: eyJhbGlhcyI6Iuemu-atjCIsImlkIjoibGlnZSJ9
    Content-Type: application/json
  json:
    {
      "areaCode": $areaCode,
      "bannerId": $bannerId,
      "bannerName": $bannerName,
      "clientVersionCode": $clientVersionCode,
      "conditionType": $conditionType1,
      "fileKey": $fileKey,
      "guideText": $guideText,
      "linkUrl": $linkUrl,
      "noticeType": 0,
      "notifyRoles": $notifyRoles,
      "notifyVersions": $notifyVersions,
      "orderNo": $orderNo,
      "productCode": $productCode,
      "title": "",
      "validityEndTime": $validityEndTime,
      "validityStartTime": $validityStartTime,
      "validityUserConfig": [
      {
        "conditionType": $conditionType,
        "configMap": $configMap,
        "hasSatisfy": $hasSatisfy,
        "type": $type
      }
      ]
    }
