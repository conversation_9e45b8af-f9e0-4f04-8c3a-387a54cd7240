- config:
    name: 验证手机验证码
#    base_url: ${ENV(base_url)}
    variables:
      accountId1: ${ENV(accountId1)}


- test:
    name: 验证手机验证码-authCodeId为空
    api: api/phone-code/authCode.yml
    variables:
      operatorId: $accountId1
      authCodeId: ""
      code: 123456
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "must not be blank"]

- test:
    name: 验证手机验证码-code为空
    api: api/phone-code/authCode.yml
    variables:
      operatorId: $accountId1
      authCodeId: 123
      code: ""
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "must not be blank"]
