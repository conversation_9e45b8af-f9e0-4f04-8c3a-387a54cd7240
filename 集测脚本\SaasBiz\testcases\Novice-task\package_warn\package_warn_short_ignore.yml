- config:
    name: 7天内不在提醒
#    base_url: ${ENV(contract_manager_url)}
    variables:
        orgId1: "1c3216ca94a24773ba3538a7b4186046"  # 余量不足百分之30的企业
        gid1: "4eb242d0dcb345e4b622c31463d66f9f"
        orgId2: "39a75ce7129a4fabb20453f6bbe35e37" # 余量不足百分之5的企业
        gid2: "45f34572e22d4d85a498a279ebbbadea"
        orgId3: "dedd095f0e96419ea6f10a7ea0e95de3"  # 余量0的企业
        gid3: "c93158bb95a64aa09c449ced0b0a4a91"
        orgId4: "e8db242fb2d74153b3895dd5eb795a9e"  # 余量+套餐超期未续购的企业
        gid4: "847e0239c7f44ddc822b2d5394c2195d"
        operatorId1: "276b5a8f2a964142bb1ebcf0dd82bf72"  # 忽略7天内不在弹窗的弹窗 明绣 <EMAIL>
        operatorId2: "4284de3d0c0d442faecc914401769664" # 忽略不再提醒进行弹窗
        operatorId3: "e668c04d3f534441bbaaa3d8ee5673fa" # 7天内不弹窗
        operatorId4: "fa540af3f3d24adaad2c5504f78df711" # 不再进行弹窗


- test:
    name: 7天内不在提醒-orgId为空
    variables:
        operatorId: $operatorId3
        orgId: ""
        accountId: $operatorId3
        client: "DING_TALK"
        sourceCode: "DING_TALK_PC"
    api: api/Novice-task/package_warn/package_warn_short_ignore.yml
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "参数错误: orgId不能为空"]
      - eq: ["content.data", null]

- test:
    name: 7天内不在提醒-accountId为空
    variables:
        operatorId: ""
        orgId: $orgId2
        accountId: ""
        client: "DING_TALK"
        sourceCode: "DING_TALK_PC"
    api: api/Novice-task/package_warn/package_warn_short_ignore.yml
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "参数错误: accountId不能为空"]
      - eq: ["content.data", null]

- test:
    name: 7天内不在提醒-sourceCode为空
    variables:
        operatorId: $operatorId3
        orgId: $orgId2
        accountId: $operatorId3
        client: "DING_TALK"
        sourceCode: ""
    api: api/Novice-task/package_warn/package_warn_short_ignore.yml
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "参数错误: sourceCode不能为空"]
      - eq: ["content.data", null]


- test:
    name: 7天内不在提醒-标准签-成功
    variables:
        operatorId: $operatorId4
        orgId: $orgId2
        accountId: $operatorId4
        client: ""
        sourceCode: "WEB"
    api: api/Novice-task/package_warn/package_warn_short_ignore.yml
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]

- test:
    name: 7天内不在提醒-钉签PC-成功
    variables:
        operatorId: $operatorId4
        orgId: $orgId2
        accountId: $operatorId4
        client: "DING_TALK"
        sourceCode: "DING_TALK_PC"
    api: api/Novice-task/package_warn/package_warn_short_ignore.yml
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]

- test:
    name: 7天内不在提醒-钉签移动-成功
    variables:
        operatorId: $operatorId4
        orgId: $orgId2
        accountId: $operatorId4
        client: "DING_TALK"
        sourceCode: "DING_TALK_MOBILE"
    api: api/Novice-task/package_warn/package_warn_short_ignore.yml
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]
