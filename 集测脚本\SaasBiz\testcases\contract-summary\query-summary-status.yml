- config:
    name: 获取合同内所有文件摘要是否运行完毕


- test:
    name: 获取合同内所有文件摘要是否运行完毕-成功
    api: api/contract-summary/query-summary-status.yml
    variables:
      "processId": "53073a756cd9499bb7c9dc1ee1740236"
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]

- test:
    name: 获取合同内所有文件摘要是否运行完毕-流程id为空
    api: api/contract-summary/query-summary-status.yml
    variables:
      "processId": ""
    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: 合同流程id不能为空"]

