- config:
    name: 添加流程模板到分类下
    variables:
      orgId1: ${ENV(orgId1)}                   #orgId2的子企业，基础版以上
      orgId2: ${ENV(orgId2)}                   #基础版以上的企业
      accountId1: ${ENV(accountId1)}           #orgId1、orgId2、orgId3的管理员
      categoryId1: ${ENV(categoryId1)}         #orgId2下的模板分类
      flowTemplateId1: ${ENV(flowTemplateId1)} #orgId2下的流程模板
      flowTemplateId2: ${ENV(flowTemplateId2)} #orgId2下的流程模板，且授权给了orgId1
      accountId2: ${ENV(accountId2)}           #orgId2下的普通成员
      accountId3: ${ENV(accountId3)}           #不是orgId2下的成员
      roleId1: ${ENV(roleId1)}                 #orgId2企业下编辑模板权限的角色id
      orgId3: ${ENV(orgId3)}                   #基础版的企业
      categoryId2: ${ENV(categoryId2)}         #orgId3下的模板分类
      flowTemplateId3: ${ENV(flowTemplateId3)} #orgId3下的流程模板
      db_name1: contract_manager


- test:
    name: 添加流程模板到分类下-flowTemplateId为空
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      flowTemplateId: ""
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", flowTemplateId不能为空]

- test:
    name: 添加流程模板到分类下-flowTemplateId不存在
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      flowTemplateId: 123
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 流程模板不存在]

- test:
    name: 添加流程模板到分类下-flowTemplateId不属于当前企业
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId3
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
#      - contains: ["content.message", 流程模板拥有者不匹配]

- test:
    name: 添加流程模板到分类下-categoryId为空
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
      categoryId: ""
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类id不能为空]

- test:
    name: 添加流程模板到分类下-categoryId不存在
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
      categoryId: 123
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类不存在]

- test:
    name: 添加流程模板到分类下-categoryId不属于当前企业
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
      categoryId: $categoryId2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类不存在]

- test:
    name: 添加流程模板到分类下-操作人不是企业成员
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId3
      flowTemplateId: $flowTemplateId1
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", "企业成员不存在"]

#- test:
#    name: 添加流程模板到分类下-操作人无该模板的编辑权限
#    api: api/template-category/add-to-category.yml
#    variables:
#      tenantId: $orgId2
#      operatorId: $accountId2
#      flowTemplateId: $flowTemplateId1
#      categoryId: $categoryId1
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", *********]
#      - contains: ["content.message", "您没有模板编辑权限"]

- test:
    name: 根据分组获取可操作的角色列表
    api: api/role/getRoleList.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      group: TEMP_GROUP
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - roleList: content.data     #企业下模板授权的roleList

- test:
    name: 流程模板批量授权-给accountId2设置模板的编辑权限
    api: api/template-manage/batchAuth.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      roleId2: ${getValue($roleList, roleKey, TEMP_USE, id)}  #企业下可使用的roleId
      roleId3: ${getValue($roleList, roleKey, TEMP_UPDATE, id)}  #企业下可编辑的roleId
      flowTemplateAuthList:
        [
        {
          "authList":[
          {
            "authId":"ALL",
            "accountOid":"",
            "roleId":"$roleId2",
            "roleKey":"TEMP_USE",
            "type":1
          },
          {
            "authId":null,
            "accountOid":"$accountId2",
            "roleId":"$roleId3",
            "roleKey":"TEMP_UPDATE",
            "type":2
          }
          ],
          "flowTemplateId":"$flowTemplateId1"
        }
        ]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

#- test:
#    name: 添加流程模板到分类下-操作人有该模板的编辑权限
#    api: api/template-category/add-to-category.yml
#    variables:
#      tenantId: $orgId2
#      operatorId: $accountId2
#      flowTemplateId: $flowTemplateId1
#      categoryId: $categoryId1
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]

- test:
    name: 流程模板批量授权-取消accountId2的模板编辑权限
    api: api/template-manage/batchAuth.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      roleId2: ${getValue($roleList, roleKey, TEMP_USE, id)}  #企业下可使用的roleId
      flowTemplateAuthList:
        [
        {
          "authList":[
          {
            "authId":"ALL",
            "accountOid":"",
            "roleId":"$roleId2",
            "roleKey":"TEMP_USE",
            "type":1
          }
          ],
          "flowTemplateId":"$flowTemplateId1"
        }
        ]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-给accountId2设置全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId2
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 添加流程模板到分类下-操作人有全局模板编辑权限
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId2
      flowTemplateId: $flowTemplateId1
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-取消accountId2的全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId2
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: []
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 添加流程模板到分类下-管理员默认有权限
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 添加流程模板到分类下-基础版企业不支持
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId3
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId2
      categoryId: $categoryId2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 版本功能不支持]

- test:
    name: 流程模板列表-子企业下主企业授权的模板不展示主企业设置的分类
    api: api/template-manage/flowTemplateList.yml
    variables:
      sql1: "SELECT flow_template_name FROM doc_cooperation.flow_template where flow_template_id='$flowTemplateId2';"
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      status: ""
      flowTemplateName: ${select_sql($sql1, $db_name1)}
      queryLabel: true
      label: ""
      containShared: true
      excludeLabels: ""
      queryUse: false
      containsDynamic: true
      categoryId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.categories", []]
