- config:
    name: 编辑接口访问限制配置
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      accountId: ${ENV(accountId1)}


- test:
    name: 编辑接口访问限制配置-成功
    api: api/port_accessright/port_compile.yml
    variables:
      id: 598
      bizDomain: ""
      limits: ["MEMBER_SKIP_CHECK", "WHITE_LIST", "NEED_LOGIN"]
      uri: "url=http://saas-common-manage-contractsign-********.projectk8s.tsign.cn"
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 当前接口访问限制配置不存在]
