- config:
    name: 信息采集任务的结果页

- test:
    name: 表单关联了仅签的静态模板-企业主体-无法发起
    api: api/info-collect/task_result.yml
    variables:
        appId: ${ENV(appid)}
        tenantId: "c76c2f6f136d4997aaccf30f1f767f59"
        operatorId: "565a742760cc485185bbd3cfc1e47e80"
        json:
          {
            "taskId": null,
            "taskKey": "task933762033826369536",
            "dataId": "933762120462209024"
          }
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]
        - eq: ["content.data.taskDataStatus.formName", "勿删！集测采集任务001"]


- test:
    name: 表单关联了带填写的模板
    api: api/info-collect/task_result.yml
    variables:
        appId: ${ENV(appid)}
        tenantId: "c76c2f6f136d4997aaccf30f1f767f59"
        operatorId: "565a742760cc485185bbd3cfc1e47e80"
        json:
          {
            "taskId": null,
            "taskKey": "task933765420571660288",
            "dataId": "933765506645463040"
          }
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]
        - eq: ["content.data.taskDataStatus.formName", "勿删！集测采集002"]

- test:
    name: 表单关联了仅签的模板-签署主体是个人-有发起按钮
    api: api/info-collect/task_result.yml
    variables:
        appId: ${ENV(appid)}
        tenantId: "c76c2f6f136d4997aaccf30f1f767f59"
        operatorId: "565a742760cc485185bbd3cfc1e47e80"
        json:
          {
              "taskId": null,
              "taskKey": "task944665880117125120",
              "dataId": "944665987940098048",
              "platform": 5
          }
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.data.btnList.0.name", "去签署"]
        - eq: ["content.data.taskDataStatus.formName", "勿删！集测的仅签采集-顺序签"]