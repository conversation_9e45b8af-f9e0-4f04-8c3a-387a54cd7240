- config:
    name: 查询流程模板分类列表
    variables:
      orgId1: ${ENV(orgId2)}                   #基础版以上的企业
      accountId1: ${ENV(accountId1)}           #orgId1和orgId2的管理员
      accountId2: ${ENV(accountId2)}           #orgId1下的普通成员
      accountId3: ${ENV(accountId3)}           #不是orgId1下的成员
      orgId2: ${ENV(orgId3)}                   #基础版的企业


- test:
    name: 查询流程模板分类列表-操作人不是企业成员
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId3
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", ********]
      - contains: ["content.message", 您不是该企业成员]

- test:
    name: 查询流程模板分类列表-基础版的企业
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询流程模板分类列表-企业下的普通员工
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
