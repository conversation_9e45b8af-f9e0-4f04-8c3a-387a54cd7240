- config:
    name: 获取模版关联数据源异常信息
    variables:
      appId: ${ENV(appid)}
      tenantId: 08486d61823d4c2086122b26fb3e615a
      operatorId: 0bc26b27ede64cc48e7a800f4c5cbac7

- test:
    name: 获取企业下的数据源异常
    api: api/dataSource/getDataSourceError.yml
    variables:
      appId: $appId
      tenantId: $tenantId
      operatorId: $operatorId
      dataSourceId: form66eb9c67e4b0941661d2757a
      flowTemplateId: 6c354949d71b426ab6d536587b5052c2
    validate:
      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]