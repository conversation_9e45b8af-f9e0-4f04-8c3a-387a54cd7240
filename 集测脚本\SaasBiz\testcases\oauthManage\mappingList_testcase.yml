- config:
    name: SaaS页面接口-查询授权关系-分页接口
    variables:
      - appId: "${ENV(authAppId_1)}"
      - psnId: "${ENV(auth_admin_oid)}"
      - psnId2: "${ENV(auth_psnId)}"
      - orgId: "${ENV(auth_orgId)}"
- test:
    name: 查询企业应用授权记录
    variables:
        - headers: "${gen_headers_rest($orgId,$psnId)}"
        - pageNum: 1
        - pageSize: 10
    api: api/oauthManage/mappingList_api.yml
    validate:
        - len_gt: [ "content.data.authList", 0 ]
        - gt: [ "content.data.count", 0 ]
        - ne: ["content.data.authList.0.appId",null]
        - ne: ["content.data.authList.0.appName",null]
        - contains: ["content.data.authList.0","appLogo"]
        - len_gt: ["content.data.authList.0.scopes",0]
        - ne: [ "content.data.authList.0.scopes.0.scopeCode",null ]
        - contains: [ "content.data.authList.0.scopes.0","scopeDesc" ]

- test:
    name: 查询企业应用授权记录-操作员无权限
    variables:
        - headers: "${gen_headers_rest($orgId,$psnId2)}"
        - pageNum: 1
        - pageSize: 10
    api: api/oauthManage/mappingList_api.yml
    validate:
        - ne: [ "content.code", 0 ]
        - eq: [ "content.message", "用户无操作权限" ]

- test:
    name: 查询企业应用授权记录-企业不存在
    variables:
        - headers: "${gen_headers_rest($org_str,$psnId)}"
        - pageNum: 1
        - pageSize: 10
        - org_str: "aaa"
    api: api/oauthManage/mappingList_api.yml
    validate:
        - ne: [ "content.code", 0 ]
#        - eq: [ "content.message", "账号不存在或已注销" ]

- test:
    name: 查询企业应用授权记录-操作员非法
    variables:
        - psn_str: "aaaa"
        - headers: "${gen_headers_rest($orgId,$psn_str)}"
        - pageNum: 1
        - pageSize: 10
    api: api/oauthManage/mappingList_api.yml
    validate:
        - ne: [ "content.code", 0 ]
#        - eq: [ "content.message", "账号不存在或已注销" ]




- test:
    name: 查询个人应用授权记录
    variables:
        - headers: "${gen_headers_rest($psnId,$psnId)}"
        - pageNum: 1
        - pageSize: 10
    api: api/oauthManage/mappingList_api.yml
    validate:
        - len_gt: [ "content.data.authList", 0 ]
        - gt: [ "content.data.count", 0 ]
        - ne: ["content.data.authList.0.appId",null]
        - ne: ["content.data.authList.0.appName",null]
        - contains: ["content.data.authList.0","appLogo"]
        - len_gt: ["content.data.authList.0.scopes",0]
        - ne: [ "content.data.authList.0.scopes.0.scopeCode",null ]
        - contains: [ "content.data.authList.0.scopes.0","scopeDesc" ]


- test:
    name: 查询个人应用授权记录-操作员无权限
    variables:
        - headers: "${gen_headers_rest($psnId,$psnId2)}"
        - pageNum: 1
        - pageSize: 10
    api: api/oauthManage/mappingList_api.yml
    validate:
        - ne: [ "content.code", 0 ]
#        - contains: [ "content.message", "账号不存在或已注销" ]

- test:
    name: 查询个人应用授权记录-个人账号不存在
    variables:
        - psn_str: "aaa"
        - headers: "${gen_headers_rest($psn_str,$psnId)}"
        - pageNum: 1
        - pageSize: 10
    api: api/oauthManage/mappingList_api.yml
    validate:
        - ne: [ "content.code", 0 ]
#        - contains: [ "content.message", "账号不存在或已注销" ]

- test:
    name: 查询个人应用授权记录-操作员非法
    variables:
        - psn_str: "aaaa"
        - headers: "${gen_headers_rest($psnId,$psn_str)}"
        - pageNum: 1
        - pageSize: 10
    api: api/oauthManage/mappingList_api.yml
    validate:
        - ne: [ "content.code", 0 ]
#        - contains: [ "content.message", "账号不存在或已注销" ]


