import os
import datetime
import time
import hashlib
import base64
import random
import importlib
import time
from time import sleep
import platform
import pymysql as mdb
from httprunner import utils
import pymysql
import string
import requests
import json
from datetime import timedelta
try:
    import redis
except Exception as e:
    print("开始执行安装redis==4.2.2 任务")
    val = os.system('pip install redis==4.2.2 -i https://mirrors.aliyun.com/pypi/simple/')
    import redis  # 二次引入

service_group = os.environ['service_group']
sep = os.path.sep  # 路径分隔符
cur_dir = os.path.abspath('.') + sep


def gen_headers(app_id, **kwargs):
    header = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": app_id,
        "X-Tsign-Open-Auth-Mode": "simple",
        "X-Tsign-Service-Group": service_group,
        "x-tsign-client-id":"WEB"
    }
    headers = {**header, **kwargs}
    return headers

#
# ·
# # 默认数据路径都放在data下
def open_file(local_path):
    fd = open(cur_dir + local_path, 'rb')
    return fd


def hook_sleep_n_secs(n_secs):
    time.sleep(n_secs)


def now_time(n):
    # return int(time.time())
    timeArray = (datetime.datetime.now() + datetime.timedelta(days=n)).strftime("%Y/%m/%d %H:%M:%S")
    timeArray1 = time.strptime(timeArray, "%Y/%m/%d %H:%M:%S")
    return int(time.mktime(timeArray1) * 1000)


def now_timeh(n):
    # return int(time.time())
    timeArray = (datetime.datetime.now() + datetime.timedelta(hours=n)).strftime("%Y/%m/%d %H:%M:%S")
    timeArray1 = time.strptime(timeArray, "%Y/%m/%d %H:%M:%S")
    return int(time.mktime(timeArray1) * 1000)


# 获取文件的大小
def get_file_size(local_path):
    size = os.path.getsize(cur_dir + local_path)
    return size


# 获取当前时间的时间戳
def getTimeStamp():
    t = time.time()
    return int(t)


def getTimeStamp_ms():
    t = time.time()
    return int(t * 1000)


def hook_sleep_n_secs(n_secs):
    time.sleep(n_secs)


def hook_sleep_n_secss(response, n_secs):
    if response.status_code == 200:  # 接口请求code等于200 则等待n_secs 秒
        time.sleep(n_secs)
    else:  # 接口请求code不等于200 则等待0.5 秒
        time.sleep(0.5)


def generate_random_str(randomlength):
    #     """
    #     生成一个指定长度的随机字符串
    #     """
    random_str = ''
    base_str = 'abcdefghigklmnopqrstuvwxyzabcdefghigklmnopqrstuvwxyz0123456789'
    length = len(base_str) - 1
    for i in range(randomlength):
        random_str += base_str[random.randint(0, length)]
    return random_str


def teardown_hook_sleep_N_secs(num):
    x = time.sleep(num)
    return x


def groupingNum():
    y = 1609430400000 + random.randint(0, 9) * 86400000
    return y


# 连接数据库
def connect_db(name):
    if (name == 'contract_manager'):
        connect = pymysql.Connect(
            host=os.environ['db_host1'],
            port=3306,
            user=os.environ['db_user1'],
            password=os.environ['db_pwd1'],
            database=os.environ['db_name1'],
            charset=os.environ['charset']
        )
    elif (name == 'saas_biz'):
        connect = pymysql.Connect(
            host=os.environ['db_host1'],
            port=3306,
            user=os.environ['db_user1'],
            password=os.environ['db_pwd1'],
            database=os.environ['db_name1'],
            charset=os.environ['charset']
        )
    elif (name == 'saas_base_manage'):
        connect = pymysql.Connect(
            host=os.environ['db_host2'],
            port=3306,
            user=os.environ['db_user2'],
            password=os.environ['db_pwd2'],
            database=os.environ['db_user2'],
            charset=os.environ['charset']
        )
    elif (name == 'seal'):
        connect = pymysql.Connect(
            host=os.environ['db_host3'],
            port=3306,
            user=os.environ['db_user3'],
            password=os.environ['db_pwd3'],
            database=os.environ['db_user3'],
            charset=os.environ['charset']
        )
    elif(name=='contract_analysis'):
        connect = pymysql.Connect(
            host=os.environ['db_host_contract_analysis'],
            port=3306,
            user=os.environ['db_user_contract_analysis'],
            password=os.environ['db_pwd_contract_analysis'],
            database=os.environ['db_name_contract_analysis'],
            charset='utf8')
    elif(name=='epaas_doc_template'):
            connect = pymysql.Connect(
                host=os.environ['db_host_epaas_doc_template'],
                port=3306,
                user=os.environ['db_user_epaas_doc_template'],
                password=os.environ['db_pwd_epaas_doc_template'],
                database=os.environ['db_name_epaas_doc_template'],
                charset='utf8')
    elif(name=='saas_integration'):
                connect = pymysql.Connect(
                    host=os.environ['db_host_saas_integration'],
                    port=3306,
                    user=os.environ['db_user_saas_integration'],
                    password=os.environ['db_pwd_saas_integration'],
                    database=os.environ['db_name_saas_integration'],
                    charset='utf8')
    elif(name== 'cm_new'):
                connect = pymysql.Connect(
                    host=os.environ['db_host_cm_new'],
                    port=3306,
                    user=os.environ['db_user_cm_new'],
                    password=os.environ['db_pwd_cm_new'],
                    database=os.environ['db_name_cm_new'],
                    charset=os.environ['db_charset_cm_new'])
    else:
        connect = pymysql.Connect(
            host=os.environ['db_host1'],
            port=3306,
            user=os.environ['db_user1'],
            password=os.environ['db_pwd1'],
            database=os.environ['db_name1'],
            charset=os.environ['charset']
        )
    return connect


# 查询数据库,返回一个string字符串
def select_sql(sql, db_name):
    db = connect_db(db_name)
    cur = db.cursor()
    try:
        cur.execute(sql)
        result = cur.fetchall()
        print("result:{}".format(result))
        print("查询的结果：{}".format(result[0][0]))
        return result[0][0]
    except Exception as e:
        print("出错了："+str(e))
    finally:
        cur.close()
    return
# if __name__=='__main__':
#     select_sql("select rule_id from contract_manager.contract_fulfillment_rule where tenant_oid='08486d61823d4c2086122b26fb3e615a' and deleted=0 and name='履约提醒-1727661574878' limit 1;",'contract_manager')

def delete_sql(host, user, pswd, db, sql):
    print(sql)
    status = 0
    conn = mdb.connect(host, user, pswd, db, charset='utf8')
    cur = conn.cursor()
    # 执行SQL语句
    cur.execute(sql)
    conn.commit()
    conn.cursor()
    conn.close()
    return status


# 数据库执行delete、update、insert操作
def execute_sql(db, sql):
    cur = db.cursor()
    try:
        cur.execute(sql)
        db.commit()
    except Exception as e:
        db.rollback()
        print(e)
    finally:
        db.close()


# hook__清理、准备测试数据
def hook_db_data(sql, db_name):
    db = connect_db(db_name)
    print("正在执行SQL：{0}".format(sql))
    execute_sql(db, sql)


# 版本号自增
def versionIncrement(v):
    str1 = v.replace('.', '')
    print(str1)
    num = int(str1) + 1
    str2 = str(num)
    b = []
    for i in range(len(str2)):
        b.append(str2[i:i + 1])
    print('.'.join(b))
    return '.'.join(b)


def code():
    # 获取26个大小写字母
    letters = string.ascii_letters
    # 获取26个小写字母
    Lowercase_letters = string.ascii_lowercase
    # 获取26个大写字母
    Capital = string.ascii_uppercase
    # 获取阿拉伯数字
    digits = string.digits
    # s是小写字母和数字的集合
    s = Lowercase_letters + digits
    # 生成28位小写和数字的集合，并将列表转字符串
    code = ''.join(random.sample(s, 5))
    print('随机code:%s' % code)
    return code


# 通用查询
def select_1(host, user, pswd, db, selectsql):
    print(db)
    print(selectsql)
    status = 0
    conn = mdb.connect(host, user, pswd, db, charset='utf8')
    cur = conn.cursor()
    # SQL 查询语句
    selectSQL = selectsql
    print(selectSQL)
    cur.execute(selectSQL)
    result = cur.fetchall()
    print(result)
    no11 = result[0][0]
    conn.commit()
    conn.cursor()
    conn.close()
    return no11


# 解约前查询及处理数据
def relate_query(selectsql1, selectsql2, updatesql):
    flag = True
    exist = select_sql(selectsql1, "contract_manager")
    if exist != None:
        status = select_sql(selectsql2, "contract_manager")
        if status == 4:
            hook_db_data(updatesql, "contract_manager")
        else:
            flag = False
    return flag


def update_sql(host, user, pswd, db, sql):
    print(sql)
    status = 0
    conn = mdb.connect(host, user, pswd, db, charset='utf8')
    cur = conn.cursor()
    # 执行SQL语句
    updateSql = sql
    conn.commit()
    conn.cursor()
    conn.close()


# 获取dict中对应key的value值
def getValue(dict, key1, value1, key2):
    value2 = ""
    for i in range(len(dict)):
        if dict[i][key1] == value1:
            value2 = dict[i][key2]
    return value2


# 获取当前日期的时间戳
def today_getTimeStamp_ms():
    t = datetime.datetime.now()
    # 当前日期
    t1 = t.strftime('%Y-%m-%d 00:00:00')
    # 转为秒级时间戳
    start_time = time.mktime(time.strptime(t1, '%Y-%m-%d %H:%M:%S'))
    return int(start_time * 1000)


# 获取当前日期的时间戳
def todayEnd_getTimeStamp_ms():
    t = datetime.datetime.now()
    # 当前日期
    t1 = t.strftime('%Y-%m-%d 23:59:59')
    # 转为秒级时间戳
    start_time = time.mktime(time.strptime(t1, '%Y-%m-%d %H:%M:%S'))
    return int(start_time * 1000)


# 获取经过base64转换的md5值, 参考https://yq.aliyun.com/articles/27523?spm=5176.11065265.1996646101.searchclickresult.3b6d4025K8qbp3
def get_file_base64_md5(local_path):
    fd = open(cur_dir + local_path, 'rb')
    m = hashlib.md5()
    while True:
        d = fd.read(8096)
        if not d:
            break
        m.update(d)
    byte = base64.b64encode(m.digest())
    return bytes.decode(byte)


# 两个数字相减
def minus(a, b):
    res = a - b
    return res


# 两个数字相减
def sum(a, b):
    res = a + b
    return res


def now_time_before(n):
    timeArray = (datetime.datetime.now() - datetime.timedelta(days=n)).strftime("%Y/%m/%d %H:%M:%S")
    timeArray1 = time.strptime(timeArray, "%Y/%m/%d %H:%M:%S")
    return int(time.mktime(timeArray1) * 1000)


def compareNum(a, b):
    return a > b


# 循环查询列表3次
def waitAndSearch(url1, params1, headers1, times):
    for i in range(times):
        data = process_list(url1, params1, headers1)
        if (data > 0):
            break
        time.sleep(2)
    return data


def process_list(url1, params1, headers1):
    baseUrl = 'http://in-test-openapi.tsign.cn'
    params = params1
    headers = headers1
    r = requests.get(url=baseUrl + url1, params=params, headers=headers)
    print(r.status_code)
    total = r.json()['data']['total']
    print(total)
    return total


# 获取批量任务进度是否有进行中或失败的任务
def get_addTaskStatus(data):
    print(data)
    if data == None:
        return True
    else:
        return False


def redis_val(key):
    # redis 账号密码
    configparser = {"host": "r-bp18f2292281c704.redis.rds.aliyuncs.com",
                    "password": "secret#123456#"

                    }
    # redis链接
    r = redis.Redis(**configparser)

    # 查询redis
    val = r.get(key)

    # 查询结果处理后返回
    if val:
        val = val.decode("utf8")
        val = json.loads(val)
        print(val)
        r.delete(key)
    else:
        print(val)
        return val


# 循环查询异步发起结果
def waitTillDone(url1, params1, headers1, times):
    for i in range(times):
        baseUrl = 'http://in-test-openapi.tsign.cn'
        params = params1
        headers = headers1
        r = requests.get(url=baseUrl + url1, params=params, headers=headers)
        # print(r.status_code)
        done = r.json()['data']['done']
        print(done)
        success = r.json()['data']['success']
        if (done == True):
            break
        time.sleep(2)
    return success


# SQL语句中动态传递参数
def select_sql2(sql, param, db_name):
    db = connect_db(db_name)
    cur = db.cursor()
    try:
        cur.execute(sql, param)
        result = cur.fetchall()
        print("查询的结果：{}".format(result[0][0]))
        return result[0][0]
    except Exception as e:
        print(e)
    finally:
        cur.close()
    return


# 校验流程的状态
def validateProcessStatus(processId):
    hook_sleep_n_secs(3)
    sql = "SELECT status FROM contract_manager.process where process_id=%s"
    db_name = 'cm'
    status = select_sql2(sql, processId, db_name)
    assert status == 1 or status == 2



# service_Group = os.environ['Service_Group_NEW']

def gen_headers_rest(tenantId,operatorId):
    headers = {"Content-Type": "application/json", "X-Tsign-Service-Id": "saas-biz",
               "X-Tsign-Service-Group": service_group,"X-Tsign-Open-Operator-Id":operatorId,
               "X-Tsign-Open-Tenant-Id":tenantId}
    return headers

def getLength (list):
    length  = len(list)
    return length

def str_append(str1,str2):
    list = []
    list.append(str1)
    list.append(str2)
    data = ",".join(list)
    return str(data)

# 循环获取批量任务进度
def waitTillDone2(url1, params1, headers1, times):
    for i in range(times):
        baseUrl = 'http://in-test-openapi.tsign.cn'
        params = params1
        headers = headers1
        r = requests.get(url=baseUrl + url1, params=params, headers=headers)
        # print(r.status_code)
        data = r.json()['data']
        # print(data)
        if (data == None):
            break
        time.sleep(2)
    # return success
def strToint(str):
    return int(str)

def get_next_year_timestamp():
    # 获取当前时间
    current_time = time.time()

    # 加上一年
    next_year = current_time + 31608696

    # 将时间转换为毫秒级别时间戳
    timestamp = int(next_year * 1000)

    return timestamp

def get_next_month_x3_timestamp():
    # 获取当前时间
    current_time = time.time()

    # 减三个月
    next_year = current_time - 724050

    # 将时间转换为毫秒级别时间戳
    timestamp = int(next_year * 1000)

    return timestamp

def strJoin(str1, str2):
    str = str1+str2
    return str

def getValueFromJson(json_data, key):
    data_dict = json.loads(json_data)
    value = data_dict[key]
    return value

#循环查询上传文件后的文件状态结果
def waitFileStatus(fileIds,tenantId,times):
    for i in range(times):
        print("-----第"+str(i+1)+"次循环-----")
        flag = False
        baseUrl = os.environ['open_url']
        app_id = os.environ['app_id']
        url = "/v2/files/status"
        data = {
                           "fileIds": fileIds,
                           "isCheck": True
                         }
        headers = {
                         "Content-Type": "application/json",
                         "X-Tsign-Open-App-Id": app_id,
                         "X-Tsign-Open-Auth-Mode": "simple",
                         "X-Tsign-Open-Tenant-Id": tenantId
                     }
        r = requests.post(url=baseUrl+url, json=data, headers=headers)
        #print(r.status_code)
        fileStatusList = r.json()['data']['fileStatusList']
        for fileStatus in fileStatusList:
          status = fileStatus['status']
          print(status)
          if(status!=2 and status!=5):
            flag = True
            break
        if flag==False:
          break
        time.sleep(2)


def get_future_timestamp(minutes=5):
    # 获取当前时间
    now = datetime.datetime.now()
    # 加上指定的分钟数
    future_time = now + timedelta(minutes=minutes)
    # 将未来的时间转换为时间戳（秒级）
    timestamp = timedelta_to_timestamp(future_time)
    return timestamp


def timedelta_to_timestamp(td):
    # 计算总秒数
    total_seconds = (td - datetime.datetime(1970, 1, 1)).total_seconds()
    return total_seconds

#获取当前时间n秒后的时间戳
def timestamp_after_n_seconds(n):
    timestamp_after_n_seconds = int((time.time() + n) * 1000)
    return timestamp_after_n_seconds

# 两个数字相减
def sum(a, b):
    res = a + b
    return res

# 验证印章列表中所有印章的sealBizType是否都匹配指定类型
def validate_all_seals_biz_type(seal_list, expected_biz_type):
    """
    验证印章列表中所有印章的sealBizType是否都匹配指定类型
    :param seal_list: 印章列表
    :param expected_biz_type: 期望的印章业务类型
    :return: True表示所有印章都匹配，False表示有不匹配的
    """
    if not seal_list:
        return True  # 空列表认为验证通过

    for seal in seal_list:
        if seal.get('sealBizType') != expected_biz_type:
            return False

    return True