- config:
    name: 流程测试
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      subjectId: ${ENV(sasstiany_accountId)}

- test:
    name: 判断用户是否存在天印流程
    api: api/saas_tianyin_process/flows_user_check.yml
    variables:
      type: 2
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      pageSize: 10
      pageNum: 1
      status: 2
      keyword: ""
      subjectId: ${ENV(sasstiany_accountId)}
      code: 0
      message: "成功"
    extract:
      - flowId: content.data.flows.0.flowId

- test:
    name: 获取签署任务概要
    api: api/saas_tianyin_process/flows_outline.yml
    variables:
      code: *********
      message: "您没有查看当前流程的权限"

- test:
    name: 获取用户可查看的企业天印流程列表
    api: api/saas_tianyin_process/flows_view_list.yml
    variables:
      flowName: "个人"
      status: 2
      code: 0
      message: "成功"

- test:
    name: 获取用户可出证的天印流程列表
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: ""
      pageNum: ""
      appId: ""
      flowName: ""
      signerKeyWord: ""
      initiatorKeyWord: ""
      createFrom: ""
      createEnd: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"