- config:
    name: 合同备注相关场景
    variables:
      accountId1: ${ENV(nss_accountId1)}
      account1: ***********
      accountName1: 隆多
      accountId2: ${ENV(nss_accountId2)}
      orgId1: ${ENV(nss_orgid1)}
      orgName1: esigntest你是真的秀
      taskName1: 直接发起-${getTimeStamp_ms()}
      fileId1: 3a05bfd44d674d6dadb85ead01bef255


- test:
    name: 直接发起
    api: api/contract/startProcess.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      approveTemplateId: null
      businessType: 0
      ccs: []
      files:
        [
        {
          "fileId": $fileId1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": "简单文档.pdf",
          "from":2,                 #文件来自 1-模板文件 2-合同文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: ""
      initiatorAccountId: $accountId2
      participants:
        [
        {
          "participantSubjectType":1,
          "role":"3",
          "sealType":null,
          "signRequirements":"1",
          "roleSet":1,
          "fillOrder":0,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":null,
          "instances":[
          {
            "account": $account1,
            "accountOid": $accountId1,
            "accountNick":"",
            "accountName": $accountName1,
            "accountRealName":true,
            "comment":"",
            "subjectId": $orgId1,
            "subjectName": $orgName1,
            "subjectRealName":true,
            "subjectType":1,
            "preFillValues":null,
            "fileValues":null,
            "subTaskName":"",
            "subTaskBizId":"",
            "subTaskBizType":""
          }
          ],
          "willTypes":[

          ],
          "signSealType":1,
          "signSeal":"",
          "forceReadEnd":false,
          "forceReadTime":""
        }
        ]
      scene: 1
      taskName: $taskName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - processId1: content.data.processId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 新增合同备注
    api: api/contract/add-remark.yml
    variables:
      processId: $processId1
      tenantId: $orgId1
      menuId:
      remark: 青莲备注-${getTimeStamp_ms()}
      OperatorId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询合同备注
    api: api/contract/get-remark.yml
    variables:
      processId: $processId1
      tenantId: $orgId1
      menuId: ''
      OperatorId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - contains: ["content.data.0.remark", 青莲备注]
