- config:
    name: 归档条件异常case
    variables:
      orgId1: 92e7df785d36440c89096b71a83fcf06
      accountId1: ${ENV(mx_accountId)}
      menuId1: 5f30298c14df4cca906af634141c868a   #有归档条件且绑定了非指定分类的台账
      menuId2: 7583f4b50f814349b673299c300406ae   #无归档条件，未绑定台账
      menuId3: 114abc2ecd5549b9aed53e1b5ee4f8d8   #有指定该分类的台账，无归档条件
      formId1: 971a17a8f7084047ab0ea9c0a3c95cc0    #指定分类的台账，指定的分类是menuId3

- test:
    name: 归档系统条件
    api: api/auto-archive/system-rule-config.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      bizType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fieldId1: content.data.0.fieldId
      - fieldName1: content.data.0.fieldName

- test:
    name: 创建归档条件-分类已存在归档条件
    api: api/auto-archive/save-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      bindingFormId: ""
      bindingMenuId: $menuId1
      conditions:
        [
        {
          "fieldId":"$fieldId1",
          "key":"${getTimeStamp_ms()}",
          "value":[
            "集测归档条件"
          ],
          "errorMessage":"",
          "childOperatorType":2,
          "startNum":"",
          "endNum":"",
          "matchType":3,
          "operatorType":1,
          "childOperators":[
          {
            "operatorType":2,
            "conditionParams":"[\"集测归档条件\"]"
          }
          ],
          "fieldType":3
        }
        ]
      removeContract: 1
      status: 0
      unityForm: 0
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "该分类已绑定规则，请重新选择"]

- test:
    name: 创建归档条件-选择的台账是指定其他分类的
    api: api/auto-archive/save-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      bindingFormId: $formId1
      bindingMenuId: $menuId2
      conditions:
        [
        {
          "fieldId":"$fieldId1",
          "key":"${getTimeStamp_ms()}",
          "value":[
            "集测归档条件"
          ],
          "errorMessage":"",
          "childOperatorType":2,
          "startNum":"",
          "endNum":"",
          "matchType":3,
          "operatorType":1,
          "childOperators":[
          {
            "operatorType":2,
            "conditionParams":"[\"集测归档条件\"]"
          }
          ],
          "fieldType":3
        }
        ]
      removeContract: 1
      status: 0
      unityForm: 0
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "该台账已经关联其他分类，不支持新的分类归档使用"]

- test:
    name: 删除分类-指定分类有台账
    api: api/grouping_menus/delete-menu.yml
    variables:
      tenantId: $orgId1
      menuId: $menuId3
      accountId: $accountId1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", "该分类已绑定台账, 请在智能台账中取消对应分类后再做此操作"]

- test:
    name: 创建分类
    api: api/grouping_menus/add-menu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      name: 集测创建分类-${getTimeStamp_ms()}
      parentMenuId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - menuId4: content.data

- test:
    name: 创建归档条件
    api: api/auto-archive/save-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      bindingFormId: ""
      bindingMenuId: $menuId4
      conditions:
        [
        {
          "fieldId":"$fieldId1",
          "key":"${getTimeStamp_ms()}",
          "value":[
            "集测归档条件"
          ],
          "errorMessage":"",
          "childOperatorType":2,
          "startNum":"",
          "endNum":"",
          "matchType":3,
          "operatorType":1,
          "childOperators":[
          {
            "operatorType":2,
            "conditionParams":"[\"集测归档条件\"]"
          }
          ],
          "fieldType":3
        }
        ]
      removeContract: 1
      status: 0
      unityForm: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 归档列表-验证归档条件创建成功
    api: api/auto-archive/rule-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formName: ""
      menuId: $menuId4
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]

- test:
    name: 删除分类
    api: api/grouping_menus/delete-menu.yml
    variables:
      tenantId: $orgId1
      menuId: $menuId4
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 归档列表-无结果
    api: api/auto-archive/rule-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formName: ""
      menuId: $menuId4
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.menuList", null]
