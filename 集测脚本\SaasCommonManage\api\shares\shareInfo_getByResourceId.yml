name: 根据资源id获取资源分享信息
variables:
  shareEndTime: ""
  platform: ""
  appid: ${ENV(appid)}
request:
  url: ${ENV(inner_open_url)}/v1/saas-common/shares/getByResourceId
  method: GET
  headers: ${gen_headers($appid)}
  params:
    accountId: $accountId
    subjectId: $subjectId
    resourceId: $resourceId
    resourceType: $resourceType
    shareOperateType: $shareOperateType
    router: $router
    shareEndTime: $shareEndTime
    platform: $platform
