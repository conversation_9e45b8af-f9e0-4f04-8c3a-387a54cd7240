- config:
    name: 分享给流程参与人场景
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      - accountId1: ${ENV(accountId1)}
      - shareEndTime: ""
      - shareOperateType: BORROW_READ
      - shareTargets: []
      - orgId1: ${ENV(orgId1)}
      - shareType: 1
      - resourceType: PROCESS
      - router: share
      - processId1: ${ENV(processId1)}
      - processId2: ${ENV(processId2)}
      - processId3: ${ENV(processId3)}
      - menuId1: ${ENV(menuId1)}
      - accountId2: ${ENV(accountId2)}



- test:
      name: 经办合同列表已完成的流程进行分享
      api: api/shares/create_share.yml
      variables:
          accountId: $accountId1
          callSorce: CONTRACT
          menuId: ""
          resourceId: $processId3
          subjectId: $orgId1
      validate:
          - eq: ["content.code", 0]
          - eq: ["content.message", 成功]
          - ne: ["content.data.resourceShareId", null]
          - ne: ["content.data.shareUrl", null]


- test:
      name: 企业合同已归档分类列表签署中的流程进行分享
      api: api/shares/create_share.yml
      variables:
          accountId: $accountId1
          callSorce: ORG_ARCHIVE_MENU_ID
          menuId: $menuId1
          resourceId: $processId1
          subjectId: $orgId1
      validate:
          - eq: ["content.code", 0]
          - eq: ["content.message", 成功]
          - ne: ["content.data.shareUrl", null]
      extract:
          resourceShareId1: content.data.resourceShareId


- test:
      name: 企业合同待归档列表填写中的流程进行分享
      api: api/shares/create_share.yml
      variables:
          accountId: $accountId1
          callSorce: ORG_WAITING_ARCHIVE
          menuId: ""
          resourceId: $processId2
          subjectId: $orgId1
      validate:
          - eq: ["content.code", 0]
          - eq: ["content.message", 成功]
          - ne: ["content.data.resourceShareId", null]
          - ne: ["content.data.shareUrl", null]


- test:
      name: 根据资源分享id获取资源分享信息
      api: api/shares/shareInfo_getByResourceShareId.yml
      variables:
          resourceShareId: $resourceShareId1
      validate:
          - eq: ["content.code", 0]
          - eq: ["content.message", 成功]
          - eq: ["content.data.resourceId", $processId1]


- test:
      name: 获取资源最终地址-流程参与人
      api: api/shares/getResourceUrl.yml
      variables:
          accountId: $accountId1
          subjectId: $orgId1
          resourceShareId: $resourceShareId1
      validate:
          - eq: ["content.code", 0]
          - eq: ["content.message", 成功]
          - ne: ["content.data.resourceUrl", null]
          - eq: ["content.data.hasAuth", true]


- test:
      name: 获取资源最终地址-非流程参与人
      api: api/shares/getResourceUrl.yml
      variables:
          accountId: $accountId2
          subjectId: $orgId1
          resourceShareId: $resourceShareId1
      validate:
          - eq: ["content.code", 0]
          - eq: ["content.message", 成功]
          - eq: ["content.data.resourceUrl", null]
          - eq: ["content.data.hasAuth", false]
