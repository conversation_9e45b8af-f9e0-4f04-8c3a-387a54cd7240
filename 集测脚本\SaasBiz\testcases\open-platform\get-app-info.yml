- config:
    name: 获取APP信息
    variables:
      tenantId: ${ENV(nss_orgid1)}
      operatorId: 81728261a6004f1ba11ffa8002447e61
      app_id: ${ENV(bzq_appid)}
      app_id2: 7876556673
      app_id3: 1111111111
      app_id4: 7876718581

- test:
    name: 获取app信息-获取成功
    api: api/open-platform/get-app-info.yml
    variables:
      "appId": $app_id2
      "subjectOid": $tenantId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取app信息-appid不存在
    api: api/open-platform/get-app-info.yml
    variables:
      "appId": $app_id3
      "subjectOid": $tenantId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.exist", false]

- test:
    name: 获取app信息-appid存在但查询企业合同列表，验证流程状态为签署中不属于该企业
    api: api/open-platform/get-app-info.yml
    variables:
      "appId": $app_id4
      "subjectOid": $tenantId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.exist", True]
