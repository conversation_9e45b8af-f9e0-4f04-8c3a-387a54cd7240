- config:
    name: 查询权限申请文案
    variables:
        orgId1: ${ENV(orgId4)}
        accountId1: ${ENV(accountId6)}


- test:
    name: 查询权限申请文案-code不存在
    api: api/permission/apply-msg.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      code: 123
    validate:
      - eq: ["content.code",*********]
      - eq: ["content.message", 当前权限暂未配置申请文案]

- test:
    name: 查询权限申请文案-无模板编辑权限
    api: api/permission/apply-msg.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      code: TEMPLATE-UPDATE
    validate:
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]
      - contains: ["content.data.noPermissionHintText", 您没有模板授权权限]

