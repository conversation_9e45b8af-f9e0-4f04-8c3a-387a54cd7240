- config:
    name: 获取用户基本信息


- test:
    name: 获取用户基本信息
    api: api/saas_tianyin_process/get_accountInfo.yml
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]


- test:
    name: 获取用户基本信息-accountId不存在
    api: api/saas_tianyin_process/get_accountInfo.yml
    variables:
      accountId: '123456'
    validate:
      - eq: [ "content.code", ********* ]
      - eq: [ "content.message", 账号不存在或已注销 ]
