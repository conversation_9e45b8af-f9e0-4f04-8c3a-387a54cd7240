- config:
    name: 归档条件自动绑定指定分类的台账
    variables:
      orgId1: 92e7df785d36440c89096b71a83fcf06
      accountId1: ${ENV(mx_accountId)}
      menuId1: 47316c8fbf4042258ede8170fe005796   #无归档条件，未绑定台账
      fieldId1: 09b4fb23fc674c6f97fc34f4e99f56af
      formName1: 指定归档分类1-${getTimeStamp_ms()}
      formName2: 指定归档分类2-${getTimeStamp_ms()}
      db_name: contract_analysis
      orgGid1: ac0091d8148e454db6fd0ccce1206883

#先创建台账再创建归档条件
- test:
    name: 1创建台账-提取方式为指定归档分类
    api: api/contract-ledger/save-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: [$menuId1]
      conditions: []
      extractType: 6
      fieldIds: [$fieldId1]
      formName: $formName1
      ledgerTemplateMapping: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - formId1: content.data

- test:
    name: 1校验菜单是否存在规则-已自动绑定
    api: api/auto-archive/exist-menu-rule-bind.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.autoBindRuleMenu", true]
      - eq: ["content.data.exist", false]

- test:
    name: 1创建归档条件
    api: api/auto-archive/save-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      bindingFormId: ""
      bindingMenuId: $menuId1
      conditions:
        [
        {
          "fieldId":"$fieldId1",
          "key":"${getTimeStamp_ms()}",
          "value":[
            "集测归档条件"
          ],
          "errorMessage":"",
          "childOperatorType":2,
          "startNum":"",
          "endNum":"",
          "matchType":3,
          "operatorType":1,
          "childOperators":[
          {
            "operatorType":2,
            "conditionParams":"[\"集测归档条件\"]"
          }
          ],
          "fieldType":3
        }
        ]
      removeContract: 1
      status: 0
      unityForm: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 1校验菜单是否存在规则-已存在
    api: api/auto-archive/exist-menu-rule-bind.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.exist", true]

- test:
    name: 1删除归档条件
    api: api/auto-archive/delete-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 1删除台账
    api: api/contract-ledger/delete-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      sql1: "SELECT form_id FROM contract_analysis.form where tenant_gid='$orgGid1' and form_name='$formName1' limit 1;"
      formId: ${select_sql($sql1, $db_name)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

#先创建归档条件再创建台账
- test:
    name: 2创建归档条件
    api: api/auto-archive/save-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      bindingFormId: ""
      bindingMenuId: $menuId1
      conditions:
        [
        {
          "fieldId":"$fieldId1",
          "key":"${getTimeStamp_ms()}",
          "value":[
            "集测归档条件"
          ],
          "errorMessage":"",
          "childOperatorType":2,
          "startNum":"",
          "endNum":"",
          "matchType":3,
          "operatorType":1,
          "childOperators":[
          {
            "operatorType":2,
            "conditionParams":"[\"集测归档条件\"]"
          }
          ],
          "fieldType":3
        }
        ]
      removeContract: 1
      status: 0
      unityForm: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 2创建台账-提取方式为指定归档分类
    api: api/contract-ledger/save-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: [$menuId1]
      conditions: []
      extractType: 6
      fieldIds: [$fieldId1]
      formName: $formName2
      ledgerTemplateMapping: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - formId2: content.data

- test:
    name: 2校验菜单是否存在规则-已自动绑定
    api: api/auto-archive/exist-menu-rule-bind.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.autoBindRuleMenu", true]

- test:
    name: 2删除归档条件
    api: api/auto-archive/delete-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 2删除台账
    api: api/contract-ledger/delete-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      sql1: "SELECT form_id FROM contract_analysis.form where tenant_gid='$orgGid1' and form_name='$formName2' limit 1;"
      formId: ${select_sql($sql1, $db_name)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
