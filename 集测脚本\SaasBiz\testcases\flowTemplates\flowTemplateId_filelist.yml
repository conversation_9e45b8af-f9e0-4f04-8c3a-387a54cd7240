- config:
    name: 合同编号规则相关操作（完成）
    variables:
      orgId1: "52b72c6d9ac941bebdd0431d97f2f8ab"  #企业oid esigntest你是真的秀
      templateId1: "9df2eecac5a94f6eaf0fd49c46794945"
      OperatorId1: "691e9d2a1aae49929cf9c2b446a1e157"


- test:
    name: 流程模板的文件列表
    variables:
        TenantId: $orgId1
        OperatorId: $OperatorId1
        flowTemplateId: $templateId1

    api: api/flowTemplates/flowTemplateId_filelist.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]