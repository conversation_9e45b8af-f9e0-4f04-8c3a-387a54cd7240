import time,hashlib,hmac,urllib,base64,requests,logging
# 钉钉机器人-安全设置-加签
def secret_markdown(msg, title='测试环境P0接口', mobile='13735865796',
                    access_token='24d99320d54ca785b8d96bd1dc8355890072e0bc0e9c5006329441ef2a086709',
                    secret='SEC8f5b5cc26fc0e2b21615f8cae15089cd84cdf7e44d559d133de2b90f3b745c37'):
    timestamp = str(round(time.time() * 1000))
    secret_enc = secret.encode('utf-8')
    string_to_sign = '{}\n{}'.format(timestamp, secret)
    string_to_sign_enc = string_to_sign.encode('utf-8')
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
    url = "https://oapi.dingtalk.com/robot/send?access_token=" + access_token + "&timestamp=" + timestamp + "&sign=" + sign
    data = {
        "msgtype": "markdown",
        "markdown": {
            "title": title,
            "text": msg + "@" + mobile
        },
        "at": {
            "atMobiles": [
                mobile,
            ],
            "isAtAll": False
        }
    }
    requests.packages.urllib3.disable_warnings()
    res = requests.post(url=url, json=data, verify=False)
    logging.info(res.text)


secret_markdown('测试环境P0接口')
