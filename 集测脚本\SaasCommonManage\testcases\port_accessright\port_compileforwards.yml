- config:
    name: 停用/启用接口转发及参数替换配置
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      accountId: ${ENV(accountId1)}


- test:
    name: 停用/启用接口转发及参数替换配置-成功
    api: api/port_accessright/port_compileforwards.yml
    variables:
      id: 598
      valid: false
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 接口转发及参数替换规则配置不存在]

- test:
    name: 停用/启用接口转发及参数替换配置-成功
    api: api/port_accessright/port_compileforwards.yml
    variables:
      id: 1
      valid: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
