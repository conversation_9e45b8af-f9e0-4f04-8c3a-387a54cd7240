- config:
    name: 新建模板，禁用，启用，删除 （完成）
    variables:
      orgOid1: 714902c36f2440e281b2ff984b6e29f4         #发起主体企业
      accountOid1: b4d26d9fd5e644959cadb9434c9c41c6     #发起人无需合同审批
      file_name1: "test.pdf"
      accountOid2: a959b44e7e0f47c18e205cec5bdd861e     #企业签署人
      account2: ***********
      accountName2: 自动化测试零三
      orgOid2: bc7a0b1bf2bf42f1b99818f660b97420         #签署主体企业
      orgName2: esigntest自动化测试企业2
      accountOid3:  c0757ce4e3e3476196fd22d0c56bc1f2    #个人签署人
      account3: ***********
      accountName3: 自动化测试零二
      templateName1: 两人仅签模板-${getTimeStamp()}
      taskName1: 使用仅签模板发起-${getTimeStamp()}
      accountOid4: 3d02a7c203fc436d8f5baf86a5481235     #发起主体企业的管理员
      orgGid1: 73f94e148b994586b8de56f89210150b
#      fileId1:
#      pwd: C06DB68E819BE6EC3D26C6038D8E8D1F             #签署人/审批人签署密码
      db_name1: contract_manager


- test:
    name: 上传文件
    testcase: testcases/upload_file.yml
    variables:
      tenantId1: $orgOid1
      accountId1: $accountOid4
      contentMd5: m49PzxWxAG8sMwNkZrlJDw==
      contentType: application/pdf
      convert2Pdf: false
      fileName1: $file_name1
      fileSize1: 99580
      filePath1: "data/test.pdf"
    output:
      - fileId1


- test:
    name: 保存流程模板
    api: api/contract-manager2/save_flowTemplate.yml
    variables:
      tenantId: $orgOid1
      businessType: 0
      ccs: []
      files:
        [
        {
          "fileId": $fileId1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $file_name1,
          "from":2,                 #文件来自 1-模板文件 2-合同文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: ""
      initiatorAccountId: $accountOid4
      participants:
        [
        {
          "participantSubjectType":0,
          "role":"3",
          "sealType":"0,1",
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":null,
          "instances":null,
          "willTypes":[]
        },
        {
          "participantSubjectType":1,
          "role":"3",
          "sealType":null,
          "signRequirements":"1",
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方2",
          "participantId":null,
          "instances":null,
          "willTypes":[]
        }
        ]
      previewType: 0
      scene: 3
      taskName: $templateName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.processId", null]
      - eq: ["content.data.realProcessId", null]
    extract:
      - flowTemplateId1: content.data.processId


- test:
    name: 获取流程模板信息
    api: api/footstone-doc/getFlowTemplate.yml
    variables:
      tenantId: $orgOid1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - cooperationerId1: content.data.cooperationers.0.cooperationerId
      - cooperationerId2: content.data.cooperationers.1.cooperationerId
      - templateId1: content.data.docTemplates.0.templateId


- test:
    name: 保存控件
    api: api/footstone-doc/save_components.yml
    variables:
      tenantId: $orgOid1
      templateId: $templateId1
      structComponents:
        [
        {
          "type":6,
          "allowEdit":true,
          "context":{
            "label":"签署区",
            "ext":"{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"\",\"imgType\":1,\"fillLengthLimit\":0,\"order\":1,\"desc\":\"\",\"ocrCheckType\":0,\"mustSign\":true,\"usePageType\":\"current\",\"sealSpecs\":1}",
            "limit":null,
            "required":false,
            "pos":{
              "x":221,
              "y":680,
              "page":1
            },
            "style":{
              "width":100,
              "height":100,
              "font":1,
              "fontSize":12,
              "textColor":"#000000"
            }
          }
        },
        {
          "type":6,
          "allowEdit":true,
          "context":{
            "label":"骑缝章",
            "ext":"{\"units\":\"px\",\"qiFeng\":true,\"signDatePos\":null,\"signRequirements\":\"1\",\"imgType\":1,\"fillLengthLimit\":0,\"order\":2,\"desc\":\"\",\"ocrCheckType\":0,\"mustSign\":true,\"usePageType\":\"current\",\"sealSpecs\":1,\"page\":\"1-2\"}",
            "limit":null,
            "required":true,
            "pos":{
              "x":535,
              "y":496,
              "page":0
            },
            "style":{
              "width":60,
              "height":120,
              "font":1,
              "fontSize":16,
              "textColor":"#000000"
            }
          }
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - structId1: content.data.0
      - structId2: content.data.1


- test:
    name: 关联参与方与控件
    api: api/footstone-doc/bind_components.yml
    variables:
      tenantId: $orgOid1
      flowTemplateId: $flowTemplateId1
      cooperationerStructs:
        [
        {
          "cooperationerId":$cooperationerId1,
          "structIds":[
            $structId1
          ]
        },
        {
          "cooperationerId":$cooperationerId2,
          "structIds":[
            $structId2
          ]
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 确认流程模板设置
    api: api/footstone-doc/confirm_flowTemplate.yml
    variables:
      tenantId: $orgOid1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 启用流程模板
    api: api/flowTemplates/enable_flowTemplate.yml
    variables:
      tenantId: $orgOid1
      operatorId: $accountOid4
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 禁用流程模板
    api: api/flowTemplates/disable_flowTemplate.yml
    variables:
      tenantId: $orgOid1
      operatorId: $accountOid4
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 删除流程模板
    api: api/flowTemplates/delete_flowTemplate.yml
    variables:
      tenantId: $orgOid1
      operatorId: $accountOid4
      sql1: "SELECT flow_template_id FROM doc_cooperation.flow_template where oid='$orgOid1' and flow_template_name='$templateName1';"
      flowTemplateId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
