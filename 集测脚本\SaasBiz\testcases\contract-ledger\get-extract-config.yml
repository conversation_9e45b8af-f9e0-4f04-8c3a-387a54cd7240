- config:
    name: 查询提取限制
    variables:
      orgId1: b9e33907adcb4591a4326ba916a92cd8
      accountId1: ${ENV(mx_accountId)}
      orgId2: 167c277155754dacb4a7ab6820955159

- test:
    name: 查询提取限制-正式会员用户
    api: api/contract-ledger/get-extract-config.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 20000]
      - ge: ["content.data.margin", 0]
      - eq: ["content.data.vipNormal", true]

- test:
    name: 查询提取限制-赠送会员用户
    api: api/contract-ledger/get-extract-config.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 20000]
      - ge: ["content.data.margin", 0]
      - eq: ["content.data.vipNormal", false]

- test:
    name: 查询提取限制-操作人不是企业成员
    api: api/contract-ledger/get-extract-config.yml
    variables:
      tenantId: $orgId1
      operatorId: ff115e6dfc834f3aa4bded527459b84c
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", "您不是该企业成员，请联系企业管理员加入企业。"]

- test:
    name: 查询提取限制-tenantId未实名无gid
    api: api/contract-ledger/get-extract-config.yml
    variables:
      tenantId: ff115e6dfc834f3aa4bded527459b84c
      operatorId: ff115e6dfc834f3aa4bded527459b84c
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]
      - ge: ["content.data.margin", 0]
      - eq: ["content.data.vipNormal", false]

- test:
    name: 查询提取限制-企业的会员版本<高级版
    api: api/contract-ledger/get-extract-config.yml
    variables:
      tenantId: 1869df2e7c5e475b8c4cefc5d7049733
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]
      - ge: ["content.data.margin", 0]
      - eq: ["content.data.vipNormal", false]
