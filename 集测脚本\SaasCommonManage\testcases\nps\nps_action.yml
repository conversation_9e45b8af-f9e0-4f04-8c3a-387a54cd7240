- config:
    name: 问卷相关操作
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      - productCode1: mx-${getTimeStamp_ms()}
      - productName1: 明绣-${getTimeStamp_ms()}
      - areaCode1: test
      - areaName1: 测试
      - accountId1: ${ENV(accountId1)}
      - name1: 问卷-${getTimeStamp_ms()}
      - db_name1: saas-common-manage


- test:
    name: 新增产品
    api: api/product-area/add_product.yml
    variables:
      productCode: $productCode1
      productName: $productName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 新增位置
    api: api/product-area/add_area.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      areaName: $areaName1
      type: 4
      displayType: 2
      capacity: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 新增问卷内容
    api: api/nps/add_nps.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      name: $name1
      pageStayTime: 3
      showCycle: 1
      showType: 1
      thirdNpsId: 123
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取用户可出现的问卷信息-不展示
    api: api/nps/get_nps.yml
    variables:
      productCode: $productCode1
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.npsInfos", null]


- test:
    name: 问卷分页查询
    api: api/nps/nps_list.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      name: $name1
      status: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      npsId1: content.data.npsInfoAdmins.0.id


- test:
    name: 上架问卷
    api: api/nps/enable_nps.yml
    variables:
      npsId: $npsId1
      status: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取用户可出现的问卷信息
    api: api/nps/get_nps.yml
    variables:
      productCode: $productCode1
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.npsInfos", null]


- test:
    name: 问卷展示回调
    api: api/nps/nps_callback.yml
    variables:
      npsId: $npsId1
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取用户可出现的问卷信息-不展示
    api: api/nps/get_nps.yml
    variables:
      productCode: $productCode1
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.npsInfos", null]


- test:
    name: 下架问卷
    api: api/nps/enable_nps.yml
    variables:
      npsId: $npsId1
      status: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 删除问卷,并删除产品和位置
    api: api/nps/delete_nps.yml
    variables:
      npsId: $npsId1
      sql1: "delete from product_area_info where product_code='$productCode1' and area_code='$areaCode1';"
      sql2: "delete from product_info where product_code='$productCode1';"
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_db_data($sql1, $db_name1)}
      - ${hook_db_data($sql2, $db_name1)}
