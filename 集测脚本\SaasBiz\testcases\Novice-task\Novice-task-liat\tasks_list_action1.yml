- config:
    name: 获取url
    variables:
      code1: 0
      message1: 成功

- test:
     name: 企业新手任务过滤主体filterSubject为false
     api: api/Novice-task/Novice-task-list/tasks_list_1.yml
     variables:
          filterSubject: false
          accountId: 9ef3d1358bfa426e8a11a3158bca46a9
          tenantId: 9ef3d1358bfa426e8a11a3158bca
     validate:
         - eq: ["content.code", $code1]
         - contains: ["content.message", $message1]
#

- test:
     name: 企业新手任务过滤主体filterSubject为true
     api: api/Novice-task/Novice-task-list/tasks_list_1.yml
     variables:
          filterSubject: true
          accountId: 9ef3d1358bfa426e8a11a3158bca46a9
          tenantId: 9ef3d1358bfa426e8a11a3158bca
     validate:
         - eq: ["content.code", $code1]
         - contains: ["content.message", $message1]

#***********  没有企业的个人账号  accountId：a19cd9dd945445cb8588ed0da65ba41e
- test:
     name: 个人新手任务过滤主体filterSubject为false
     api: api/Novice-task/Novice-task-list/tasks_list_1.yml
     variables:
          accountId: a19cd9dd945445cb8588ed0da65ba41e
          filterSubject: false
          tenantId: a19cd9dd945445cb8588ed0da65ba41e
     validate:
         - eq: ["content.code", $code1]
         - contains: ["content.message", $message1]
#

- test:
     name: 个人新手任务过滤主体filterSubject为true
     api: api/Novice-task/Novice-task-list/tasks_list_1.yml
     variables:
          filterSubject: true
          accountId: a19cd9dd945445cb8588ed0da65ba41e
          tenantId: a19cd9dd945445cb8588ed0da65ba41e
     validate:
         - eq: ["content.code", $code1]
         - contains: ["content.message", $message1]

#    未实名企业   ***********    王之印    个人OID：b34ecfab05354cc8aa456d84ce39ce76 企业OID： 9ef3d1358bfa426e8a11a3158bca46a9


- test:
     name: 未实名企业新手任务过滤主体filterSubject为false
     api: api/Novice-task/Novice-task-list/tasks_list_1.yml
     variables:
          accountId: b34ecfab05354cc8aa456d84ce39ce76
          filterSubject: false
          tenantId: 9ef3d1358bfa426e8a11a3158bca46a9
     validate:
         - eq: ["content.code", $code1]
         - contains: ["content.message", $message1]
#

- test:
     name: 未实名企业新手任务过滤主体filterSubject为true
     api: api/Novice-task/Novice-task-list/tasks_list_1.yml
     variables:
          filterSubject: true
          accountId: b34ecfab05354cc8aa456d84ce39ce76
          tenantId: 9ef3d1358bfa426e8a11a3158bca46a9
     validate:
         - eq: ["content.code", $code1]
         - contains: ["content.message", $message1]

#数政企业 个人OID：60dee8af527d46f6a9b91631bc9accff 企业oid:  0222219e0b7344f599047581281e74a0
- test:
     name: 未实名企业新手任务过滤主体filterSubject为false
     api: api/Novice-task/Novice-task-list/tasks_list_1.yml
     variables:
          accountId: 60dee8af527d46f6a9b91631bc9accff
          filterSubject: false
          tenantId: 0222219e0b7344f599047581281e74a0
     validate:
         - eq: ["content.code", $code1]
         - contains: ["content.message", $message1]
#

- test:
     name: 未实名企业新手任务过滤主体filterSubject为true
     api: api/Novice-task/Novice-task-list/tasks_list_1.yml
     variables:
          filterSubject: true
          accountId: 60dee8af527d46f6a9b91631bc9accff
          tenantId: 0222219e0b7344f599047581281e74a0
     validate:
         - eq: ["content.code", $code1]
         - contains: ["content.message", $message1]

#      ***********   王之梦      432427189203188070  一个企业只实名 个人oid: ea964ce78b2d4018b9fd8ec996ae1daf 企业oid:0b928e1dcb0f4592b88a724bd120a630

- test:
     name: 已经实名企业新手任务过滤主体filterSubject为false
     api: api/Novice-task/Novice-task-list/tasks_list_1.yml
     variables:
          accountId: ea964ce78b2d4018b9fd8ec996ae1daf
          filterSubject: false
          tenantId: 0b928e1dcb0f4592b88a724bd120a630
     validate:
         - eq: ["content.code", $code1]
         - contains: ["content.message", $message1]
#

- test:
     name: 已经实名企业新手任务过滤主体filterSubject为true
     api: api/Novice-task/Novice-task-list/tasks_list_1.yml
     variables:
          filterSubject: true
          accountId: ea964ce78b2d4018b9fd8ec996ae1daf
          tenantId: 0b928e1dcb0f4592b88a724bd120a630
     validate:
         - eq: ["content.code", 0]
         - contains: ["content.message", 成功]
