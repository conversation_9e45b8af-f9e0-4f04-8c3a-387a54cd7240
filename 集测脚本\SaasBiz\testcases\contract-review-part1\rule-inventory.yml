- config:
    name: 审查清单操作
    variables:
        appId: "7876701657"
        groupId: "94b05d2978bc41d2bcdcfda8be5216d6"
        inventoryId1: "4dd54f5584d9405295b6adf4942011ad"
        ruleId1: "5e0be97fd4c24260a093c013db964204"

- test:
    name: 审查清单列表
    api: api/contract-review-part1/rule-inventory/rule-inventory-page.yml
    variables:
        appId: $appId
    extract:
        inventoryList: content.data.inventoryList
    validate:
        - eq: ["content.code", 0]
        - ne: ["${getLength($inventoryList)}", 0]
        - eq: ["content.message", 成功]
- test:
    name: 审查清单详情
    api: api/contract-review-part1/rule-inventory/rule-inventory-detail.yml
    variables:
        appId: $appId
        inventoryId: $inventoryId1
    validate:
      - eq: ["content.data.inventoryName",集测勿动]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
- test:
    name: 创建审查清单
    api: api/contract-review-part1/rule-inventory/rule-inventory-save.yml
    variables:
      appId: $appId
      ruleId: $ruleId1
    extract:
      inventoryId2: content.data.inventoryId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除审查清单
    api: api/contract-review-part1/rule-inventory/rule-inventory-del.yml
    variables:
      appId: $appId
      inventoryId: $inventoryId2
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]





