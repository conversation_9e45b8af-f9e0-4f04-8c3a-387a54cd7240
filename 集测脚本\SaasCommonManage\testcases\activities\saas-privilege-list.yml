- config:
    name: 运营支撑平台得权限角色&操作
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 运营支撑获取saas所有权限
    api: api/activities/saas-privilege-crmlist.yml
    extract:
      - targetClassKey1: content.data.saasPrivilegeList.0.targetClassKey
      - targetClassKey2: content.data.saasPrivilegeList.1.targetClassKey
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", '成功']


- test:
    name: 运营支撑设置saas所有权限
    api: api/activities/saas-privilege-set.yml
    variables:
      operatorName: '青莲'
      privilegeConfigList:
       -
          targetClassKey: targetClassKey1
          weight: 10
       -  targetClassKey: targetClassKey2
          weight: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", '成功']

- test:
    name: saas获取所有权限
    api: api/activities/saas-privilege-list.yml
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", '成功']
