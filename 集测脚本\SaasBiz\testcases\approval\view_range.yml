- config:
    name: 获取可见范围


- test:
    name: 发起获取符合条件的模版
    api: api/approval-template/approval-template-available.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
#      approvalId : "c2efb02302c241cf94c0df67cfb22a60"
      approvalTemplateType: 2
      approvalTemplateConditionType: 0
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.list.0.approvalTemplateName", 仅兰生可见]