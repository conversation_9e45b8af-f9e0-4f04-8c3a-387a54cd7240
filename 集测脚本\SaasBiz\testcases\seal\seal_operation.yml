- config:
    name: 印章具体操作
    variables:
      accountId1: ${ENV(nss_accountId1)}
      account1: ***********
      accountName1: 隆多
      accountId2: ${ENV(nss_accountId2)}
      orgId1: ${ENV(nss_orgid1)}
      sealId1: 15e2446b-8109-4c77-8ac2-f97f6e720a56
      flowtemplateId1: 0f8241dcc5e24395bad33f4f6445bfba
      sealName1: 企业人事章${getTimeStamp_ms()}
      db_name1: seal
      effectiveTime: "${getTimeStamp_ms()}"
      expireTime: "${get_next_year_timestamp()}"


#创建企业模板印章
- test:
    name: 创建企业模板印章-人事章
    variables:
        tenantid: $orgId1
        alias: $sealName1
        bottomText: ren
        color: RED
        horizontalText: shi
        opacity: 80
        style: NONE
        surroundTextInner: ${getTimeStamp_ms()}
        templateType: PERSONNEL_ROUND_STAR
        widthHeight: "38_38"
    api: api/seal/add_organizationstemplate.yml
    extract:
        - Cu_sealId: content.data.sealId
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

#新增印章授权
#- test:
#    name: 印章授权-授权印章审批权限
#    variables:
#        tenantid: $orgId1
#        operatorid: $accountId2
#        appScheme: null
#        autoFall: null
#        effectiveTime: ${getTimeStamp_ms()}
#        expireTime: ${get_next_year_timestamp()}
#        fallType: null
#        grantRedirectUrl: ""
#        grantType: 1
#        grantedAccountIds:
#           - $accountId1
#        grantedUserCode: null
#        grantedUserName: null
#        grantedUserRole: null
#        h5: null
#        notifySetting: false
#        notifyUrl: ""
#        orgId: $orgId1
#        resourceId: $Cu_sealId
#        roleKey: "SEAL_EXAMINER"
#        scopeList:
#           - $flowtemplateId1
#        token: ""
#        clientId: "WEB"
#    api: api/seal/add-rule-grants.yml
#    extract:
#        - ruleGrantId1: content.data.ruleGrantIds.0
#    validate:
#        - eq: ["content.code",0]
#        - contained_by: ["content.message", ["成功","缺少参数"]]
- test:
    name: 印章授权-授权印章审批权限1
    variables:
        json: {
          "effectiveTime": "$effectiveTime",
          "expireTime": "$expireTime",
          "grantType": 1,
          "grantedAccountIds": ["b76df3c9dc064bdea11220186179e3dc"],
          "grantedRoleIds": [],
          "grantedDepartmentIds": [],
          "grantedResourceIds": [$Cu_sealId],
          "roleKey": "SEAL_EXAMINER",
          "scopeList": ["ALL"],
          "scopeApprovalTemplateIds": [],
          "scopeAppIds": [],
          "fallType": 0,
          "notifySetting": true,
          "authConfirmMethod": 2,
          "sealOwnerOid": "52b72c6d9ac941bebdd0431d97f2f8ab",
          "grantLevel": 1,
          "resourceOwnerAdminOid": "691e9d2a1aae49929cf9c2b446a1e157",
          "resourceOwnerOid": "52b72c6d9ac941bebdd0431d97f2f8ab",
          "onlyApprovalNotUse": true
        }

    api: api/seal/seal_batch_grant.yml
#    extract:
#        - ruleGrantId1: content.data.ruleGrantIds.0
    validate:
        - eq: ["content.code",0]
        - eq: ["content.message", "成功"]

#- test:
#    name: 根据分组查询印章授权记录
#    variables:
#      - OperatorId: 691e9d2a1aae49929cf9c2b446a1e157
#      - offset: 1
#      - size: 10
#      - groupId : 124
#    api: api/seal/query-seal-grant-group-detail.yml
##    extract:
##        - ruleGrantId1: content.data.ruleGrantIds.0
#    validate:
#        - eq: ["content.code",0]
#        - eq: ["content.message", "成功"]



#- test:
#    name: 批量删除印章授权
#    variables:
#        ruleGrantIds:
#        - $ruleGrantId1
#        tenantid: $orgId1
#    api: api/seal/del-batch-grants.yml
#    validate:
#        - eq: ["status_code",200]
#        - contains: ["content.message","成功"]

- test:
    name: 查询印章授权列表
    variables:
      - OperatorId: 691e9d2a1aae49929cf9c2b446a1e157
      - ruleGrantStatus: "ALL"
      - type: "1"
      - resourceId: "$Cu_sealId"
      - orgId: "52b72c6d9ac941bebdd0431d97f2f8ab"
      - sealOwnerOid: "52b72c6d9ac941bebdd0431d97f2f8ab"
      - "offset": 0
      - "size": 20
    api: api/seal/rule-grant-list.yml
    extract:
        - ruleGrantId1: content.data.grantList.0.ruleGrantId
    validate:
        - eq: ["content.code",0]
        - eq: ["content.message", "成功"]

- test:
    name: 编辑印章授权
    variables:
        json: {
          "effectiveTime": "$effectiveTime",
          "expireTime": "$expireTime",
          "grantType": 1,
          "grantedAccountIds": ["b76df3c9dc064bdea11220186179e3dc"],
          "grantedRoleIds": [],
          "grantedDepartmentIds": [],
          "grantedResourceIds": [$Cu_sealId],
          "roleKey": "SEAL_EXAMINER",
          "scopeList": ["ALL"],
          "scopeApprovalTemplateIds": [],
          "scopeAppIds": [],
          "fallType": 0,
          "notifySetting": true,
          "authConfirmMethod": 2,
          "sealOwnerOid": "52b72c6d9ac941bebdd0431d97f2f8ab",
          "grantLevel": 1,
          "resourceOwnerAdminOid": "691e9d2a1aae49929cf9c2b446a1e157",
          "resourceOwnerOid": "52b72c6d9ac941bebdd0431d97f2f8ab",
          "onlyApprovalNotUse": true,
          "ruleGrantedId": "$ruleGrantId1"
        }

    api: api/seal/do-update-rule-grant.yml
#    extract:
#        - ruleGrantId1: content.data.ruleGrantIds.0
    validate:
        - eq: ["content.code",0]
        - eq: ["content.message", "成功"]

- test:
    name: 设置默认印章
    variables:
        sealId: $Cu_sealId
        tenantid: $orgId1
    api: api/seal/set-default-seal.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","成功"]


- test:
    name: 设置默认印章-重置状态
    variables:
        sealId: $sealId1
        tenantid: $orgId1
    api: api/seal/set-default-seal.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","成功"]

- test:
    name: 删除印章rpc接口-清数据
    variables:
        sql1: "SELECT uuid FROM seal.seal_ref where oid='$orgId1' and name='$sealName1' and status=1;"
        Rpc_sealId: ${select_sql($sql1, $db_name1)}
    api: api/seal/deleteSeal_rpc.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","执行成功"]
    teardown_hooks:
        - ${hook_sleep_n_secs(3)}

- test:
    name: 查询印章授权列表
    variables:
        orgId: "bfcd509c78214873aacbf3329c595176"
        sealId: "9ef83c60-567f-4ead-9345-5d69bfd13d01"
        sealOwnerOid: "bfcd509c78214873aacbf3329c595176"
    api: api/seal/get-rule-granted-list.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","成功"]