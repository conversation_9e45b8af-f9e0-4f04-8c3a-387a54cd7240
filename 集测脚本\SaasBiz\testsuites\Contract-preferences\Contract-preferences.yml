config:
  name: 合同偏好设置相关接口


testcases:
  -
    name: 获取合同偏好设置
    testcase: testcases/Contract-preferences/query-preferences.yml

  -
    name: 保存用户合同偏好设置
    testcase: testcases/Contract-preferences/save-preference.yml
  -
    name: 页数余额查询
    testcase: testcases/contract-ledger/ai-page-balance.yml
  -
    name: 关闭待确认体验完成台账通知
    testcase: testcases/contract-ledger/close-toBeConfirmed-trial.yml
  -
    name: 继续运行台账
    testcase: testcases/contract-ledger/continue-run.yml
  -
    name: 查询最后一次台账运行信息
    testcase: testcases/contract-ledger/last-run-info.yml
  -
    name: 获取下一条台账提取记录
    testcase: testcases/contract-ledger/next-extract-process.yml
  -
    name: 查询合同抽取结果
    testcase: testcases/contract-ledger/process-extract-data.yml
  -
    name: 查询最后一次体验完成台账
    testcase: testcases/contract-ledger/toBeConfirmed-trial.yml
  -
    name: 试运行结果合同列表
    testcase: testcases/contract-ledger/trial-processes.yml
  -
    name: 修改台账ai提取字段
    testcase: testcases/contract-ledger/update-form-aiField.yml
  -
    name: 获取台账合同url
    testcase: testcases/contract-ledger/process-file-url.yml
  -
    name: 查询是否存在欠费的提取记录
    testcase: testcases/offline_contract/has-arrears-extract.yml
