- config:
    name: 修改水印模板状态 (启/停)


- test:
    name: 修改水印模板状态 (启/停)-不存在的水印模板id
    api: api/watermark-template/change_watermark-template_status.yml
    variables:
      watermarkId: 1
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code",  ********]
      - eq: ["content.message", "水印模板不存在或已被删除"]

- test:
    name: 修改水印模板状态 (启/停)-文件水印
    api: api/watermark-template/change_watermark-template_status.yml
    variables:
      watermarkId: 4efaea0251b14dc88d674a4573a5b4b4
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code",  0]
      - eq: ["content.message", "成功"]

- test:
    name: 修改水印模板状态 (启/停)-屏幕水印
    api: api/watermark-template/change_watermark-template_status.yml
    variables:
      watermarkId: f47f34a0d73141adbd270583564dc2ba
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code",  0]
      - eq: ["content.message", "成功"]
