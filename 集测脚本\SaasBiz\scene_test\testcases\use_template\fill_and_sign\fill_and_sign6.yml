- config:
    name: 发起填写+填写人在相对方黑名单
    variables:
      tenantId_1: ${ENV(mx_orgId1)}                       # 当前空间主体
      operatorId_1: ${ENV(ls_oid)}                        # 操作人oid
      initiatorAccountId: ${ENV(ls_oid)}                  # 发起人的oid
      account_1: ${ENV(ls_account3)}                       # 参与人手机号
      account_2: ${ENV(mx_account2)}
      accountOid_1: ${ENV(ls_oid3)}                        # 参与人oid
      accountOid_2: ${ENV(mx_accountId2)}
      accountName_1: ${ENV(ls_accountName3)}               # 参与人姓名
      accountName_2: ${ENV(mx_accountName2)}
      subject_1: ${ENV(ls_orgId1)}                        # 参与人主体oid
      subject_2: ${ENV(mx_accountId2)}
      subjectName_1: ${ENV(ls_orgName1)}                    # 参与人的主体名称
      subjectName_2: ${ENV(mx_accountName2)}
      fileId_1: 8b843fd6c90d4c87a888061cced947dd             # 模板的文件id
      file_name1: 劳动合同.pdf                               # 文件名称
      fileId_2: b52cfad6b6294daf91cfa41d23aac844             # 附件id
      file_name2: 测试的文件.docx                            # 附件名称
      taskName: 填写人在相对方黑名单${generate_random_str(3)}
      flowTemplateId: ${ENV(ls_flowTemplateId_8)}                   # 流程模板ID
      approveTemplateId: ""                                       # 合同审批ID
      
      
- test:
    name: 获取流程发起的详情信息
    api: api/contract_manage/processDetail.yml
    variables:
      tenantId: $tenantId_1
      operatorId: $operatorId_1
      flowTemplateId: $flowTemplateId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - participantId1: content.data.participants.0.participantId
      - participantId2: content.data.participants.1.participantId
      
- test:
    name: 使用模板发起填写流程(填写人在相对方黑名单)
    api: api/contract_manage/startProcess.yml
    variables:
      tenantId: $tenantId_1
      operatorId: $operatorId_1
      businessType: 0
      ccs: []
      files:
        [
          {
            "fileId": $fileId_1,
            "fileType":1,             #文件类型：1-合同文件 2-附件
            "fileName": $file_name1,
            "from":2,                 #文件来自：1-模板文件 2-合同文件
            "fileSecret":false        #文件是否保密
          },
          {
            "fileId": $fileId_2,
            "fileType":2,
            "fileName": $file_name2,
            "from":2,
            "fileSecret":false
          }
        ]
      flowTemplateId: $flowTemplateId
      initiatorAccountId: $initiatorAccountId
      approveTemplateId: $approveTemplateId
      signEndTime: ""
      fileEndTime: ""
      signValidityConfig:
        {
         "validityType":3,
         "durationYear":null,
         "durationMonth":null,
         "durationDay":null
        }
      fileValidityConfig:
        {
         "validityType":3,
         "durationYear":null,
         "durationMonth":null,
         "durationDay":null
        }
      participants:
        [
        {
          "participantSubjectType": 1,
          "role": "3,1",
          "sealType": null,
          "signRequirements": "3,1",
          "roleSet":1,
          "type": 1,
          "signSealType": 1,
          "fillOrder": 1,
          "signOrder": 1,
          "participantLabel":"签署方1",
          "participantId": $participantId1,
          "instances":[
          {
            "account": $account_1,
            "accountOid": $accountOid_1,
            "accountName": $accountName_1,
            "accountRealName":true,
            "comment":"",
            "subjectId": $subject_1,
            "subjectName": $subjectName_1,
            "subjectRealName":true,
            "subjectType": 1,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[
            "EMAIL",
            "FACE",
            "CODE_SMS",
            "SIGN_PWD"
          ]
        },
        {
          "participantSubjectType": 0,
          "role": "3,1",
          "sealType": "1",
          "signRequirements": null,
          "roleSet":1,
          "type": 1,
          "signSealType": 1,
          "fillOrder": 2,
          "signOrder": 1,
          "participantLabel":"签署方2",
          "participantId": $participantId2,
          "instances":[
          {
            "account": $account_2,
            "accountOid": $accountOid_2,
            "accountName": $accountName_2,
            "accountRealName":true,
            "comment":"",
            "subjectId": $subject_2,
            "subjectName": $subjectName_2,
            "subjectRealName":true,
            "subjectType": 0,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[
            "CODE_SMS",
            "FACE",
            "EMAIL",
            "SIGN_PWD"
          ]
        }
        ]
      scene: 2
      taskName: $taskName
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]
    teardown_hooks:
      - ${hook_sleep_n_secs(10)}