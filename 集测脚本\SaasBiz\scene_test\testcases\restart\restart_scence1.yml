- config:
    name: 重新发起流程场景一：模板发起流程校验填写数据
    variables:
      accountId1: ${ENV(mx_accountId1)}
      account1: ${ENV(mx_account1)}
      accountName1: ${ENV(mx_accountName1)}
      accountId2: ${ENV(mx_accountId2)}
      account2: ${ENV(mx_account2)}
      accountName2: ${ENV(mx_accountName2)}
      orgId1: ${ENV(mx_orgId1)}
      orgName1: ${ENV(mx_orgName1)}
      signSeal1: 6f9c25da-54dd-40c5-8fb0-444b3084db57
      flowTemplateId1: 090c61dcf86f43678e66ca3d4b9b8c5e
      taskName1: 使用模板发起-${getTimeStamp_ms()}


- test:
    name: 流程发起详情信息
    api: api/contract_manage/processDetail.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - participantId1: content.data.participants.0.participantId
      - participantId2: content.data.participants.1.participantId
      - fileId1: content.data.files.0.fileId
      - fileName1: content.data.files.0.fileName

- test:
    name: 使用模板发起
    api: api/contract_manage/startProcess.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      businessType: 0
      ccs: [
      {
        "account": $account1,
        "accountOid": $accountId1,
        "accountName": $accountName1,
        "accountNick":"",
        "accountRealName":true,
        "comment":"",
        "subjectId": $orgId1,
        "subjectName": $orgName1,
        "subjectRealName":true,
        "subjectType":1
      }
      ]
      files:
        [
        {
          "fileId": $fileId1,
          "fileType":1,
          "fileName": $fileName1,
          "from":2,
          "fileSecret":false
        }
        ]
      flowTemplateId: $flowTemplateId1
      initiatorAccountId: $accountId1
      participants:
        [
        {
          "participantSubjectType":1,
          "role":"1,3",
          "sealType":null,
          "signRequirements":"1",
          "roleSet":0,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":$participantId1,
          "instances":[
          {
            "account":$account1,
            "accountOid":$accountId1,
            "accountName":$accountName1,
            "accountRealName":true,
            "comment":"",
            "subjectId":$orgId1,
            "subjectName":$orgName1,
            "subjectRealName":true,
            "subjectType":1,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[
            "FACE",
            "CODE_SMS",
            "EMAIL",
            "SIGN_PWD"
          ],
          "type":1,
          "signSealType":3,
          "signSeal": $signSeal1,
          "forceReadEnd":true,
          "forceReadTime":18
        },
        {
          "participantSubjectType":0,
          "role":"3",
          "sealType":"0,1,2",
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":2,
          "signOrder":1,
          "participantLabel":"签署方2",
          "participantId": $participantId2,
          "instances":[
          {
            "account": $account2,
            "accountOid": $accountId2,
            "accountNick":"",
            "accountName": $accountName2,
            "accountRealName":true,
            "comment":"",
            "subjectId": $accountId2,
            "subjectName": $accountName2,
            "subjectRealName":true,
            "subjectType":0,
            "preFillValues":null,
            "fileValues":null,
            "subTaskName":"",
            "subTaskBizId":"",
            "subTaskBizType":""
          }
          ],
          "willTypes":[

          ],
          "type":1,
          "signSealType":1,
          "signSeal":"",
          "forceReadEnd":false,
          "forceReadTime":""
        }
        ]
      scene: 2
      taskName: $taskName1
      signEndTime: *************
      fileEndTime: *************
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - cooperationId1: content.data.cooperationId
      - processId1: content.data.processId

- test:
    name: 获取协作流程详情
    api: api/footstone-doc/getCooperationInfo.yml
    variables:
      cooperationId: $cooperationId1
      loginAccountId: null
      menuId: null
      queryAccountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - cooperationerId1: content.data.cooperationers.0.cooperationerId

- test:
    name: 根据协作方获取结构项数据
    api: api/footstone-doc/doccooperations/getComponents.yml
    variables:
      cooperationId: $cooperationId1
      cooperationerId: $cooperationerId1
      menuId: null
      queryAccountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - templateId1: content.data.cooperationTaskStructsList.1.cooperationStucts.0.templateId
      - structId1: content.data.cooperationTaskStructsList.1.cooperationStucts.0.structId  #单行文本
      - structId2: content.data.cooperationTaskStructsList.1.cooperationStucts.1.structId  #多行文本
      - structId3: content.data.cooperationTaskStructsList.1.cooperationStucts.2.structId  #数字
      - structId4: content.data.cooperationTaskStructsList.1.cooperationStucts.3.structId  #日期
      - structId5: content.data.cooperationTaskStructsList.1.cooperationStucts.4.structId  #单选
      - structId6: content.data.cooperationTaskStructsList.1.cooperationStucts.5.structId  #多选
      - structId7: content.data.cooperationTaskStructsList.1.cooperationStucts.6.structId  #勾选
      - structId8: content.data.cooperationTaskStructsList.1.cooperationStucts.7.structId  #图片

- test:
    name: 提交填写
    api: api/footstone-doc/commitContents.yml
    variables:
      cooperationId: $cooperationId1
      cooperationerId: $cooperationerId1
      contentDatas:
        [
        {
          "content":"测试",
          "structId": $structId1,
          "templateId": $templateId1
        },
        {
          "content":"test\n哈哈",
          "structId": $structId2,
          "templateId": $templateId1
        },
        {
          "content":"123",
          "structId": $structId3,
          "templateId": $templateId1
        },
        {
          "content":"${today_date()}",
          "structId": $structId4,
          "templateId": $templateId1
        },
        {
          "content":0,
          "structId": $structId5,
          "templateId": $templateId1
        },
        {
          "content":"[1,2,3]",
          "structId": $structId6,
          "templateId": $templateId1
        },
        {
          "content":false,
          "structId": $structId7,
          "templateId": $templateId1
        },
        {
          "content":"ddb7d784b9d843dd925e846eb714b003",
          "structId": $structId8,
          "templateId": $templateId1
        },
        ]
      operateAccountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
#    extract:
#      - flowId1: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 重新发起获取回填信息校验（企业空间下）-模板发起流程
    api: api/contract_manage/backfill.yml
    variables:
      processId: $processId1
      tenantId: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.ccs.0.accountOid", $accountId1]   #校验抄送人
      - eq: ["content.data.ccs.0.subjectId", $orgId1]   #校验抄送人
      - eq: ["content.data.participants.0.instances.0.subjectId", $orgId1]  #校验参与方
      - eq: ["content.data.files.0.fileId", $fileId1]   #文件
      - ne: ["content.data.participants.0.instances.0.preFillValues", null]  #校验填写控件回填信息
      - eq: ["content.data.participants.0.instances.0.preFillValues.913d3d3525b34b1096c7836ee4a9eab3.3", "2"]  #校验填写控件回填信息
      - len_eq:                                        #校验意愿认证方式
          - content.data.participants.0.instances.0.preFillValues
          - 9
      - contains:
          - content.data.participants.0.instances.0.preFillValues.ade961b8803a468a9cf796abcb495cd6  #校验控件预填写信息
          - 测试
      - eq: ["content.data.participants.0.signSeal", $signSeal1]  #校验指定印章
      - eq: ["content.data.participants.0.forceReadEnd", true]  #校验指定阅读到底
      - eq: ["content.data.participants.0.forceReadTime", 18]  #校验阅读时长
      - len_eq:                                        #校验意愿认证方式
          - content.data.participants.0.willTypes
          - 4
      - contains:
          - content.data.participants.0.willTypes
          - CODE_SMS

- test:
    name: 撤回流程
    api: api/contract_manage/process_operation/process_batchRevoke.yml
    variables:
      processIds: [$processId1]
      accountId: $accountId1
      subjectId: $orgId1
      reason: 撤回流程测试
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]