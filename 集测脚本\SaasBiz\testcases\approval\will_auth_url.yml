- config:
    name: 获取审批意愿认证地址


- test:
    name: 获取审批意愿认证地址--成功
    api: api/approval/will_auth_url.yml
    variables:
      tenantId: 80ff3354fac947cdae1555fc4cc25c7f
      operatorId: 80ff3354fac947cdae1555fc4cc25c7f
      json:
        {
          "accountId": "80ff3354fac947cdae1555fc4cc25c7f",
          "bizType": "SIGN",
          "redirectUrl": "https://testfront.tsign.cn:8887/approve-manage-front/approve/mobile/list?
          context=t58cGac&client_type=WE_CHAT&start_time=*************&event_name=front_init_time_consume",
          "approvalTask": {
            "processId": "a68f6d572d01492797213dff19a111b5",
            "approvalId": "AF-1c6a0c5f03334c0bb6e203cd22358586",
            "taskId": "597c4abb-e276-11ee-88bd-7ed6842df62c"
          }
        }
    validate:
      - eq: [ "status_code", 200 ]
#      - eq: [ "content.code", 0 ]
#      - eq: [ "content.message", 成功 ]

- test:
    name: 获取审批意愿地址_企业在ca证书的灰度中
    api: api/approval/will_auth_url.yml
    variables:
      tenantId: 08486d61823d4c2086122b26fb3e615a
      operatorId: 0bc26b27ede64cc48e7a800f4c5cbac7
      json:
        {
          "approvalTasks": [ {
            "approvalCode": "AF-cda001f605f040c297def4bc394e2f72",
            "taskId": "b71a7414-ef79-11ef-a117-f608665cd599"
          } ],
          "redirectUrl": "http://web-treaty-front-2-esignca.projectk8s.tsign.cn/approve-manage/list?
          queryType=3&approveStartType=1&processId=&partType=1&partInputVal=",
          "appScheme": "",
          "origin": "BROWSER",
          "willTypeHided": "5"
        }
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.data.needAuth", true ]