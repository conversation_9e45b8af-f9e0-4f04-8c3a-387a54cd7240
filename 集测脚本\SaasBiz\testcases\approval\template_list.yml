- config:
    name: 审批模版列表（完成）



- test:
    name: 审批模版列表
    api: api/approval/template_list.yml
    variables:
      operatorId : 565a742760cc485185bbd3cfc1e47e80
      tenantId : d9ec3169a6e04d148e5a8cc08ab3c13d
      pageNum : 1
      pageSize: 10
      approvalTempateName: 测试
      approvalType: 2
      approvalTempalteStatus: 1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 审批模版列表
    api: api/approval/template_list.yml
    variables:
      operatorId : 565a742760cc485185bbd3cfc1e47e80
      tenantId : d9ec3169a6e04d148e5a8cc08ab3c13d
      pageNum : 1
      pageSize: 10
      approvalTempateName: 测试1
      approvalType: 1
      approvalTempalteStatus: 1


    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 审批模版列表
    api: api/approval/template_list.yml
    variables:
      operatorId : 565a742760cc485185bbd3cfc1e47e80
      tenantId : d9ec3169a6e04d148e5a8cc08ab3c13d
      pageNum : 1
      pageSize: 10
      approvalTempateName: 测试1
      approvalType: 1
      approvalTempalteStatus: 0


    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
