- config:
    name: 查询功能介绍信息
#    base_url: ${ENV(base_url)}


- test:
    name: 查询功能介绍信息-functionCode不存在
    api: api/vipmanage/function-illustrate.yml
    variables:
      functionCode: 123
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.docContent", null]

- test:
    name: 查询功能介绍信息-信息采集器功能
    api: api/vipmanage/function-illustrate.yml
    variables:
      functionCode: info_collect
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.docTitle", 信息采集器]
      - ne: ["content.data.docContent", null]
