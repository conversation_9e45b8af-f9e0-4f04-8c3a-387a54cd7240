- config:
    name: 根据orgId查询企业信息
    variables:
      orgId1: ${ENV(orgId1)}
      accountId1: ${ENV(accountId1)}  #管理员
      roleId1: cd6ff6f563f746bba93ecf56e7928873
      accountId2: 74ada1674a4d441eb0120c789f1b9300  #普通员工

- test:
    name: 根据orgId查询企业信息-operatorId为空
    api: api/organizations/queryInfo_byOrgId.yml
    variables:
      orgId: $orgId1
      operatorId: ""
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 鉴权用户或主体账号缺失]

- test:
    name: 根据orgId查询企业信息-orgId不存在
    api: api/organizations/queryInfo_byOrgId.yml
    variables:
      orgId: 123
      operatorId: $accountId1
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 请求访问失败：鉴权用户主体不存在]
#      - eq: ["content.data.subjectName", null]

- test:
    name: 根据orgId查询企业信息-管理员
    api: api/organizations/queryInfo_byOrgId.yml
    variables:
      orgId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.subjectName", null]
      - eq: ["content.data.canBuy", true]

- test:
    name: 给批量成员增加角色-企业账户购买权限
    api: api/footstone-user-api/grantRoles.yml
    variables:
      orgId: $orgId1
      memberIds: ["$accountId2"]
      roleIds: ["$roleId1"]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 根据orgId查询企业信息-普通员工有企业账户购买权限
    api: api/organizations/queryInfo_byOrgId.yml
    variables:
      orgId: $orgId1
      operatorId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.subjectName", null]
      - eq: ["content.data.canBuy", true]

- test:
    name: 给批量成员取消角色
    api: api/footstone-user-api/revokeRole.yml
    variables:
      orgId: $orgId1
      memberIds: ["$accountId2"]
      roleId: $roleId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 根据orgId查询企业信息-普通员工无企业账户购买权限
    api: api/organizations/queryInfo_byOrgId.yml
    variables:
      orgId: $orgId1
      operatorId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.subjectName", null]
      - eq: ["content.data.canBuy", false]