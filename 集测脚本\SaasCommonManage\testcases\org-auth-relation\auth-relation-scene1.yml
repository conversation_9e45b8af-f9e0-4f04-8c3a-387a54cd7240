- config:
    name: 关联组织串场景-关联关系A-B-C-D
    variables:
      accountId1: 475955db49aa4289a8cb9422e200988c
      orgId1: 63e3c58149c04317bf521b7a20af0588
      orgName1: esigntest集测多组织测试企业A1
      orgId2: 17aea5c0b95041e3aa34512fcdb61e4b
      orgName2: esigntest集测多组织测试企业B1
      orgCode2: 91000000891644932X
      orgId3: 79ca0187cafd43c690b9c07fce40b9da
      orgName3: esigntest集测多组织测试企业C1
      orgCode3: 9100000089228976H4
      orgId4: 3aca3907069f40bc93001b368c0fc635
      orgName4: esigntest集测多组织测试企业D1
      orgCode4: 9100000089283443MU
      fileKey1: ${ENV(fileKey_1)}
      orgId5: 4f7d013b5f0c42f5b76e3dd3bc439cb1
      orgName5: esigntest集测多组织测试企业E1
      orgCode5: 91000000893491660F

- test:
    name: 判断是否可作为授权企业-校验企业A
    api: api/org-auth-relation/check-auth-tenant.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", true]

- test:
    name: 获取批量任务进度-无任务
    api: api/org-auth-relation/batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]

- test:
    name: 获取订单信息
    api: api/org-auth-relation/effective-products.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.0.effectiveEndTime", "${getTimeStamp_ms()}"]
    extract:
      - orderId1: content.data.0.orderId
      - margin1: content.data.0.margin

- test:
    name: 查询关联企业列表-无待授权、待生效、生效中的关联关系
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: null
      authRelationStatusList: "1,2,6"
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]

- test:
    name: 添加前获取添加条件
    api: api/org-auth-relation/auth-relation-config.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.batchAddExcelUrl", null]
      - gt: ["content.data.onlineMaxAddCount", 0]
    extract:
      - authReason1: content.data.authReasonList.0.value
      - authReason2: content.data.authReasonList.0.value
      - authReason3: content.data.authReasonList.0.value

- test:
    name: 添加关联企业前校验企业是否符合规则-A-B校验通过
    api: api/org-auth-relation/check-tenant-when-add.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      parentTenantOid: $orgId1
      childTenantList:
        [
        {
          "orderId": "$orderId1",
          "authResources":[
            "100","400","300"
          ],
          "shareConfigs":[

          ],
          "childTenantName":"$orgName2",
          "childUsccCode":"$orgCode2",
          "childTenantOid":"$orgId2",
          "secondAuthResource": {
            "300": [
              "deleteManage"
            ]
          }
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.pass", true]

- test:
    name: 添加关联组织-A-B
    api: api/org-auth-relation/add-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authReason: $authReason1
      parentTenantOid: $orgId1
      childTenantList:
        [
        {
          "orderId": "$orderId1",
          "authResources":[
            "100","400","300"
          ],
          "secondAuthResource": {
            "300": [
              "deleteManage"
            ]
          },
          "shareConfigs":[

          ],
          "childTenantName":"$orgName2",
          "childUsccCode":"$orgCode2",
          "childTenantOid":"$orgId2"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]

- test:
    name: 获取批量任务进度-有进行中的任务
    api: api/org-auth-relation/batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data", null]

- test:
    name: 执行批量添加授权任务
    api: api/org-auth-relation/testBatchTask.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content", success]

- test:
    name: 获取批量任务进度-任务完成发起成功
    api: api/org-auth-relation/batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      url1: "/v2/org-auth-relation/batch-add-task"
      params1:
        {

        }
      headers1:
        {
          "X-Tsign-Open-App-Id": "${ENV(appid)}",
          "X-Tsign-Service-Group": "${ENV(Groupid)}",
          "X-Tsign-Open-Auth-Mode": simple,
          "X-Tsign-Open-Tenant-Id": $orgId1,
          "X-Tsign-Open-Operator-Id": $accountId1
        }
    setup_hooks:
      - ${waitTillDone2($url1,$params1,$headers1,5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]

- test:
    name: 获取订单信息-验证订单商品数-1
    api: api/org-auth-relation/effective-products.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    extract:
      - margin2: content.data.0.margin
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["${minus($margin1,$margin2)}", 1]

- test:
    name: 处理删除过完成的任务
    api: api/org-auth-relation/delete-batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询关联企业列表-A下查询下级组织B，状态为待授权
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 1]
    extract:
      - authRelationId1: content.data.list.0.authRelationId

- test:
    name: 查询关联企业授权记录列表
    api: api/org-auth-relation/auth-relation-log-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      authRelationId: $authRelationId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - eq: ["content.data.list.0.authRelationStatus", 1]

- test:
    name: 查询关联企业列表-B下查询上级组织A，状态为待授权
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName1
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 1]

- test:
    name: 查询授权关系最新的签署信息
    api: api/org-auth-relation/get-auth-relation-last-process.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId1
      authRelationLogId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - processId1: content.data.processId
      - flowId1: content.data.flowId

- test:
    name: 查询签署流程详情
    api: api/footstone/flowDetail.yml
    variables:
      flowId: $flowId1
      authorizerIds: ${str_append($orgId1,$orgId2)}
      queryAccountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - signfields1: content.data.signDocs.0.signfields

- test:
    name: 查询签署可用印章列表-获取企业A的印章
    api: api/footstone/getSignSeals.yml
    variables:
      flowId: $flowId1
      accountId: $accountId1
      batchSign: false
      signerAccountId: $accountId1
      authorizerIds: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - sealId1: content.data.officialSeals.0.organSeals.0.sealId

- test:
    name: 查询签署可用印章列表-获取企业B的印章
    api: api/footstone/getSignSeals.yml
    variables:
      flowId: $flowId1
      accountId: $accountId1
      batchSign: false
      signerAccountId: $accountId1
      authorizerIds: $orgId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - sealId2: content.data.officialSeals.0.organSeals.0.sealId

- test:
    name: 发起验证码
    api: api/footstone/sendSms.yml
    variables:
      accountId: $accountId1
      flowIds: [$flowId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - bizId1: content.data.bizId

- test:
    name: 校验验证码认证
    api: api/footstone/checkSms.yml
    variables:
      accountId: $accountId1
      authCode: 123456
      bizId: $bizId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.passed", true]

- test:
    name: 提交签署
    api: api/footstone/add-Update-Execute.yml
    variables:
      flowId: $flowId1
      accountId: $accountId1
      addSignfields: []
      async: true
      signfieldIds: []
      signfieldId1: ${getValue($signfields1,authorizedAccountId,$orgId1,signfieldId)}
      posBean1: ${getValue($signfields1,authorizedAccountId,$orgId1,posBean)}
      signfieldId2: ${getValue($signfields1,authorizedAccountId,$orgId2,signfieldId)}
      posBean2: ${getValue($signfields1,authorizedAccountId,$orgId2,posBean)}
      updateSignfields:
        [
        {
          "signfieldId": $signfieldId1,
          "sealId": $sealId1,
          "signerOperatorAuthorizerId": $orgId1,
          "signerOperatorId": $accountId1,
          "authorizedAccountId": $orgId1,
          "crossEnterpriseSeal": false,
          "fieldType": 0,
          "posBean": $posBean1
        },
        {
          "signfieldId": $signfieldId2,
          "sealId": $sealId2,
          "signerOperatorAuthorizerId": $orgId2,
          "signerOperatorId": $accountId1,
          "authorizedAccountId": $orgId2,
          "crossEnterpriseSeal": false,
          "fieldType": 0,
          "posBean": $posBean2
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 查询关联企业列表-A下查询下级组织B，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    setup_hooks:
      - ${hook_sleep_n_secs(3)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]

- test:
    name: 查询关联企业授权记录列表
    api: api/org-auth-relation/auth-relation-log-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      authRelationId: $authRelationId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - eq: ["content.data.list.0.authRelationStatus", 2]

- test:
    name: 查询有效关联企业列表
    api: api/org-auth-relation/search-effective-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      searchTenantName: null
      bizScene: null
      querySelf: false
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.childTenantName", $orgName2]
      - eq: ["content.data.list.0.childTenantOid", $orgId2]

- test:
    name: 判断是否可作为授权企业-校验企业A
    api: api/org-auth-relation/check-auth-tenant.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", true]

- test:
    name: 判断是否可作为授权企业-校验企业B
    api: api/org-auth-relation/check-auth-tenant.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", false]

- test:
    name: 添加关联企业前校验企业是否符合规则-A-B-C校验通过
    api: api/org-auth-relation/check-tenant-when-add.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      parentTenantOid: $orgId2
      childTenantList:
        [
        {
          "orderId": "$orderId1",
          "authResources":[
            "100",
            "200"
          ],
          "shareConfigs":[
            "shareVip"
          ],
          "childTenantName":"$orgName3",
          "childUsccCode":"$orgCode3",
          "childTenantOid":"$orgId3"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.pass", true]

- test:
    name: 添加关联组织-A-B-C
    api: api/org-auth-relation/add-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authReason: $authReason2
      parentTenantOid: $orgId2
      childTenantList:
        [
        {
          "orderId": "$orderId1",
          "authResources":[
            "100",
            "200"
          ],
          "shareConfigs":[
            "shareVip"
          ],
          "childTenantName":"$orgName3",
          "childUsccCode":"$orgCode3",
          "childTenantOid":"$orgId3"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]

- test:
    name: 获取批量任务进度-有进行中的任务
    api: api/org-auth-relation/batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data", null]

- test:
    name: 执行批量添加授权任务
    api: api/org-auth-relation/testBatchTask.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content", success]

- test:
    name: 获取批量任务进度-任务完成发起成功
    api: api/org-auth-relation/batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      url1: "/v2/org-auth-relation/batch-add-task"
      params1:
        {

        }
      headers1:
        {
          "X-Tsign-Open-App-Id": "${ENV(appid)}",
          "X-Tsign-Service-Group": "${ENV(Groupid)}",
          "X-Tsign-Open-Auth-Mode": simple,
          "X-Tsign-Open-Tenant-Id": $orgId1,
          "X-Tsign-Open-Operator-Id": $accountId1
        }
    setup_hooks:
      - ${waitTillDone2($url1,$params1,$headers1,5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]

- test:
    name: 获取订单信息-验证订单商品数-1
    api: api/org-auth-relation/effective-products.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    extract:
      - margin3: content.data.0.margin
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["${minus($margin2,$margin3)}", 1]

- test:
    name: 处理删除过完成的任务
    api: api/org-auth-relation/delete-batch-add-task.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询关联企业列表-A下查询下级组织C，状态为待授权
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: shareVip
      searchKey: $orgName3
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 1]
    extract:
      - authRelationId2: content.data.list.0.authRelationId

- test:
    name: 查询关联企业列表-B下查询下级组织C，状态为待授权
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName3
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 1]

- test:
    name: 查询关联企业列表-C下查询上级组织B，状态为待授权
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId3
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 1]

- test:
    name: 查询授权关系最新的签署信息
    api: api/org-auth-relation/get-auth-relation-last-process.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId2
      authRelationLogId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - processId2: content.data.processId
      - flowId2: content.data.flowId

- test:
    name: 获取流程跳转地址-B查看A-C的授权协议流程
    api: api/contract-manager/process-geturl.yml
    variables:
      processId: $processId2
      accountId: $accountId1
      menuId: -3
      subjectId: $orgId2
      platform: 5
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 查询签署流程详情
    api: api/footstone/flowDetail.yml
    variables:
      flowId: $flowId2
      authorizerIds: ${str_append($orgId1,$orgId3)}
      queryAccountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - signfields2: content.data.signDocs.0.signfields

- test:
    name: 查询签署可用印章列表-获取企业C的印章
    api: api/footstone/getSignSeals.yml
    variables:
      flowId: $flowId2
      accountId: $accountId1
      batchSign: false
      signerAccountId: $accountId1
      authorizerIds: $orgId3
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - sealId3: content.data.officialSeals.0.organSeals.0.sealId

- test:
    name: 发起验证码
    api: api/footstone/sendSms.yml
    variables:
      accountId: $accountId1
      flowIds: [$flowId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - bizId1: content.data.bizId

- test:
    name: 校验验证码认证
    api: api/footstone/checkSms.yml
    variables:
      accountId: $accountId1
      authCode: 123456
      bizId: $bizId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.passed", true]

- test:
    name: 提交签署
    api: api/footstone/add-Update-Execute.yml
    variables:
      flowId: $flowId2
      accountId: $accountId1
      addSignfields: []
      async: true
      signfieldIds: []
      signfieldId1: ${getValue($signfields2,authorizedAccountId,$orgId1,signfieldId)}
      posBean1: ${getValue($signfields2,authorizedAccountId,$orgId1,posBean)}
      signfieldId2: ${getValue($signfields2,authorizedAccountId,$orgId3,signfieldId)}
      posBean2: ${getValue($signfields2,authorizedAccountId,$orgId3,posBean)}
      updateSignfields:
        [
        {
          "signfieldId": $signfieldId1,
          "sealId": $sealId1,
          "signerOperatorAuthorizerId": $orgId1,
          "signerOperatorId": $accountId1,
          "authorizedAccountId": $orgId1,
          "crossEnterpriseSeal": false,
          "fieldType": 0,
          "posBean": $posBean1
        },
        {
          "signfieldId": $signfieldId2,
          "sealId": $sealId3,
          "signerOperatorAuthorizerId": $orgId3,
          "signerOperatorId": $accountId1,
          "authorizedAccountId": $orgId3,
          "crossEnterpriseSeal": false,
          "fieldType": 0,
          "posBean": $posBean2
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 查询关联企业列表-A下查询下级组织C，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName3
      authRelationStatusList: null
      queryDeleted: null
    setup_hooks:
      - ${hook_sleep_n_secs(3)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]

#排序无规律，先注释
#- test:
#    name: 查询有效关联企业列表
#    api: api/org-auth-relation/search-effective-auth-relation.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId1
#      pageNum: 1
#      pageSize: 10
#      searchTenantName: null
#      bizScene: null
#      querySelf: false
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#      - eq: ["content.data.total", 2]
#      - eq: ["content.data.list.0.childTenantName", $orgName3]
#      - eq: ["content.data.list.0.childTenantOid", $orgId3]

- test:
    name: 获取授权详情
    api: api/org-auth-relation/auth-relation-detail.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.authReason", $authReason2]
      - eq: ["content.data.parentTenantName", $orgName2]
      - eq: ["content.data.childTenantName", $orgName3]
      - eq: ["content.data.childUsccCode", $orgCode3]

- test:
    name: 判断是否可作为授权企业-校验企业C
    api: api/org-auth-relation/check-auth-tenant.yml
    variables:
      tenantId: $orgId3
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", false]

- test:
    name: 运营后台获取订单信息
    api: api/org-auth-relation/crm/effective-products.yml
    variables:
      tenantId: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.0.effectiveEndTime", "${getTimeStamp_ms()}"]
      - ne: ["content.data.0.orderId", null]
      - gt: ["content.data.0.margin", 0]

- test:
    name: 运营后台线下添加前获取添加条件
    api: api/org-auth-relation/crm/auth-relation-config.yml
    variables:
      tenantId: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.batchAddExcelUrl", null]
      - ne: ["content.data.authReasonList", []]

- test:
    name: 运营后台线下批量添加授权记录-A-B-C-D
    api: api/org-auth-relation/crm/add-auth-relation.yml
    variables:
      authReason: $authReason3
      authTenantOid: $orgId1
      childTenantList:
        [
        {
          "fileKey":"$fileKey1",
          "orderId":"$orderId1",
          "childTenantOid":"$orgId4",
          "childUsccCode":"$orgCode4",
          "childTenantName":"$orgName4",
          "authResources":[
            "100"
          ],
          "shareConfigs":[
            "shareSign"
          ]
        }
        ]
      operatorAlias: 明绣
      operatorName: 谢佳
      parentTenantOid: $orgId3
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.success", true]

- test:
    name: 查询关联企业列表-A下查询下级组织D，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: shareSign
      searchKey: $orgName4
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]
    extract:
      - authRelationId3: content.data.list.0.authRelationId

- test:
    name: 查询关联企业授权记录列表
    api: api/org-auth-relation/auth-relation-log-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      authRelationId: $authRelationId3
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - eq: ["content.data.list.0.authRelationStatus", 2]

- test:
    name: 查询关联企业列表-B下查询下级组织D，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName4
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]

- test:
    name: 查询关联企业列表-C下查询下级组织D，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId3
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName4
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]

- test:
    name: 查询关联企业列表-D下查询下级组织C，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId4
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName3
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]

#排序无规律，先注释
#- test:
#    name: 查询有效关联企业列表
#    api: api/org-auth-relation/search-effective-auth-relation.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId1
#      pageNum: 1
#      pageSize: 10
#      searchTenantName: null
#      bizScene: null
#      querySelf: false
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#      - eq: ["content.data.total", 3]
#      - eq: ["content.data.list.0.childTenantName", $orgName4]
#      - eq: ["content.data.list.0.childTenantOid", $orgId4]

- test:
    name: 运营后台查询关联企业列表
    api: api/org-auth-relation/crm/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      pageNum: 1
      pageSize: 10
      queryDeleted: false
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 3]

- test:
    name: 运营后台查询关联企业授权记录列表
    api: api/org-auth-relation/crm/auth-relation-log-list.yml
    variables:
      tenantId: $orgId1
      pageNum: 1
      pageSize: 10
      authRelationId: $authRelationId3
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - eq: ["content.data.list.0.authRelationStatus", 2]

- test:
    name: 判断是否可作为授权企业-校验企业D
    api: api/org-auth-relation/check-auth-tenant.yml
    variables:
      tenantId: $orgId4
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", false]

- test:
    name: 添加关联企业前校验企业是否符合规则-A-B-C-D-E校验不通过，层级不能超过4级
    api: api/org-auth-relation/check-tenant-when-add.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      parentTenantOid: $orgId4
      childTenantList:
        [
        {
          "orderId": "$orderId1",
          "authResources":[
            "100"
          ],
          "shareConfigs":[
            "shareSign"
          ],
          "childTenantName":"$orgName5",
          "childUsccCode":"$orgCode5",
          "childTenantOid":"$orgId5"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.pass", false]

- test:
    name: 主动解除授权-解除A-B
    api: api/org-auth-relation/rescind-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId1
      authRelationLogId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 查询关联企业列表-A下查询下级组织B，状态为已失效
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 3]

- test:
    name: 查询关联企业授权记录列表
    api: api/org-auth-relation/auth-relation-log-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      authRelationId: $authRelationId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - eq: ["content.data.list.0.authRelationStatus", 4]

- test:
    name: 查询关联企业列表-C下查询上级组织B，状态为已失效
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId3
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 3]

- test:
    name: 查询关联企业列表-C下查询上级组织A，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId3
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName1
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]

- test:
    name: 主动解除授权-解除A-C
    api: api/org-auth-relation/rescind-auth-relation.yml
    variables:
      tenantId: $orgId3
      operatorId: $accountId1
      authRelationId: $authRelationId2
      authRelationLogId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 查询关联企业列表-D下查询上级组织A，状态为生效中
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId4
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName1
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.authRelationStatus", 2]

- test:
    name: 主动解除授权-解除A-D
    api: api/org-auth-relation/rescind-auth-relation.yml
    variables:
      tenantId: $orgId4
      operatorId: $accountId1
      authRelationId: $authRelationId3
      authRelationLogId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 查询关联企业列表-A下查询有效的下级组织
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: null
      authRelationStatusList: "2"
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]

- test:
    name: 删除授权-删除A-B
    api: api/org-auth-relation/delete-auth-relation.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      authRelationId: $authRelationId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询关联企业列表-A下查询下级组织B
    api: api/org-auth-relation/auth-relation-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      shareKey: null
      searchKey: $orgName2
      authRelationStatusList: null
      queryDeleted: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]
