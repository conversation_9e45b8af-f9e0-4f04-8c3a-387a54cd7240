- config:
    name: 更新审批模版（完成）


- test:
    name: 更新审批模版
    api: api/approval/template_update.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalTemplateId: AT66bd4fc2b1bc4ac4afeb4ea9f11223fb
      approvalTemplateName: 测试
      approvalTemplateDesc: 测试
      conditionType: 0
      approvalTemplateDescription: "11"
      conditionValues: []
      approvalTemplateType: 2
      approvalTemplateModel:
        {
          "frontProcessDefinition": "${ENV(test2)}",
          "processDefinition": "${ENV(test1)}"
        }
    validate:
      - eq: ["content.code", 110000001]
      - eq: ["status_code", 200]
      - eq: ["content.message", '审批流模版不存在']


- test:
    name: 更新审批模版
    api: api/approval/template_update.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalTemplateId: AT93b42adeda724c6d88e6c73acd954490
      approvalTemplateName: 测试1
      approvalTemplateDesc: 22
      conditionType: 1
      approvalTemplateDescription: "11"
      conditionValues: [{name: "123124", value: "c7defb83396b45e79c11bbae59d23227"}]
      approvalTemplateType: 2
      candidate: ${ENV(test1)}
      approvalTemplateModel:
        {
          "frontProcessDefinition": "{\"nodeConfig\":{\"nodeName\":\"test\",\"type\":0,\"eventType\":0,\"priorityLevel\":\"\",\"settype\":\"\",\"selectMode\":\"\",\"selectRange\":\"\",\"directorLevel\":\"\",\"examineMode\":\"\",\"noHanderAction\":\"\",\"examineEndDirectorLevel\":\"\",\"ccSelfSelectFlag\":\"\",\"conditionList\":[],\"nodeUserList\":[],\"id\":\"start\",\"childNode\":{\"nodeName\":\"审核人\",\"type\":1,\"id\":\"approve_ZMOTC66H_1681210591803\",\"settype\":\"ROLE\",\"selectMode\":0,\"selectRange\":0,\"directorLevel\":1,\"examineMode\":\"OR_SIGN\",\"noHanderAction\":2,\"examineEndDirectorLevel\":0,\"childNode\":{},\"nodeUserList\":[],\"receiveRole\":\"4a8cecb3795c46e2a4e6189df8365eed\",\"departmentHead\":0,\"isLastPaaroveNode\":true,\"lastApproveNodeVal\":[]},\"conditionNodes\":[],\"isLastPaaroveNode\":false}}",
          "processDefinition":"{\"autoComplete\":true,\"nodes\":[{\"key\":\"start\",\"name\":\"触发审批事件\",\"type\":\"START\"},{\"key\":\"approve_ZMOTC66H_1681210591803\",\"name\":\"审核人\",\"type\":\"OR_SIGN\",\"candidates\":[{\"candidate\":\"${candidate}\",\"candidateType\":\"PROCESS_VARIATE\",\"userName\":\"\"}],\"isApproveFinal\":true},{\"key\":\"end\",\"name\":\"结束\",\"type\":\"END\"}],\"sequences\":[{\"defaultSequence\":false,\"key\":\"sequences-45OB6PDE-1681210634788\",\"name\":\"触发审批事件\",\"source\":\"start\",\"target\":\"approve_ZMOTC66H_1681210591803\",\"controlGroups\":{}},{\"defaultSequence\":false,\"key\":\"sequences-WRAZKOFV-1681210634789\",\"name\":\"审核人\",\"source\":\"approve_ZMOTC66H_1681210591803\",\"target\":\"end\",\"controlGroups\":{}}]}"
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]