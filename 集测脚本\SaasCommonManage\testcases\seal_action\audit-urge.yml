- config:
    name: 印章审核催办
    variables:
      orgId: "0001095aa4bb4f178dafd418ac274a62"  #企业oid 阮小五测试企业
      adminId: "a46d29697c50498eb9ff75655ba9bad1"  #管理员oid   阮小五
      data: ${ENV(data)}
      refuse_fileId: "66f9b050-588f-4d23-bbee-7751127716c5"



#印章审核催办
- test:
    name: 印章审核催办
    variables:
        operatorid: $adminId
        tenantid: $orgId
        sealId: "5f6b9a02-84ba-4084-8be4-d7c4f3767bbb"
        key: "saas-common-manager:audit:urge:seal:5f6b9a02-84ba-4084-8be4-d7c4f3767bbb"
    #extract:
        #- Bg_sealId: content.data.sealId
    api: api/seal/audit-urge.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]
    setup_hooks:
        - ${redis_val($key)}