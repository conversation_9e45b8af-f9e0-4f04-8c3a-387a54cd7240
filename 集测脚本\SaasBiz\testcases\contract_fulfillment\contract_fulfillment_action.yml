- config:
    name: 履约提醒的场景
    variables:
      tenantId: ${ENV(mx_orgId1)}                       # 当前空间主体
      operatorId: ${ENV(mx_accountId1)}                 # 操作人oid
      fulfillment_name: 履约提醒-${getTimeStamp_ms()}
      app_id: ${ENV(app_id)}
      db_name1: cm_new


- test:
    name: 查询履约的类型
    api: api/contract_fulfillment/fulfillment_typeList.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - contains: ["content.data.list", 到期提醒]


- test:
    name: 获取企业下的履约规则列表
    api: api/contract_fulfillment/fulfillment_ruleList.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "name": "",
          "typeName": null,
          "pageNum": 1,
          "pageSize": 10
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      
- test:
    name: 修改履约规则
    api: api/contract_fulfillment/fulfillment_updateRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "name": "默认履约-勿删！",
          "noticeRule": {
            "condition": [{
                            "key": "contractValidity",
                            "value": "30",
                            "middleType": "before"
                          }],
            "reminder": {
              "accounts": [],
              "type": ["initiator", "signer", "cc"]
            }
          },
          "status": "disable",
          "type": "expire",
          "typeName": "到期提醒",
          "scopeType": "all",
          "noticeChannels": ["INMAIL"],
          "ruleId": "b5c0244664824299a7c5ca6611cacff1"
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      

- test:
    name: 新增履约规则-到期提醒-全部合同
    api: api/contract_fulfillment/fulfillment_saveRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "name": $fulfillment_name,
          "noticeRule": {
            "condition": [{
                            "key": "createTime",
                            "value": 3,
                            "middleType": "after"
                          }],
            "reminder": {
              "accounts": [],
              "type": ["initiator", "signer", "cc"]
            }
          },
          "status": "enable",
          "type": "expire",
          "typeName": "",
          "scopeType": "all",
          "noticeChannels": ["INMAIL", "MOBILE"]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - ruleId_1: content.data

- test:
    name: 查看履约的详情
    api: api/contract_fulfillment/fulfillment_getRuleDetail.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      ruleId: $ruleId_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.typeName", 到期提醒]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}
      
- test:
    name: 删除履约规则1
    api: api/contract_fulfillment/fulfillment_deleteRule.yml
    variables:
      sql1: "select rule_id from contract_fulfillment_rule where tenant_oid='$tenantId' and deleted=0 and name='$fulfillment_name' limit 1;"
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      ruleId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      
- test:
    name: 新增履约规则-收款提醒-台账提取
    api: api/contract_fulfillment/fulfillment_saveRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "name": $fulfillment_name,
          "formId": "1096c5455b3b4dddbf24a0024bab4f3e",
          "noticeRule": {
            "condition": [{
                            "key": "contractValidity",
                            "value": 3,
                            "middleType": "after"
                          }],
            "reminder": {
              "accounts": [],
              "type": ["initiator", "signer"]
            }
          },
          "status": "enable",
          "type": "collection",
          "typeName": "",
          "scopeType": "form",
          "noticeChannels": ["INMAIL"]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - ruleId_2: content.data
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}
      
- test:
    name: 删除履约规则2
    api: api/contract_fulfillment/fulfillment_deleteRule.yml
    variables:
      sql1: "select rule_id from contract_fulfillment_rule where tenant_oid='$tenantId' and deleted=0 and name='$fulfillment_name' limit 1;"
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      ruleId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      
- test:
    name: 新增履约规则-付款提醒-自定义范围
    api: api/contract_fulfillment/fulfillment_saveRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "name": $fulfillment_name,
          "scopeConditions": [{
                                "fieldId": "4a59f2db6bda427e8c4c4662f9c0e25d",
                                "value": [{
                                            "categoryId": "24b9ea11cbb44a6c92755c57dc6fec6f",
                                            "categoryName": "劳动合同",
                                            "extractFields": ["合同名称", "合同编号", "公司名", "公司地址", "法定代表人姓名", "员工姓名", "员工住址", "证件号", "合同期限", "合同开始日期", "合同结束日期", "岗位", "工作地点", "工作时间", "薪资"],
                                            "flowTemplates": null,
                                            "contractSize": 0,
                                            "enable": true,
                                            "rowId": "24b9ea11cbb44a6c92755c57dc6fec6f"
                                          }],
                                "fieldType": 4,
                                "matchType": 3,
                                "operatorType": 2,
                                "childOperators": [{
                                                     "conditionParams": "[{\"name\":\"劳动合同\",\"id\":\"24b9ea11cbb44a6c92755c57dc6fec6f\"}]",
                                                     "operatorType": 2
                                                   }]
                              }],
          "noticeRule": {
            "condition": [{
                            "key": "signTime",
                            "value": "5",
                            "middleType": "after"
                          }],
            "reminder": {
              "accounts": [],
              "type": ["signer"]
            }
          },
          "status": "enable",
          "type": "payment",
          "typeName": "",
          "scopeType": "force",
          "noticeChannels": ["INMAIL", "MOBILE"]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - ruleId_3: content.data
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 删除履约规则3
    api: api/contract_fulfillment/fulfillment_deleteRule.yml
    variables:
      sql1: "select rule_id from contract_fulfillment_rule where tenant_oid='$tenantId' and deleted=0 and name='$fulfillment_name' limit 1;"
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      ruleId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      
- test:
    name: 新增履约规则-自定义类型-自定义范围
    api: api/contract_fulfillment/fulfillment_saveRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "name": $fulfillment_name,
          "scopeConditions": [{
                                "fieldId": "24c65db690c84ed191c72b10b72f4113",
                                "value": [{
                                            "val": "测试",
                                            "key": "1700449566345-2133"
                                          }],
                                "fieldType": 3,
                                "matchType": 3,
                                "operatorType": 2,
                                "childOperators": [{
                                                     "conditionParams": "[\"测试\"]",
                                                     "operatorType": 2
                                                   }]
                              }],
          "noticeRule": {
            "condition": [{
                            "key": "signValidity",
                            "value": "5",
                            "middleType": "before"
                          }],
            "reminder": {
              "accounts": [],
              "type": ["cc", "signer"]
            }
          },
          "status": "disable",
          "type": "custom",
          "typeName": "自定义类型测试",
          "scopeType": "force",
          "noticeChannels": ["MOBILE", "EMAIL"]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - ruleId_4: content.data
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

      
- test:
    name: 删除履约规则4
    api: api/contract_fulfillment/fulfillment_deleteRule.yml
    variables:
      sql1: "select rule_id from contract_fulfillment_rule where tenant_oid='$tenantId' and deleted=0 and name='$fulfillment_name' limit 1;"
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      ruleId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      
- test:
    name: 启用履约规则
    api: api/contract_fulfillment/fulfillment_updateRuleStatus.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "ruleId": "b5c0244664824299a7c5ca6611cacff1",
          "status": "enable"
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      
- test:
    name: 禁用履约规则
    api: api/contract_fulfillment/fulfillment_updateRuleStatus.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "ruleId": "b5c0244664824299a7c5ca6611cacff1",
          "status": "disable"
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]