- config:
    name: 更新领章成功状态
#    base_url: ${ENV(saas_common_manage_url)}



- test:
    name: 传oid和subjectId
    api: api/getseal/receive-success.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: subjectId不存在
    api: api/getseal/receive-success.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      subjectId: 5add8dece1e941f999e589223117b999

    validate:
      - eq: ["content.code", 70000604]
      - contains: ["content.message", 请扫码重试或联系客服]


- test:
    name: oid不存在
    api: api/getseal/receive-success.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb99
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 70000604]
      - contains: ["content.message", 请扫码重试或联系客服]

- test:
    name: oid为空
    api: api/getseal/receive-success.yml
    variables:
      oid:
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 70000604]
      - contains: ["content.message", 请扫码重试或联系客服]

- test:
    name: subjectId为空
    api: api/getseal/receive-success.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      subjectId:

    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message", 主体id不能为空]

- test:
    name: oid和subjectId不在同一主体下
    api: api/getseal/receive-success.yml
    variables:
      oid: 8db662558c30419885b125314f312aa3
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 70000604]
      - contains: ["content.message", 请扫码重试或联系客服]

    #teardown_hooks:
     # - ${hook_sleep_n_secs(2)}
