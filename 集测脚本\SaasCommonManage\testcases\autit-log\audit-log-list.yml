- config:
    name: 查询审计日志列表
#    base_url: ${ENV(saas_common_manage_url)}


- test:
    name: 查询审计日志列表--审计日志模块不存在
    api: api/audit-log/audit-log-list.yml
    variables:
      firstModule: '不存在的module'
      operatorId: ''
      tenantId: ''
      startTime: 1666540800590
      endTime: 1667911605341
      event: ''
      innerCall: true
      cursor: ''
    validate:
      - eq: ["content.code", 70000804]
      - eq: ["content.message", 审计日志模块不存在]

- test:
    name: 查询审计日志列表--审计日志无操作权限
    api: api/audit-log/audit-log-list.yml
    variables:
      firstModule: 'saas_seal_management'
      operatorId: '123456'
      tenantId: '6852f935f6c241db8e84e87e62c4bcaa'
      startTime: 1666540800590
      endTime: 1667911605341
      event: ''
      innerCall: true
      cursor: ''
    validate:
      - eq: ["content.code", 70000805]
      - eq: ["content.message", 印章管理审计日志无操作权限]

- test:
    name: 查询审计日志列表--成功
    api: api/audit-log/audit-log-list.yml
    variables:
      firstModule: 'user_organizational_change'
      operatorId: '8723abe263f142009d98471ce7a70bd8'
      tenantId: 'd9ec3169a6e04d148e5a8cc08ab3c13d'
      startTime: 1666540800590
      endTime: 1667911605341
      event: ''
      innerCall: true
      cursor: ''
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
