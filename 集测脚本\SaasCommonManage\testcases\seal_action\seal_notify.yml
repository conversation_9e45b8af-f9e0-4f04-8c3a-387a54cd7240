
#临时放一下 加集测
- test:
    name: 授权列表-设置接受审批通知
    variables:
        tenantid: "41dff852f3be4927b74895833bf0c3c2"
        operatorid: "80ff3354fac947cdae1555fc4cc25c7f"
        orgId: "41dff852f3be4927b74895833bf0c3c2"
        ruleGrantedId: "d32aa4b7-874e-4ed4-a250-1f36f0bd9775"
        shouldNotify: false
        sealOwnerOid: "149453061e7347dc9c453627d307a6dc"
    api: api/seal-grant/setting-rule-grant-notify.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 授权列表-设置接受审批通知
    variables:
        tenantid: "41dff852f3be4927b74895833bf0c3c2"
        operatorid: "80ff3354fac947cdae1555fc4cc25c7f"
        orgId: "41dff852f3be4927b74895833bf0c3c2"
        ruleGrantedId: "d32aa4b7-874e-4ed4-a250-1f36f0bd9775"
        shouldNotify: true
        sealOwnerOid: "149453061e7347dc9c453627d307a6dc"
    api: api/seal-grant/setting-rule-grant-notify.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 查询印章详情
    variables:
        tenantid: "41dff852f3be4927b74895833bf0c3c2"
        sealIds: "7fd973d0-854e-4af0-aac6-5113e65d95ec"
        orgId: "41dff852f3be4927b74895833bf0c3c2"
        downloadFlag: true
        operator:
    api: api/seal/get_bySealId.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#编辑印章名称
- test:
      name: 编辑印章名称
      variables:
          tenantid: "41dff852f3be4927b74895833bf0c3c2"
          operatorid: "80ff3354fac947cdae1555fc4cc25c7f"
          sealId: "3c1e2f53-86ed-48f2-ad9c-ae38ae428ea2"
          alias: 修改名字${getTimeStamp_ms()}
          sealOwnerOid: "149453061e7347dc9c453627d307a6dc"
      api: api/seal/update-alias.yml
      validate:
          - eq: [ "content.code",0 ]
          - contained_by: [ "content.message", [ "成功","缺少参数" ] ]


#获取印章授权书下载地址
- test:
    name: 获取印章授权书下载地址-印章授权书未签署（企业外授权）
    variables:
        tenantid: "41dff852f3be4927b74895833bf0c3c2"
        operatorid: "80ff3354fac947cdae1555fc4cc25c7f"
        orgId: "41dff852f3be4927b74895833bf0c3c2"
        ruleGrantedId: "7affef0b-26e4-43e1-8323-83a4a5d1d453"
        sealOwnerOid: "149453061e7347dc9c453627d307a6dc"

    api: api/seal-grant/download-rule-grant.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","印章授权书未签署"]]