- config:
    name: 查询当前企业是否能使用该功能
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      db_name1: cm


- test:
    name: 查询功能在当前企业是否能使用-functionCode为空
    api: api/common/function-used.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: ""
      extendParam: {}
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", must not be blank]

- test:
    name: 查询功能在当前企业是否能使用-functionCode不存在
    api: api/common/function-used.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: 123
      extendParam: {}
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 未支持的功能标识]

- test:
    name: 查询信息采集器功能在当前企业是否能使用
    api: api/common/function-used.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: info_collect
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.used", false]

- test:
    name: 查询模板授权功能在当前企业是否能使用
    api: api/common/function-used.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: template_grant
      extendParam: {}
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", templateId不能为空]

- test:
    name: 查询模板授权功能在当前企业是否能使用
    api: api/common/function-used.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: template_grant
      extendParam:
        {
          "templateId": "ac8ae0af434f4488992ed25f0c65170f"
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.used", true]

- test:
    name: 查询用印审批模板功能在当前企业是否可以使用
    api: api/common/function-used.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: use_seal_approve
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.used", true]

- test:
    name: 查询合同审批模板功能在当前企业是否可以使用
    api: api/common/function-used.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: org_approve_template_manage
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.used", true]

- test:
    name: 查询关联企业功能在当前企业是否可以使用
    api: api/common/function-used.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: affiliated_enterprises
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.used", true]

- test:
    name: 查询部门管理功能在当前企业是否可以使用
    api: api/common/function-used.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: org_department_manage
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.used", true]

- test:
    name: 查询钉钉审批模板功能在当前企业是否可以使用
    api: api/common/function-used.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: ding_approve_template_manage
      extendParam: {}
      sql1: "SELECT case when COUNT(*) > 0 then TRUE else FALSE end FROM doc_cooperation.flow_template where oid='$orgId1' and type=4 and status!=2;"
      flag: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.used", $flag]

- test:
    name: 查询钉钉审批模板功能在当前企业是否可以使用
    api: api/common/function-used.yml
    variables:
      tenantId: "41dff852f3be4927b74895833bf0c3c2"
      operatorId: "8723abe263f142009d98471ce7a70bd8"
      functionCode: ding_approve_template_manage
      extendParam: {}
      sql1: "SELECT case when COUNT(*) > 0 then TRUE else FALSE end FROM doc_cooperation.flow_template where oid='$orgId1' and type=4 and status!=2;"
      flag: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.used", false]

- test:
    name: 查询专属云设置的使用记录
    api: api/common/function-used.yml
    variables:
      tenantId: "41dff852f3be4927b74895833bf0c3c2"
      operatorId: "8723abe263f142009d98471ce7a70bd8"
      functionCode: dedicated_cloud
      extendParam: {checkExistDedicatedCloud: true, checkAuthRelationShare: true}

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.used", False]

#- test:
#    name: 查询专属云功能在该企业下能否使用
#    api: api/common/can-use.yml
#    variables:
#      tenantId: "08486d61823d4c2086122b26fb3e615a"
#      operatorId: "0bc26b27ede64cc48e7a800f4c5cbac7"
#      json:
#        {
#          "functionCodes": [ {
#            "functionCode": "dedicated_cloud",
#            "extendParam": {
#              "checkExistDedicatedCloud": true,
#              "checkAuthRelationShare": true
#            }
#          } ]
#        }
#    validate:
#      - eq: ["content.code", 0]
##      - eq: ["content.message", 成功]
#      - eq: ["content.data.functionCodesCanUseDTOS.0.canUse", true]