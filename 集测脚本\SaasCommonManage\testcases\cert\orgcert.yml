- config:
    name: 获取企业证书信息
#    base_url: ${ENV(saas_common_manage_url)}


- test:
    name: 获取企业证书信息
    api: api/cert/orgcert.yml
    variables:
      operatorId: '565a742760cc485185bbd3cfc1e47e80'
      tenantId: '09d83d8687b74df292862c0cd8cc2e98'
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
#      - eq: ["content.data.certName", '深圳天谷信息科技有限公司']
#      - eq: ["content.data.sn", "7063667715108290969701234940008192061308"]
