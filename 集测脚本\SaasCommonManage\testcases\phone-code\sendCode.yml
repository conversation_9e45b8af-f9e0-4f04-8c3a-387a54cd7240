- config:
    name: 发送手机验证码
#    base_url: ${ENV(base_url)}
    variables:
      accountId1: ${ENV(accountId1)}

- test:
    name: 发送手机验证码-bizTag为空
    api: api/phone-code/sendCode.yml
    variables:
      operatorId: $accountId1
      bizTag: ""
      phone: ***********
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "must not be blank"]

- test:
    name: 发送手机验证码-bizTag不存在
    api: api/phone-code/sendCode.yml
    variables:
      operatorId: $accountId1
      bizTag: 123
      phone: ***********
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 未知的验证码业务类型]

- test:
    name: 发送手机验证码-phone为空
    api: api/phone-code/sendCode.yml
    variables:
      operatorId: $accountId1
      bizTag: apply-trial
      phone: null
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "must not be blank"]

- test:
    name: 发送手机验证码-phone格式不正确
    api: api/phone-code/sendCode.yml
    variables:
      operatorId: $accountId1
      bizTag: apply-trial
      phone: 123
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 请输入正确的手机号]
