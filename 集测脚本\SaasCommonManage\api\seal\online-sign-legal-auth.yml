 #发起线上法人授权签署流程
    variables:
        appid: ${ENV(appid)}
    request:
        url: ${ENV(inner_open_url)}/v1/saas-common/organizations/seals/online-sign-legal-auth
        method: POST
        headers: ${gen_headers($appid, X-Tsign-Open-Tenant-Id=$tenantid, X-Tsign-Open-Operator-Id=$operatorid)}
        json:
          {
            "developerAppId": $developerAppId,
            "developerCallbackUrl": $developerCallbackUrl,
            "principal": $principal,
            "orgId": $orgId
          }
