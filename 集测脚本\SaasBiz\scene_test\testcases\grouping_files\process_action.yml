- config:
    name: 企业合同非保密流程查看、下载
    variables:
      orgId1: 5546e67033104443b5c7f509ccb060f1
      accountId1: 7c1d00fb6f3e473d9a55697b9b9abe8b    #企业1的普通成员1
      accountId2: 8a17e0b48e5948738a990d09b190d45b    #企业1的管理员
      accountId3: 196bb99c0716402892d7118c6243ac36    #企业1的普通成员2
      account3: ***********
      accountName3: 测试四十
      roleId1: 16f12f028f6f42b49a06f5bcc948decc       #企业1的企业合同待归档的查看角色id
      processId1: 6f0370263b8a443886883075b3ffb963    #企业1待归档列表的非保密填写流程
      processId2: b64f7ff47aa44cdbb42d2a4ad4209a0a    #企业1的子企业非保密填写流程
      processId3: aff431e1f9c3453aabe0144cc808eed2    #企业1待归档列表的非保密签署流程
      processId4: 8f1d29b86f684b0ca93c4a0e92076f59    #企业1的子企业非保密签署流程
      roleId2: 0f258fee46af43ffa267e39e040e0694       #企业1的企业合同已归档的查看角色id
      menuId1: 636339199c7f464ea67514823c7b6b2e       #企业1的分类1
      processId5: 0dc2bf85e51b4f5ca206106e3358b328    #企业1分类1的非保密填写流程
      processId6: 275a100eb6d5493c93e9ebc38e7e99e6    #企业1分类1的子企业非保密填写流程
      processId7: fd68858396e341bfbd276ff1cbb1c2b4    #企业1分类1的非保密签署流程
      processId8: 9851961d373745979a99d54fa820248c    #企业1分类1的子企业非保密签署流程
      roleId4: c2d56230469a47d88b30ba0058eb3492       #企业1的普通成员角色id
      roleId5: 186d7eab1ee34c2e81d4bddb93c6ffa2       #企业1的设置合同保密角色id

- test:
    name: 企业合同待归档列表查看流程-流程填写人查看非保密填写流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId1
      accountId: $accountId1
      subjectId: $orgId1
      platform: 5
      menuId: -1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同待归档列表查看流程-流程签署人查看非保密签署流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId3
      accountId: $accountId1
      subjectId: $orgId1
      platform: 5
      menuId: -1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同待归档列表查看流程-企业管理员查看非保密填写流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId1
      accountId: $accountId2
      subjectId: $orgId1
      platform: 5
      menuId: -1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同待归档列表查看流程-企业管理员查看非保密签署流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId3
      accountId: $accountId2
      subjectId: $orgId1
      platform: 5
      menuId: -1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同待归档列表下载流程-企业管理员下载非保密签署流程
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: -1
      processIds: [$processId3]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同待归档列表查看流程-企业普通成员非流程参与人查看非保密填写流程无权限
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId1
      accountId: $accountId3
      subjectId: $orgId1
      platform: 5
      menuId: -1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 操作人无权限]

- test:
    name: 企业合同待归档列表查看流程-企业普通成员非流程参与人查看非保密签署流程无权限
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId3
      accountId: $accountId3
      subjectId: $orgId1
      platform: 5
      menuId: -1
    validate:
      - eq: ["content.code", 1435208]
      - eq: ["content.message", 当前用户无权限]

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同待归档查看的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId3
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1,$roleId4,$roleId5]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同待归档列表查看流程-企业普通成员非流程参与人查看非保密填写流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId1
      accountId: $accountId3
      subjectId: $orgId1
      platform: 5
      menuId: -1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同待归档列表查看流程-企业普通成员非流程参与人查看非保密签署流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId3
      accountId: $accountId3
      subjectId: $orgId1
      platform: 5
      menuId: -1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同待归档查看的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId3
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId4,$roleId5]
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同待归档列表查看流程-主企业管理员查看子企业的非保密填写流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId2
      accountId: $accountId2
      subjectId: $orgId1
      platform: 5
      menuId: -1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同待归档列表查看流程-主企业管理员查看子企业的非保密签署流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId4
      accountId: $accountId2
      subjectId: $orgId1
      platform: 5
      menuId: -1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同待归档列表下载流程-企业管理员下载子企业非保密签署流程
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: -1
      processIds: [$processId4]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同待归档列表一键下载流程-企业管理员一键下载非保密流程
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: null
      matching: '[{"key":"title","value":["非保密的签署流程"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同已归档列表查看流程-流程填写人查看非保密填写流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId5
      accountId: $accountId1
      subjectId: $orgId1
      platform: 5
      menuId: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同已归档列表查看流程-流程签署人查看非保密签署流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId7
      accountId: $accountId1
      subjectId: $orgId1
      platform: 5
      menuId: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同已归档列表查看流程-企业管理员查看非保密填写流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId5
      accountId: $accountId2
      subjectId: $orgId1
      platform: 5
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同已归档列表查看流程-企业管理员查看非保密签署流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId7
      accountId: $accountId2
      subjectId: $orgId1
      platform: 5
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同已归档根目录下载流程-企业管理员下载非保密签署流程
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: MENU_ALL
      processIds: [$processId7]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同已归档分类1列表下载流程-企业管理员下载非保密签署流程
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: $menuId1
      processIds: [$processId7]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同已归档列表查看流程-企业普通成员非流程参与人查看非保密填写流程无权限
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId5
      accountId: $accountId3
      subjectId: $orgId1
      platform: 5
      menuId: $menuId1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 操作人无权限]

- test:
    name: 企业合同已归档列表查看流程-企业普通成员非流程参与人查看非保密签署流程无权限
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId7
      accountId: $accountId3
      subjectId: $orgId1
      platform: 5
      menuId: $menuId1
    validate:
      - eq: ["content.code", 1435208]
      - eq: ["content.message", 当前用户无权限]

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同已归档查看的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId3
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId2,$roleId4,$roleId5]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同已归档列表查看流程-企业普通成员非流程参与人查看非保密填写流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId5
      accountId: $accountId3
      subjectId: $orgId1
      platform: 5
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同已归档列表查看流程-企业普通成员非流程参与人查看非保密签署流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId7
      accountId: $accountId3
      subjectId: $orgId1
      platform: 5
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同已归档查看的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId3
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId4,$roleId5]
      memberName: ""
      revokeRoleIds: [$roleId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 获取可操作的角色列表
    api: api/grouping_permission/roleByAuthorizer.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
      authorizer: $accountId3
      authorizeType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - roleList1: content.data

- test:
    name: 添加目录用户及授权-给accountId1分配menuId1的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId3: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account3",
          "name":"$accountName3",
          "oid":"$accountId3",
          "roleId":"$roleId3",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同已归档列表查看流程-企业普通成员非流程参与人查看非保密填写流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId5
      accountId: $accountId3
      subjectId: $orgId1
      platform: 5
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同已归档列表查看流程-企业普通成员非流程参与人查看非保密签署流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId7
      accountId: $accountId3
      subjectId: $orgId1
      platform: 5
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 添加目录用户及授权-取消accountId1的menuId1的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId3: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account3",
          "name":"$accountName3",
          "oid":"$accountId3",
          "roleId":"$roleId3",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同已归档列表查看流程-主企业管理员查看子企业的非保密填写流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId6
      accountId: $accountId2
      subjectId: $orgId1
      platform: 5
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同已归档列表查看流程-主企业管理员查看子企业的非保密签署流程
    api: api/contract_manage/process_operation/process_getUrl.yml
    variables:
      processId: $processId8
      accountId: $accountId2
      subjectId: $orgId1
      platform: 5
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.url", null]
      - ne: ["content.data.longUrl", null]

- test:
    name: 企业合同已归档根目录下载流程-企业管理员下载子企业非保密签署流程
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: MENU_ALL
      processIds: [$processId8]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同已归档根目录一键下载流程-企业管理员一键下载非保密流程
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: MENU_ALL
      matching: '[{"key":"title","value":["非保密的签署流程2"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同已归档分类1列表下载流程-企业管理员下载子企业非保密签署流程
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: $menuId1
      processIds: [$processId8]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同已归档分类1列表一键下载流程-企业管理员一键下载非保密流程
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: $menuId1
      matching: '[{"key":"title","value":["非保密的签署流程2"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]
