- config:
    name: 审批通过



- test:
    name: 发起一个审批流
    api: api/common/startapproval_input.yml
    variables:
      operatorId: a690089d48a14707a1cb78c453a0d991
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      skipCheck: true
      approvalName: "兰生-直接发起流程"
      approvalTemplateCode: "ATe066e1c4d88a4f329c3400c1b77357bc"
      subjectOid: "d9ec3169a6e04d148e5a8cc08ab3c13d"
      subjectGid: "37bdebc911bf495ca13c7410fd314a9b"
      subjectName: "杭州正道文化传媒有限公司"
      initiatorOid: "a690089d48a14707a1cb78c453a0d991"
      initiatorGid: "e76eb95bb5a9430bade950c41e415a37"
      initiatorName: "吴志强"
      initiatorDeptId:
      approvalTemplateConditionType: 0
      bizId: "1c9e547f06574a68a41738517271449c"
      bizGroupId: "1c9e547f06574a68a41738517271449c"
    validate:
      - eq: [ "status_code", 200 ]
    extract:
      approvalId: content.approvalCode


- test:
    name: 审批通过，成功
    api: api/approval/approval_agree.yml
    variables:
      operatorId: a690089d48a14707a1cb78c453a0d991
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalType: 2
      datas: [ {
        "approvalId": $approvalId
      } ]
      remark: 1
    validate:
      - eq: [ "status_code", 200 ]
#      - eq: [ "content.message", 成功 ]


- test:
    name: 发起一个审批流
    api: api/common/startapproval_input.yml
    variables:
      operatorId: a690089d48a14707a1cb78c453a0d991
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      skipCheck: true
      approvalName: "兰生-直接发起流程"
      approvalTemplateCode: "ATe066e1c4d88a4f329c3400c1b77357bc"
      subjectOid: "d9ec3169a6e04d148e5a8cc08ab3c13d"
      subjectGid: "37bdebc911bf495ca13c7410fd314a9b"
      subjectName: "杭州正道文化传媒有限公司"
      initiatorOid: "a690089d48a14707a1cb78c453a0d991"
      initiatorGid: "e76eb95bb5a9430bade950c41e415a37"
      initiatorName: "吴志强"
      initiatorDeptId:
      approvalTemplateConditionType: 0
      bizId: "1c9e547f06574a68a41738517271449c"
      bizGroupId: "1c9e547f06574a68a41738517271449c"
    validate:
      - eq: [ "status_code", 200 ]
    extract:
      approvalId1: content.approvalCode

- test:
    name: 发起一个审批流
    api: api/common/startapproval_input.yml
    variables:
      operatorId: a690089d48a14707a1cb78c453a0d991
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      skipCheck: true
      approvalName: "辰南-直接发起流程"
      approvalTemplateCode: "ATe066e1c4d88a4f329c3400c1b77357bc"
      subjectOid: "d9ec3169a6e04d148e5a8cc08ab3c13d"
      subjectGid: "37bdebc911bf495ca13c7410fd314a9b"
      subjectName: "杭州正道文化传媒有限公司"
      initiatorOid: "a690089d48a14707a1cb78c453a0d991"
      initiatorGid: "e76eb95bb5a9430bade950c41e415a37"
      initiatorName: "吴志强"
      initiatorDeptId:
      approvalTemplateConditionType: 0
      bizId: "1c9e547f06574a68a41738517271449c"
      bizGroupId: "1c9e547f06574a68a41738517271449c"
    validate:
      - eq: [ "status_code", 200 ]
    extract:
      approvalId2: content.approvalCode


- test:
    name: 审批通过，成功(批量)
    api: api/approval/approval_agree.yml
    variables:
      operatorId: a690089d48a14707a1cb78c453a0d991
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalType: 2
      datas: [ {
        "approvalId": $approvalId1 },{
        "approvalId": $approvalId2
      } ]
      remark: 1
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ "content.message", 成功 ]