- config:
    name: 获取主体的e签章开通信息
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      accountGid1: ${ENV(accountGid1)}
      db_name1: saas_base_manage
      sql1: "SELECT subject_gid FROM esignseal_service_open_info where deleted=0 order by create_time desc limit 1;"


- test:
    name: 获取主体的e签章开通信息
    api: api/esign-seal/getInfo.yml
    variables:
      gid: ${select_sql($sql1, $db_name1)}
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#      - ne: ["content.data", null]
