- config:
    name: 分页查询用印审批模板印章列表
    variables:
      orgId1: ${ENV(mx_orgId)}  #有跨企业授权章
      accountId1: ${ENV(mx_accountId)}  #企业管理员
      accountId2: 74ada1674a4d441eb0120c789f1b9300  #企业普通员工


- test:
    name: 分页查询用印审批模板印章列表-不带参数，默认不返回下载地址和查企业自己的章
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - eq: ["content.data.seals.0.url", null]
    extract:
      total: content.data.total

- test:
    name: 分页查询用印审批模板印章列表-指定页码和页数，且要返回印章的下载地址
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        pageNum: 1
        pageSize: 1
        downloadFlag: true
        grantedSeal: false
    extract:
      seals: content.data.seals
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["${getLength($seals)}", 1]
      - ne: ["content.data.seals.0.url", null]
      - eq: ["content.data.total", $total]

- test:
    name: 分页查询用印审批模板印章列表-指定的页码应该查不到数据返回空数据
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        pageNum: 2
        pageSize: $total
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", $total]
      - eq: ["content.data.seals", []]

- test:
    name: 分页查询用印审批模板印章列表-指定要查询跨企业授权章
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        grantedSeal: true
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - gt: ["content.data.total", 0]

- test:
    name: 分页查询用印审批模板印章列表-指定页码小于1报错
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        pageNum: 0
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "参数错误: 页码最小为1"]

- test:
    name: 分页查询用印审批模板印章列表-指定页码大于1000返回空数据
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        pageNum: 1001
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]
      - eq: ["content.data.seals", []]

- test:
    name: 分页查询用印审批模板印章列表-指定每页数据行数小于1报错
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        pageSize: 0
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "参数错误: 每页数据行数最少不能小于1"]

- test:
    name: 分页查询用印审批模板印章列表-指定每页数据行数大于100报错
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        pageSize: 101
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "参数错误: 每页数据行数最多不能超过100"]

- test:
    name: 分页查询用印审批模板印章列表-操作人无权限报错
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      params:
        pageNum: 1
        pageSize: 10
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 当前用户无操作权限]