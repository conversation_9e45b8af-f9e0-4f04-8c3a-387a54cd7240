- config:
    name: 任务管理相关操作
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      accountId1: ${ENV(accountId1)}
      db_name1: saas_base_manage
      sql1: "SELECT uuid FROM saas_task_info where type=1 and status in (3,4) and done=1 order by create_time desc limit 1;"
      sql2: "SELECT creator_oid FROM saas_task_info where type=1 and status in (3,4) and done=1 order by create_time desc limit 1;"
      taskId1: ${select_sql($sql1, $db_name1)}
      accountId2: ${select_sql($sql2, $db_name1)}
      sql3: "SELECT uuid FROM saas_task_info where type=3 and status=2 and done>0 order by create_time desc limit 1;"
      sql4: "SELECT creator_oid FROM saas_task_info where type=3 and status=2 and done>0 order by create_time desc limit 1;"


- test:
    name: 判断是否有进行中的任务
    api: api/taskmanage/hasExecutingTask.yml
    variables:
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 判断是否有新任务
    api: api/taskmanage/hasNewTask.yml
    variables:
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取任务列表
    api: api/taskmanage/tasks_list.yml
    variables:
      accountId: $accountId1
      taskStatus: 2
      taskType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]


- test:
    name: 获取任务列表-pageNum<1
    api: api/taskmanage/tasks_list.yml
    variables:
      accountId: $accountId1
      taskStatus: ""
      taskType: ""
      pageNum: 0
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 页码不能小于1]


- test:
    name: 获取任务列表-pageSize>100
    api: api/taskmanage/tasks_list.yml
    variables:
      accountId: $accountId1
      taskStatus: ""
      taskType: ""
      pageSize: 101
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 每页最多获取100条数据]


- test:
    name: 获取任务列表-accountId为空
    api: api/taskmanage/tasks_list.yml
    variables:
      taskStatus: ""
      taskType: ""
      accountId: ""
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 用户账号oid不能为空]


- test:
    name: 获取失败子任务列表
    api: api/taskmanage/failedTasks_list.yml
    variables:
      accountId: $accountId2
      taskId: $taskId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]


- test:
    name: 导出失败子任务列表
    api: api/taskmanage/export_failedTasks.yml
    variables:
      accountId: $accountId2
      taskId: $taskId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.failTaskUrl", null]


- test:
    name: 获取并处理任务结果
    api: api/taskmanage/handlerResult.yml
    variables:
      accountId: ${select_sql($sql4, $db_name1)}
      taskId: ${select_sql($sql3, $db_name1)}
    validate:
      - eq: ["status_code", 200]
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 执行成功]
      - ne: ["content.data.result", null]
