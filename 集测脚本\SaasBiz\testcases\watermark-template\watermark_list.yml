- config:
    name: 分页查询当前租户下的水印模板列表


- test:
    name: 分页查询当前租户下的水印模板列表
    api: api/watermark-template/watermark_list.yml
    variables:
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
      name: ""                                             # 模板名称
      pageNum: 10                                           # 页码，最小为1
      pageSize: 10                                          # 每页数量
      status: 0                                            # 水印模板状态
      type: 1                                               # 水印类型
      watermarkIds: []       # 水印模板ID列表
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


