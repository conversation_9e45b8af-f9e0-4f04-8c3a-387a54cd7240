- config:
    name: 查询推送名单相关操作
#    base_url: ${ENV(saas_common_manage_url)}

    variables:
      - contextId1: 2987
      - contextId2: 551


- test:
      name: 查询banner推送名单列表
      api: api/pash/querythepush_list.yml
      variables:
        contextId: $contextId1
        type: 1
        pageNo: 1
        pageSize: 10
        searchContent: ''
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", 成功 ]


- test:
      name: 查询弹窗推送名单列表
      api: api/pash/querythepush_list.yml
      variables:
        contextId: $contextId2
        type: 2
        pageNo: 1
        pageSize: 10
        searchContent: ''
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", 成功 ]






