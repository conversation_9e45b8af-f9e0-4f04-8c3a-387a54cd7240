- config:
    name: 删除审批



- test:
    name: 删除审批--成功
    api: api/approval/approval_delete.yml
    variables:
      operatorId: 80ff3354fac947cdae1555fc4cc25c7f
      tenantId: 149453061e7347dc9c453627d307a6dc
      "approvalType": 2
      "approvalId": "16b3e329e15047b685554603e6d77293"
      "processId": "526d9594e47647dca7ff4b218d476e93"

    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

- test:
    name: 删除审批--非企业成员
    api: api/approval/approval_delete.yml
    variables:
      operatorId: 391d0b87c284491eba58aba6dc505c86
      tenantId: 149453061e7347dc9c453627d307a6dc
      "approvalType": 2
      "approvalId": "16b3e329e15047b685554603e6d77293"
      "processId": "526d9594e47647dca7ff4b218d476e93"

    validate:
      - eq: ["content.code", 10000015]
      - contains: ["content.message", 您不是该企业成员，请联系企业管理员加入企业。]
