
- config:
    name: SaaS页面接口-认证授权关系取消接口
    variables:
      - appId_0: "${ENV(authAppId_2)}"
      - psnId: "${ENV(auth_psnId)}"
      - psnId_admin: "${ENV(auth_admin_oid)}"
      - orgId: "${ENV(auth_orgId)}"
      - transactorAccount : "auth_psn_account_3"
      - targetUrl: "https://testfront.tsign.cn:8887/index"

- test:
    name: appId为空
    variables:
        - headers: "${gen_headers_rest($orgId,$psnId_admin)}"
        - appId: ""
    api: api/oauthManage/cancelAuth_api.yml
    validate:
        - contains: [ "content.message", "参数错误: 应用id不能为空" ]
        - ne: [ "content.code", 0 ]


- test:
    name: 企业账号不存在
    variables:
        - org_str: "aaaa"
        - headers: "${gen_headers_rest($org_str,$psnId_admin)}"
        - appId: $appId_0
    api: api/oauthManage/cancelAuth_api.yml
    validate:
        - contains: [ "content.message", "账号不存在或已注销" ]

- test:
    name: 取消企业授权- 操作员无权限
    variables:
        - psn_Id: $psnId
        - headers: "${gen_headers_rest($orgId,$psnId)}"
        - appId: $appId_0
    api: api/oauthManage/cancelAuth_api.yml
    validate:
        - contains: [ "content.message", "用户无操作权限" ]
        - ne: [ "content.code", 0 ]


- test:
    name: 取消个人授权- 操作员Id非法
    variables:
        - psn_str: "aaaa"
        - headers: "${gen_headers_rest($psn_str,$psnId)}"
        - appId: $appId_0
    api: api/oauthManage/cancelAuth_api.yml
    validate:
        - contains: [ "content.message", "账号不存在或已注销" ]
        - ne: [ "content.code", 0 ]


- test:
    name: 取消个人授权- 操作员无权限（操作员Id 不等于用户ID）
    variables:
        - headers: "${gen_headers_rest($psnId,$psnId_admin)}"
        - appId: $appId_0
    api: api/oauthManage/cancelAuth_api.yml
    validate:
#        - contains: [ "content.message", "账号不存在或已注销" ]
        - ne: [ "content.code", 0 ]


- test:
    name: 取消个人授权
    variables:
        - headers: "${gen_headers_rest($psnId,$psnId)}"
        - appId: $appId_0
    api: api/oauthManage/cancelAuth_api.yml
    validate:
        - eq: [ "content.code", 0 ]
        - eq: [ "content.message", "成功" ]
        - eq: [ "content.data", true ]

- test:
    name: check-查询个人用户-应用授权记录日志
    variables:
        - headers: "${gen_headers_rest($psnId,$psnId)}"
        - pageNum: 1
        - pageSize: 10
        - appId: $appId_0
    api: api/oauthManage/mappingsLogList_api.yml
    validate:
        - ne: ["content.data.authName",null]
        - eq: ["content.data.authOid",$psnId]
        - ne: ["content.data.appName",null]
        - ne: ["content.data.appLogo",null]
        - eq: ["content.data.appId",$appId]
        - gt: ["content.data.count",0]
        - ne: ["content.data.authLogList.0.operatorLoginAccount",null]
        - len_eq: ["content.data.authLogList.0.afterScopes",0]
        - contains: ["content.data.authLogList.0","beforeScopes"]
        - ne: ["content.data.authLogList.0.operatorOid",null]
        - ne: ["content.data.authLogList.0.operatorName",null]
        - ne: ["content.data.authLogList.0.operatorSourceDesc",null]
#        - contains: ["content.data.authLogList.0","operatorTime"]
        - ne: ["content.data.authLogList.0.operatorSourceDesc",null]



#- test:
#    name: check-查询个人授权关联数据-无授权scope
#    variables:
#        - json: {
#          "appId": $appId_0,
#          "authOid": $psnId,
#          "authType": "0",
#          "pageNum": 1,
#          "pageSize": 10
#        }
#    api: api/oauthManage/oauthManageMappingList_api.yml
#    extract:
#      - authOid_0: content.data.data.authList.0.authOid
#    validate:
#        - eq: [ "content.message", "成功" ]
#        - eq: [ "content.code", 0 ]
#        - len_gt: [ "content.data.resultList", 0 ]
#        - eq: [ "content.data.count", 1 ]
#        - eq: ["content.data.data.authList.0.appId",$appId_0]
#        - eq: ["content.data.data.authList.0.authOid",$psnId]
#        - len_le: ["content.data.data.authList.0.scopeMappings",0]


# 串场景-企业授权- 取消授权


- test:
    name: 取消企业授权
    variables:
        - headers: "${gen_headers_rest($orgId,$psnId_admin)}"
        - appId: $appId_0
    api: api/oauthManage/cancelAuth_api.yml
    validate:
        - eq: [ "content.code", 0 ]
        - eq: [ "content.message","成功" ]
        - eq: [ "content.data", true ]


- test:
    name: check-查询企业用户-应用授权记录日志
    variables:
        - headers: "${gen_headers_rest($orgId,$psnId_admin)}"
        - pageNum: 1
        - pageSize: 10
        - appId: $appId_0
    api: api/oauthManage/mappingsLogList_api.yml
    validate:
        - ne: ["content.data.authName",null]
        - eq: ["content.data.authOid",$orgId]
        - ne: ["content.data.appName",null]
        - ne: ["content.data.appLogo",null]
        - eq: ["content.data.appId",$appId]
        - gt: ["content.data.count",0]
        - ne: ["content.data.authLogList.0.operatorLoginAccount",null]
        - len_eq: ["content.data.authLogList.0.afterScopes",0]
        - contains: ["content.data.authLogList.0","beforeScopes"]
        - ne: ["content.data.authLogList.0.operatorOid",null]
        - ne: ["content.data.authLogList.0.operatorName",null]
        - ne: ["content.data.authLogList.0.operatorSourceDesc",null]
#        - contains: ["content.data.authLogList.0","operatorTime"]
        - ne: ["content.data.authLogList.0.operatorSourceDesc",null]



#- test:
#    name: check-查询企业授权关系日志
#    variables:
#        - json: {
#          "appId": $appId_0,
#          "authOid": $orgId,
#          "pageNum": 1,
#          "pageSize": 10
#        }
#    api: api/oauthManage/mappingLogList_api.yml
#    validate:
#        - ne: ["content.authName",null]
#        - eq: ["content.authOid",$orgId]
#        - ne: ["content.appName",null]
#        - ne: ["content.appLogo",null]
#        - eq: ["content.appId",$appId_0]
#        - gt: ["content.count",0]
#        - eq: ["content.data.authList.0.operatorOid",$psnId]
#        - len_le: ["content.data.authList.0.afterScopes",0]
#
#- test:
#    name: check-查询企业授权关联数据,没有授权scope
#    variables:
#        - json: {
#          "appId": $appId_0,
#          "authOid": $orgId,
#          "authType": "1",
#          "pageNum": 1,
#          "pageSize": 10
#        }
#    api: api/oauthManage/oauthManageMappingList_api.yml
#    extract:
#      - authOid_0: content.data.data.authList.0.authOid
#    validate:
#        - eq: [ "content.message", "成功" ]
#        - eq: [ "content.code", 0 ]
#        - len_gt: [ "content.data.resultList", 0 ]
#        - eq: [ "content.data.count", 1 ]
#        - eq: ["content.data.data.authList.0.appId",$appId_0]
#        - eq: ["content.data.data.authList.0.authOid",$orgId]
#        - len_le: ["content.data.data.authList.0.scopeMappings",0]





