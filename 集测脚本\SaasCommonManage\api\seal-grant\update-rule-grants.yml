 #批量编辑印章授权
    variables:
        appid: ${ENV(appid)}
    request:
        url: ${ENV(inner_open_url)}/v1/saas-common/rules-grant/seals/update-rule-grants
        method: PUT
        headers: ${gen_headers($appid, X-Tsign-Open-Tenant-Id=$tenantid, X-Tsign-Open-Operator-Id=$operatorid)}
        json:
          {
              "appScheme": $appScheme,
              "authKey": $authKey,
              "grantRedirectUrl": $grantRedirectUrl,
              "h5": $h5,
              "orgId": $orgId,
              "resourceId": $resourceId,
              "ruleGrantedList": $ruleGrantedList,
              "token": $token,
              "type": $type
          }
