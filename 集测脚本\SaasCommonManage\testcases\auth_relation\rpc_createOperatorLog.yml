- config:
    name: 线下添加关联企业记录
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      fileKey1: ${ENV(fileKey2)}
      gid1: ${ENV(orgGid1)}   #主企业
      gid2: ${ENV(orgGid2)}   #关联企业
      authTime: 1
      authUnit: year
      operatorName: 谢佳
      operatorAlias: 明绣


- test:
    name: 线下添加关联企业记录-成功
    api: api/auth_relation/rpc_createOperatorLog.yml
    variables:
      fileKey: $fileKey1
      gids: ["$gid2"]
      parentGid: $gid1
    validate:
      - eq: ["status_code", 200]
