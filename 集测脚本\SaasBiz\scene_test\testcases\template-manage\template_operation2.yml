- config:
    name: 模板基本操作-普通模板（个人空间）
    variables:
      accountId1: ${ENV(mx_accountId1)}
      account1: ${ENV(mx_account1)}
      accountName1: ${ENV(mx_accountName1)}
      orgId1: ${ENV(mx_orgId1)}
      orgName1: ${ENV(mx_orgName1)}
      fileName1: "test.pdf"
      contentMd5_1: 2FwUG2Zm3BtLVOuuYCPh7g==
      fileSize1: 99580
      filePath1: "data/test.pdf"
      contentType1: application/pdf
      templateName1: 两人仅签模板-${getTimeStamp()}1
      taskName1: 使用仅签模板发起-${getTimeStamp()}1
      flowTemplateId2: ${ENV(flowTemplateId2)}
      name1: 模板1-${getTimeStamp()}
      name2: 模板2-${getTimeStamp()}



- test:
    name: 文件直传创建文件
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $accountId1
      accountId: $accountId1
      contentType: $contentType1
      contentMd5: $contentMd5_1
      convert2Pdf: false
      fileName: $fileName1
      fileSize: $fileSize1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId1: content.data.fileId
      - uploadUrl1: content.data.uploadUrl


- test:
    name: 上传文件到oss
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl1
      filePath: $filePath1
      contentType: $contentType1
      contentMd5: $contentMd5_1
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 保存流程模板
    api: api/contract_manage/save_flowTemplate.yml
    variables:
      tenantId: $accountId1
      operatorId: $accountId1
      businessType: 0
      ccs: []
      files:
        [
        {
          "fileId": $fileId1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $fileName1,
          "from":2,                 #文件来自 1-模板文件 2-合同文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: ""
      refFlowTemplateId: ""
      initiatorAccountId: $accountId1
      participants:
        [
        {
          "participantSubjectType":0,
          "role":"3",
          "sealType":"0,1",
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":null,
          "instances":null,
          "willTypes":[]
        },
        {
          "participantSubjectType":1,
          "role":"3",
          "sealType":null,
          "signRequirements":"1",
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方2",
          "participantId":null,
          "instances":null,
          "willTypes":[]
        }
        ]
      previewType: 0
      scene: 3
      taskName: $templateName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - flowTemplateId1: content.data.processId
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}


- test:
    name: 保存流程模板-新增模板---需要判重-模板名称与已有模板重复，请修改
    api: api/contract_manage/save_flowTemplate.yml
    variables:
      tenantId: $accountId1
      operatorId: $accountId1
      businessType: 0
      ccs: []
      files:
        [
        {
          "fileId": $fileId1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $fileName1,
          "from":2,                 #文件来自 1-模板文件 2-合同文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: ""
      refFlowTemplateId: ""
      initiatorAccountId: $accountId1
      participants:
        [
        {
          "participantSubjectType":0,
          "role":"3",
          "sealType":"0,1",
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":null,
          "instances":null,
          "willTypes":[]
        },
        {
          "participantSubjectType":1,
          "role":"3",
          "sealType":null,
          "signRequirements":"1",
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方2",
          "participantId":null,
          "instances":null,
          "willTypes":[]
        }
        ]
      previewType: 0
      scene: 3
      taskName: $templateName1
    validate:
      - eq: ["content.code", *********]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}


- test:
    name: 流程模板列表
    api: api/template_manage/flowTemplates.yml
    variables:
      - tenantId: $accountId1
      - accountId: $accountId1
      - flowTemplateName: $templateName1
    validate:
      - eq: ["content.code", 0]
#      - eq: ["content.data.total", 0]


- test:
    name: 保存流程模板-修改模板
    api: api/contract_manage/save_flowTemplate_2.yml
    variables:
      - fileId: $fileId1
      - tenantId: $accountId1
      - operatorId: $accountId1
      - flowTemplateId: $flowTemplateId1
      - taskName: $name2
      - role_jia: "1"
      - role_yi: "1,3"
      - scene: 3
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.processId", null]




- test:
    name: 禁用流程模板
    api: api/template_manage/template_disable.yml
    variables:
      - flowTemplateId: $flowTemplateId1
      - tenantId: $accountId1
      - accountId: $accountId1
      - operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 删除模板
    api: api/template_manage/template_delete.yml
    variables:
      - flowTemplateId: $flowTemplateId1
      - tenantId: $accountId1
      - operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
