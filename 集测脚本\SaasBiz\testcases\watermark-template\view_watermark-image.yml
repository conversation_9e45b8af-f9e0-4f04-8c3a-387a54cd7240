- config:
    name: 查看水印中的图片


- test:
    name: 查看水印中的图片-成功
    api: api/watermark-template/view_watermark-image.yml
    variables:
      watermarkId: ac8c927a0baf4e1a9a3add213454079a
      filekey: ${ENV(filekey)}
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 查看水印中的图片-不存在的水印模板id
    api: api/watermark-template/view_watermark-image.yml
    variables:
      watermarkId: 1
      filekey: ${ENV(filekey)}
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", ********]
      - eq: ["content.message", "水印模板不存在或已被删除"]

- test:
    name: 查看水印中的图片-错误的filekey
    api: api/watermark-template/view_watermark-image.yml
    variables:
      watermarkId: ac8c927a0baf4e1a9a3add213454079a
      filekey: 1
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", ********]
      - eq: ["content.message", "当前水印模板中不包含需要查看的图片，无法查看"]