- config:
    name: 使用有填写的静态epaas模板批量发起流程
    variables:
      flowTemplateId1: d043555e64384c9d955c3759194ecec3
      orgId1: 2963014d2c9a4d0ca78ee05cde06f835
      orgName1: esigntest测试企业epaas模板专用1
      accountId1: ${ENV(mx_accountId)}
      account1: <EMAIL>
      accountName1: 明绣
      db_name1: doc-cooperation
      db_name2: epaas_doc_template
      taskName1: 使用有填写的静态epaas模板批量发起-${getTimeStamp_ms()}
      fieldId1: 2b16e91777494b318983f32ed212776a


- test:
    name: 流程发起详情信息
    api: api/contract-manager2/flowTemplateInfo.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - participantId1: content.data.participants.0.participantId
      - fileId1: content.data.files.0.fileId
      - fileName1: content.data.files.0.fileName

- test:
    name: 使用模板异步发起
    api: api/contract-manager2/start-async.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      businessType: 0
      epaasTag: true
      ccs: []
      files:
        [
        {
          "fileId": $fileId1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $fileName1,
          "from":4,                 #文件来自 1-模板文件 2-合同文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: $flowTemplateId1
      initiatorAccountId: $accountId1
      participants:
        [
        {
          "participantSubjectType":0,
          "role":"1,3",
          "sealType":"0,1",
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":$participantId1,
          "instances":[
          {
            "account":$account1,
            "accountOid":$accountId1,
            "accountName":$accountName1,
            "accountRealName":true,
            "comment":"",
            "subjectId":$accountId1,
            "subjectName":$accountName1,
            "subjectRealName":true,
            "subjectType":0,
            "preFillValues":{
              $fieldId1: "哈哈"
            },
            "subTaskName":""
          },
          {
            "account":$account1,
            "accountOid":$accountId1,
            "accountName":$accountName1,
            "accountRealName":true,
            "comment":"",
            "subjectId":$accountId1,
            "subjectName":$accountName1,
            "subjectRealName":true,
            "subjectType":0,
            "preFillValues":{
              $fieldId1: "123"
            },
            "subTaskName":""
          }
          ],
          "willTypes":[]
        }
        ]
      scene: 2
      taskName: $taskName1
      skipFill: true
      skipFillType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.processId", ""]

- test:
    name: 批量合同列表-验证流程发起成功
    api: api/contract-manager2/processGroupList.yml
    variables:
      accountId: $accountId1
      page: 1
      pageSize: 10
      authorizedAccountId: $orgId1
      signerKeyWord: null
      statusList: null
      processGroupName: $taskName1
    setup_hooks:
      - ${hook_sleep_n_secs(10)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ge: ["content.data.total", 1]
    extract:
      - processGroupId1: content.data.processGroupList.0.processGroupId

- test:
    name: 批量合同列表查询子流程-验证跳过填写成功
    api: api/contract-manager2/group_subProcessList.yml
    variables:
      processGroupId: $processGroupId1
      accountId: $accountId1
      authorizedAccountId: $orgId1
      signerKeyWord: null
      statusList: null
      page: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 2]
      - eq: ["content.data.processList.0.flowStatus", 2]

- test:
    name: 撤销合同流程组
    api: api/contract-manager2/processGroups-revoke.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processGroupId: $processGroupId1
      revokeReason: 集测撤回
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]