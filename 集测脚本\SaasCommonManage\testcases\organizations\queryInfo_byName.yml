- config:
    name: 根据企业名称获取企业具体信息
#    base_url: ${ENV(saas_common_manage_url)}


- test:
    name: 根据企业名称获取企业具体信息-orgName小于4个字符
    api: api/organizations/queryInfo_byName.yml
    variables:
      orgName: 测试
    validate:
      - eq: ["content.code", 70000007]
      - eq: ["content.message", 企业名称不可少于4个字]


- test:
    name: 根据企业名称获取企业具体信息-成功且查询结果不为空
    api: api/organizations/queryInfo_byName.yml
    variables:
      orgName: 南京韩复
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.orgInfos", []]
