- config:
    name: 重新发起时获取符合条件的审批模版（还没做好）


- test:
    name: 重新发起时获取符合条件的审批模版
    api: api/approval/template_restart.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalId: ATa19d880e382b4546b44e7c337a1e3e65
      approvalTemplateId:
      approvalType:
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["status_code", 200]
#      - eq: ["content.message", 成功]
