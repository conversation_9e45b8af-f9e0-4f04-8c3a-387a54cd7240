- config:
    name: 企业某个渠道的数据源列表
    variables:
      appId: ${ENV(appid)}
      tenantId: 08486d61823d4c2086122b26fb3e615a
      operatorId: 0bc26b27ede64cc48e7a800f4c5cbac7

- test:
    name: 查询企业下的数据源--全部
    api: api/dataSource/ChannelDataSourceList.yml
    variables:
      appId: $appId
      tenantId: $tenantId
      operatorId: $operatorId
      json:
        {
          "searchKey": "",
          "pageNum": 1,
          "pageSize": 10
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.list.0.dataSourceChannel", "ESIGN"]


- test:
    name: 查询企业下的数据源--E签宝信息采集器
    api: api/dataSource/ChannelDataSourceList.yml
    variables:
      appId: $appId
      tenantId: $tenantId
      operatorId: $operatorId
      json:
        {
          "dataSourceChannel": "ESIGN",
          "searchKey": "",
          "pageNum": 1,
          "pageSize": 10
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.list.0.dataSourceChannel", "ESIGN"]