- config:
    name: 文件核验分享场景
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      - accountId1: ${ENV(accountId1)}
      - callSorce: CONTRACT
      - shareEndTime: ""
      - shareOperateType: CHECK
      - router: verification
      - resourceType: PROCESS
      - shareTargets: []
      - orgId1: ${ENV(orgId1)}
      - shareType: ""
      - menuId: ""
      - processId1: ${ENV(processId3)}
      - processId2: ${ENV(processId1)}
      - processId3: ${ENV(processId4)}
      - accountId2: ${ENV(accountId2)}
      - accountId3: ${ENV(accountId3)}



- test:
    name: 文件核验-未完成的流程
    api: api/shares/shareInfo_getByResourceId.yml
    variables:
      accountId: $accountId1
      subjectId: $orgId1
      resourceId: $processId2
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 未完成的合同状态不允许核验]


- test:
    name: 文件核验-流程的签署文件未添加文件核验控件
    api: api/shares/shareInfo_getByResourceId.yml
    variables:
      accountId: $accountId1
      subjectId: $orgId1
      resourceId: $processId3
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", "合同文件中不存在核验二维码,不能进行核验分享"]


- test:
    name: 文件核验-当前操作人无权限
    api: api/shares/shareInfo_getByResourceId.yml
    variables:
      accountId: $accountId2
      subjectId: $orgId1
      resourceId: $processId1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", "您没有该功能的权限，请联系管理员获取"]


- test:
    name: 文件核验-当前操作人非企业成员
    api: api/shares/shareInfo_getByResourceId.yml
    variables:
      accountId: $accountId3
      subjectId: $orgId1
      resourceId: $processId1
    validate:
      - eq: ["content.code", ********]
#      - eq: ["content.message", 企业成员不存在]


- test:
    name: 根据资源id获取资源分享信息成功
    api: api/shares/shareInfo_getByResourceId.yml
    variables:
      accountId: $accountId1
      subjectId: $orgId1
      resourceId: $processId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.shareUrl", null]
      - ne: ["content.data.qrCode", null]
    extract:
      resourceShareId1: content.data.resourceShareId


- test:
    name: 修改资源分享配置-打开核验开关，且需实名
    api: api/shares/changeConfig.yml
    variables:
      accountId: $accountId1
      subjectId: $orgId1
      needRealName: true
      resourceShareId: $resourceShareId1
      status: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 根据资源分享id获取资源分享信息
    api: api/shares/shareInfo_getByResourceShareId.yml
    variables:
      resourceShareId: $resourceShareId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.resourceId", $processId1]
      - eq: ["content.data.needRealName", true]
      - eq: ["content.data.status", 1]


- test:
    name: 获取资源最终地址-非流程参与人
    api: api/shares/getResourceUrl.yml
    variables:
      accountId: $accountId2
      subjectId: $orgId1
      resourceShareId: $resourceShareId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.resourceUrl", null]
      - eq: ["content.data.hasAuth", true]


- test:
    name: 修改资源分享配置-关闭核验开关，且不需要实名
    api: api/shares/changeConfig.yml
    variables:
      accountId: $accountId1
      subjectId: $orgId1
      needRealName: false
      resourceShareId: $resourceShareId1
      status: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 根据资源分享id获取资源分享信息
    api: api/shares/shareInfo_getByResourceShareId.yml
    variables:
      resourceShareId: $resourceShareId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.needRealName", false]
      - eq: ["content.data.status", 0]


- test:
    name: 获取资源最终地址-流程参与人
    api: api/shares/getResourceUrl.yml
    variables:
      accountId: $accountId1
      subjectId: $orgId1
      resourceShareId: $resourceShareId1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 您无权访问]
