name: 修改弹窗
variables:
  fileKey: ${ENV(fileKey1)}
  linkUrl: "https://www.esign.cn"
  validityEndTime: ""
  validityStartTime: ""
  weight: 1
  notifyVersions: []
#  id: 1737

request:
  url: ${ENV(saas_common_manage_url)}/v1/saas-common/popups/update
  method: POST
  headers:
    x-timevale-jwtcontent: eyJhbGlhcyI6Iuemu-atjCIsImlkIjoibGlnZSJ9
    Content-Type: application/json
  json:
    {
      "areaCode": $areaCode,
      "configType": 0,
      "fileKey": $fileKey,
      "id": $id,
      "linkUrl": $linkUrl,
      "name": $name,
      "noticeType": 0,
      "notifyVersions": $notifyVersions,
      "popupType": 0,
      "productCode": $productCode,
      "scrollPictures": [
      {
        "fileKey": $fileKey,
        "linkUrl": $linkUrl
      }
      ],
      "title": "",
      "validityEndTime": $validityEndTime,
      "validityStartTime": $validityStartTime,
      "validityUserConfig": [
      {
        "conditionType": $conditionType,
        "configMap": $configMap,
        "hasSatisfy": $hasSatisfy,
        "type": $type
      }
      ],
      "weight": $weight
    }
