- config:
    name: 印章授权相关操作
    variables:
      orgId1: "0001095aa4bb4f178dafd418ac274a62"  #企业oid 阮小五测试企业
      adminId1: "a46d29697c50498eb9ff75655ba9bad1"  #管理员oid   阮小五
      oid1: "219461ffede84f629235751ca4967b8e" #企业成员  阮小七
      effectiveTime0: "${getTimeStamp()}" #生效开始时间
      expireTime0: "${getTimeStamp()}"  #有效期截止时间
      data: ${ENV(data)}
      refuse_fileId: "66f9b050-588f-4d23-bbee-7751127716c5"
      flowtemplateId: "029626cede054b8a921411541fa35585"  #流程模板
      flowtemplateId1: "2dbee91edb284271a429775d5a0b31e1" #流程模板
      grantedUserCode1: "912345098765432345"
      grantedUserName1: "esigntest你是真的秀"
      orgId2: "52b72c6d9ac941bebdd0431d97f2f8ab" # esigntest你是真的秀
      adminId2: "691e9d2a1aae49929cf9c2b446a1e157" #青莲
      sealId1: "85f6ad66-e798-4350-a3c2-62c26a8ad596"

#创建企业模板印章
- test:
    name: 创建企业模板印章-其他章
    variables:
        tenantid: $orgId1
        alias: "企业其他章${getTimeStamp()}"
        bottomText: qining1
        color: BLACK
        horizontalText: ""
        opacity: 80
        style: NONE
        surroundTextInner: ta
        templateType: COMMON_TWO_OVAL
        widthHeight: "45_30"
    api: api/seal/add_organizationstemplate.yml
    extract:
        - Cu_sealId: content.data.sealId
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]


#新增印章授权
- test:
    name: 印章授权-跨企业授权
    variables:
        tenantid: $orgId1
        operatorid: $adminId1
        appScheme: null
        autoFall: false
        effectiveTime: *************
        expireTime: *************
        fallType: null
        grantRedirectUrl: ""
        grantType: 2
        grantedAccountIds:
           - $orgId2
        grantedUserCode: $grantedUserCode1
        grantedUserName: $grantedUserName1
        grantedUserRole: null
        h5: null
        notifySetting: true
        notifyUrl: ""
        orgId: $orgId1
        resourceId: $Cu_sealId
        roleKey: "SEAL_USER"
        scopeList:
           - "ALL"
        token: ""
    api: api/seal-grant/add-rule-grants.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 被授权企业列表列表(获取ruleGrantedId)
    variables:
        tenantid: $orgId2
        operatorid: $adminId2
        orgId: $orgId2
        downloadFlag: true
        secSealGrantNumQueryFlag: true
        offset: 0
        size: 20
    api: api/seal-granted/rule-granted-list.yml
  #  extract:
  #      - sealGrantBizId2: content.data.grantedList.0.sealGrantBizId
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

##删除印章被授权信息
#- test:
#    name: 删除印章被授权信息
#    variables:
#        tenantid: $orgId2
#        operatorid: $adminId2
#        ruleGrantedId: $sealGrantBizId2
#    api: api/seal-granted/delete-rule-granted.yml
#    validate:
#        - eq: ["content.code",0]
#        - contained_by: ["content.message", ["成功","缺少参数"]]


#被授权企业列表
- test:
    name: 被授权企业列表列表
    variables:
        tenantid: $orgId1
        operatorid: $adminId1
        orgId: $orgId1
        downloadFlag: true
        secSealGrantNumQueryFlag: true
        offset: 0
        size: 20
    api: api/seal-granted/rule-granted-list.yml
    extract:
#        - sealGrantBizId1: content.data.grantedList.0.sealGrantBizId
        - grantedList: content.data.grantedList
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 二次授权新增-印章使用权限
    variables:
        tenantid: $orgId1
        operatorid: $adminId1
        appScheme: $orgId1
        authKey: ""
        autoFall: null
        effectiveTime: *************
        expireTime: *************
        grantedAccountIds: [$oid1]
        h5: ""
        roleKey: SEAL_USER
        scopeList: [$flowtemplateId]
        sealGrantBizId: ${getValue($grantedList, sealId, $sealId1, sealGrantBizId)}
        token: ""
    api: api/seal-granted/add-second.yml
    extract:
        - secGrantBizId1: content.data.secSealGrantBizIds.0
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 二次授权新增-全部成员-自动落章
    variables:
        tenantid: $orgId1
        operatorid: $adminId1
        appScheme: $orgId1
        authKey: ""
        autoFall: false
        effectiveTime: *************
        expireTime: *************
        grantedAccountIds: ["ALL"]
        h5: ""
        roleKey: SEAL_USER
        scopeList: [$flowtemplateId1]
        sealGrantBizId: ${getValue($grantedList, sealId, $sealId1, sealGrantBizId)}
        token: ""
    api: api/seal-granted/add-second.yml
    extract:
        - secGrantBizId2: content.data.secSealGrantBizIds.0
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 下载二次授权书
    variables:
        tenantid: $orgId1
        operatorid: $adminId1
        secondSealGrantBizId: $secGrantBizId1
    api: api/seal-granted/download-second.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","印章授权书未签署"]]

- test:
    name: 查看二次授权列表
    variables:
        tenantid: $orgId1
        operatorid: $adminId1
        sealGrantBizId: ${getValue($grantedList, sealId, $sealId1, sealGrantBizId)}
        pageNum: 1
        pageSize: 20
    api: api/seal-granted/second-list.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 删除二次授权1
    variables:
        tenantid: $orgId1
        operatorid: $adminId1
        secondSealGrantBizId: $secGrantBizId1
    api: api/seal-granted/delete-second.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 删除二次授权2
    variables:
        tenantid: $orgId1
        operatorid: $adminId1
        secondSealGrantBizId: $secGrantBizId2
    api: api/seal-granted/delete-second.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]


- test:
    name: 删除印章rpc接口-清数据
    variables:
        Rpc_sealId: $Cu_sealId
    api: api/seal/deleteSeal_rpc.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","执行成功"]
    setup_hooks:
        - ${hook_sleep_n_secs(2)}