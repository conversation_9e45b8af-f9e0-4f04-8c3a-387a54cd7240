name: 流程发起
variables:
  appId: ${ENV(appid)}
  clientId: WEB
  approveTemplateId: null
  batchDropSeal: false
  bizGroupId: null
  businessRemark: null
  canProcessCheck: false
  fileEndTime: null
  fileValidityConfig:
    {
      validityType: 3
    }
  noticeType: null
  originFileIds: []
  originProcessId: null
  platform: 5
  processNotifyUrl: null
  redirectUrl: "https://www.esign.cn"
  refFlowTemplateId: null
  requestNo: null
  rescindFileIds: []
  secretType: null
  signEndTime: null
  signValidityConfig:
    {
      validityType: 3
    }
  signedNoticeUrl: null
  skipFill: false
  skipStartValid: false
  solutionNum: null
  source: null
  taskComment: null
  token: null
  visibleAccounts: []
  useWatermarkTemplateId: ""
  supportAddDoc: false

request:
  url: ${ENV(inner_open_url)}/v2/processes/start
  method: POST
  headers: ${gen_headers($appId, X-Tsign-Open-Tenant-Id=$tenantId, X-Tsign-Open-Operator-Id=$operatorId, X-Tsign-Client-Id=$clientId)}
  json:
    {
      "approveTemplateId": $approveTemplateId,
      "batchDropSeal": $batchDropSeal,
      "bizGroupId": $bizGroupId,
      "businessRemark": $businessRemark,
      "businessType": $businessType,
      "canProcessCheck": $canProcessCheck,
      "ccs": $ccs,
      "fileEndTime": $fileEndTime,
      "fileValidityConfig": $fileValidityConfig,
      "files": $files,
      "flowTemplateId": $flowTemplateId,
      "initiatorAccountId": $initiatorAccountId,
      "noticeType": $noticeType,
      "originFileIds": $originFileIds,
      "originProcessId": $originProcessId,
      "participants": $participants,
      "platform": $platform,
      "processNotifyUrl": $processNotifyUrl,
      "redirectUrl": $redirectUrl,
      "refFlowTemplateId": $refFlowTemplateId,
      "requestNo": $requestNo,
      "rescindFileIds": $rescindFileIds,
      "scene": $scene,
      "secretType": $secretType,
      "signEndTime": $signEndTime,
      "signValidityConfig": $signValidityConfig,
      "signedNoticeUrl": $signedNoticeUrl,
      "skipFill": $skipFill,
      "skipStartValid": $skipStartValid,
      "solutionNum": $solutionNum,
      "source": $source,
      "taskComment": $taskComment,
      "taskName": $taskName,
      "token": $token,
      "visibleAccounts": $visibleAccounts,
      "supportAddDoc": $supportAddDoc,
      "useWatermarkTemplateId": $useWatermarkTemplateId
    }
