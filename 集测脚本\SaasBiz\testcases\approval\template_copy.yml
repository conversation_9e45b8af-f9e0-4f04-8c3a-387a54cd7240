- config:
    name: 复制审批模版（完成）

- test:
    name: 复制审批模版,合同审批
    api: api/approval/template_copy.yml
    variables:
      operatorId: 691e9d2a1aae49929cf9c2b446a1e157
      tenantId: 52b72c6d9ac941bebdd0431d97f2f8ab
      approvalTemplateId : AT39E102635B824301A81F0DB9C1A1776F
      approvalTemplateType: 2

    extract:
      approvalTemplateId1: content.data.approvalTemplateId
    validate:
      - eq: ["content.code", 0]
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]


- test:
    name: 删除审批模版,合同审批
    api: api/approval/template_del.yml
    variables:
      operatorId: 691e9d2a1aae49929cf9c2b446a1e157
      tenantId: 52b72c6d9ac941bebdd0431d97f2f8ab
      approvalTemplateId : $approvalTemplateId1
      approvalTemplateType: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]


- test:
    name: 复制审批模版,用印审批
    api: api/approval/template_copy.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalTemplateId : AFT-1704426986001011904
      approvalTemplateType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
    extract:
      approvalTemplateId2: content.data.approvalTemplateId


- test:
    name: 删除审批模版,用印审批
    api: api/approval/template_del.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalTemplateId : $approvalTemplateId2
      approvalTemplateType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
