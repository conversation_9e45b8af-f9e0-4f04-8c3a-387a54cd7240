- config:
    name: 印章可见范围具体操作
    variables:
      accountId1: ${ENV(nss_accountId1)}
      account1: ***********
      accountName1: 隆多
      accountId2: ${ENV(nss_accountId2)}   #青莲
      orgId1: ${ENV(nss_orgid1)}  #esigntest你是真的秀
      assignedDeptId1: 5b12c659d8d84a78ad8f7e82bc85039f #部门id 炮兵营
      sealName1: 企业人事章${getTimeStamp_ms()}
      db_name1: seal



#创建企业模板印章
- test:
    name: 创建企业模板印章-人事章
    variables:
        tenantid: $orgId1
        alias: $sealName1
        bottomText: ren
        color: RED
        horizontalText: shi
        opacity: 80
        style: NONE
        surroundTextInner: ${getTimeStamp_ms()}
        templateType: PERSONNEL_ROUND_STAR
        widthHeight: "38_38"
    api: api/seal/add_organizationstemplate.yml
    extract:
        - Cu_sealId: content.data.sealId
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

#设置可见范围
- test:
    name: 设置印章可见范围
    variables:
        tenantid: $orgId1
        OperatorId: $accountId2
        assignedDeptIdList:
            - $assignedDeptId1
        assignedMemberIdList:
            - $accountId1
        visibleScope: PART_MEMBER
        sealId: $Cu_sealId
    api: api/seal/set_seal_scope.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

#查询可见范围
- test:
    name: 查询印章可见范围
    variables:
        tenantid: $orgId1
        OperatorId: $accountId2
        sealId: $Cu_sealId
    api: api/seal/query_seal_scope.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#查询部分成员可见范围
- test:
    name: 查询印章部分成员可见范围-部门
    variables:
        tenantid: $orgId1
        OperatorId: $accountId2
        sealId: $Cu_sealId
        pageNo: 1
        pageSize: 8
        type: ORGAN_DEPT
    api: api/seal/query_seal_partscope.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
        - eq: ["content.data.visibleDept.partDeptList.0.deptId",$assignedDeptId1]


- test:
    name: 查询印章部分成员可见范围-成员
    variables:
        tenantid: $orgId1
        OperatorId: $accountId2
        sealId: $Cu_sealId
        pageNo: 1
        pageSize: 8
        type: ORGAN_MEMBER
    api: api/seal/query_seal_partscope.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
        - eq: ["content.data.visibleMember.partMemberList.0.memberId",$accountId1]


#删除可见范围
- test:
    name: 删除印章可见范围
    variables:
        tenantid: $orgId1
        OperatorId: $accountId2
        deptIdList:
            - $assignedDeptId1
        memberOidList:
            - $accountId1
        sealId: $Cu_sealId
    api: api/seal/del_seal_scope.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}


- test:
    name: 删除印章rpc接口-清数据
    variables:
        sql1: "SELECT uuid FROM seal.seal_ref where oid='$orgId1' and name='$sealName1' and status=1;"
        Rpc_sealId: ${select_sql($sql1, $db_name1)}
    api: api/seal/deleteSeal_rpc.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","执行成功"]
    teardown_hooks:
        - ${hook_sleep_n_secs(3)}