- config:
    name: 保存用户合同偏好设置，并直接发起
    variables:
      - accountId: ${ENV(mx_accountId1)}
        orgId: ${ENV(mx_orgId1)}
        taskName1: 直接发起2-${getTimeStamp_ms()}
        accountId1: ${ENV(mx_accountId1)}
        account1: ${ENV(mx_account1)}
        accountName1: ${ENV(mx_accountName1)}
        orgId1: ${ENV(mx_orgId1)}
        orgName1: ${ENV(mx_orgName1)}
        fileName1: "test.pdf"
        contentMd5_1: 2FwUG2Zm3BtLVOuuYCPh7g==
        fileSize1: 99580
        filePath1: "data/test.pdf"
        contentType1: application/pdf
        templateId1: "76ae214cab4f4e5cbff629de63d60307"
        fileId1: "6d92109c97ac4489b0580b3c4d34ad1d"
        OperatorId1: ${ENV(mx_accountId1)}


#合同编号规则保存
- test:
    name: 合同编号规则保存-关联模板
    variables:
      TenantId: $orgId1
      OperatorId: $OperatorId1
      accountId: ""
      initNumber: 10001
      prefix: ning${getTimeStamp()}
      ruleName: 测试合同编号
      subjectId: ""
      tailNumber: 5
      tailType: 2
      timeType: 3
      templates:
        - files:
            - fileId: $fileId1
              fileName: 劳动合同.pdf
          templateId: $templateId1
    api: api/contract_manage/contractNo/contractNo_save.yml
    extract:
      - ruleid1: content.data
    validate:
      - eq: [ "content.code",0 ]
      - contained_by: [ "content.message", [ "成功","缺少参数" ] ]


- test:
    name: 保存用户合同偏好设置-合同编号-自定义规则生成
    api: api/contract_manage/preferences/savePreferences.yml

    variables:
      - preferences: [ { "preferenceKey": "process_archive_range","preferenceValue": "ALL" },
                       { "preferenceKey": "process_opponent_source","preferenceValue": "2" },
                       { "preferenceKey": "process_auto_transfer","preferenceValue": "false" },
                       { "preferenceKey": "process_default_receiver","preferenceValue": "0bc26b27ede64cc48e7a800f4c5cbac7" },
                       { "preferenceKey": "process_will_types","preferenceValue": "FACE,CODE_SMS,EMAIL,SIGN_PWD,FACE_AUDIO_VIDEO_DUAL" },
                       { "preferenceKey": "process_direct_switch","preferenceValue": "1" },
                       { "preferenceKey": "new_share_download_open","preferenceValue": "true" },
                       { "preferenceKey": "contract_no_switch","preferenceValue": 2 },
                       { "preferenceKey": "contract_person_sign_method","preferenceValue": "0,1,2" },
                       { "preferenceKey": "contract_expire_notice_switch","preferenceValue": true },
                       { "preferenceKey": "contract_expire_notice_advance","preferenceValue": "30" },
                       { "preferenceKey": "contract_no_switch","preferenceValue": "{\"type\":3,\"ruleId\":\"$ruleid1\"}" }
      ]
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]


- test:
    name: 文件直传创建文件
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      contentType: $contentType1
      contentMd5: $contentMd5_1
      convert2Pdf: false
      fileName: $fileName1
      fileSize: $fileSize1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - fileId1: content.data.fileId
      - uploadUrl1: content.data.uploadUrl


- test:
    name: 上传文件到oss
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl1
      filePath: $filePath1
      contentType: $contentType1
      contentMd5: $contentMd5_1
    validate:
      - eq: [ "content.errCode", 0 ]
      - eq: [ "content.msg", 成功 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}


- test:
    name: 直接发起
    api: api/contract_manage/startProcess.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      businessType: 0
      ccs: [ {
        "account": $account1,
        "accountOid": $accountId1,
        "accountName": $accountName1,
        "accountNick": "",
        "accountRealName": true,
        "comment": "",
        "subjectId": $accountId1,
        "subjectName": $accountName1,
        "subjectRealName": true,
        "subjectType": 0
      } ]
      files:
        [
          {
            "fileId": $fileId1,
            "fileType": 1,             #文件类型，1-合并文件 2-附件
            "fileName": $fileName1,
            "from": 2,                 #文件来自 1-模板文件 2-合同文件
            "fileSecret": false,       #文件是否保密
#            "contractNo": "testNo",
            "contractNo": $ruleid1,
            "contractNoType": 2
          }
        ]
      flowTemplateId: ""
      initiatorAccountId: $accountId1
      participants:
        [
          {
            "participantSubjectType": 1,
            "role": "3",
            "sealType": null,
            "signRequirements": "1,3",
            "roleSet": 1,
            "fillOrder": 0,
            "signOrder": 1,
            "participantLabel": "签署方1",
            "participantId": null,
            "instances": [
              {
                "account": $account1,
                "accountOid": $accountId1,
                "accountName": $accountName1,
                "accountRealName": true,
                "comment": "",
                "subjectId": $orgId1,
                "subjectName": $orgName1,
                "subjectRealName": true,
                "subjectType": 1,
                "preFillValues": null,
                "subTaskName": ""
              }
            ],
            "willTypes": [ "FACE", "CODE_SMS", "EMAIL", "SIGN_PWD" ]
          },
          {
            "participantSubjectType": 0,
            "role": "3",
            "sealType": "1",
            "signRequirements": null,
            "roleSet": 1,
            "fillOrder": 0,
            "signOrder": 1,
            "participantLabel": "签署方2",
            "participantId": null,
            "instances": [
              {
                "account": $account1,
                "accountOid": $accountId1,
                "accountName": $accountName1,
                "accountRealName": true,
                "comment": "",
                "subjectId": $accountId1,
                "subjectName": $accountName1,
                "subjectRealName": true,
                "subjectType": 0,
                "preFillValues": null,
                "subTaskName": ""
              }
            ],
            "willTypes": [ "FACE", "CODE_SMS", "EMAIL", "SIGN_PWD" ]
          }
        ]
      scene: 1
      taskName: $taskName1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}


- test:
    name: 验证合同列表页合同编号准确性
    api: api/contract_manage/process_list2.yml
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        accountId: $accountId1
        subjectId: $orgId1
        docQueryType: 1
        fuzzyMatching: $taskName1
        processStatusList: ""
        pageNum: 1
        pageSize: 10
        withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.processInfoList.0.contractFiles.0.contractNo",$ruleid1]
    extract:
      - processId: content.data.processInfoList.0.processId


#修改规则
- test:
    name: 合同编号规则修改-取消关联模板
    variables:
      TenantId: $orgId1
      OperatorId: $OperatorId1
      ruleId: $ruleid1
      accountId: ""
      initNumber: 10001
      prefix: ning${getTimeStamp()}
      ruleName: 测试合同编号
      subjectId: ""
      tailNumber: 5
      tailType: 2
      timeType: 3
      templates: [ ]
    api: api/contract_manage/contractNo/contractNo_modify.yml
    validate:
      - eq: [ "content.code",0 ]
      - contained_by: [ "content.message", [ "成功","缺少参数" ] ]


- test:
    name: 保存用户合同偏好设置-合同编号
    api: api/contract_manage/preferences/savePreferences.yml

    variables:
      - preferences: [ { "preferenceKey": "process_archive_range","preferenceValue": "ALL" },
                       { "preferenceKey": "process_opponent_source","preferenceValue": "2" },
                       { "preferenceKey": "process_auto_transfer","preferenceValue": "false" },
                       { "preferenceKey": "process_default_receiver","preferenceValue": "0bc26b27ede64cc48e7a800f4c5cbac7" },
                       { "preferenceKey": "process_will_types","preferenceValue": "FACE,CODE_SMS,EMAIL,SIGN_PWD,FACE_AUDIO_VIDEO_DUAL" },
                       { "preferenceKey": "process_direct_switch","preferenceValue": "1" },
                       { "preferenceKey": "new_share_download_open","preferenceValue": "true" },
                       { "preferenceKey": "contract_no_switch","preferenceValue": 2 },
                       { "preferenceKey": "contract_person_sign_method","preferenceValue": "0,1,2" },
                       { "preferenceKey": "contract_expire_notice_switch","preferenceValue": true },
                       { "preferenceKey": "contract_expire_notice_advance","preferenceValue": "30" },
                       { "preferenceKey": "contract_no_switch","preferenceValue": "{\"type\":2}" }
      ]
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]


#删除规则
- test:
    name: 删除规则
    variables:
      TenantId: $orgId1
      OperatorId: $OperatorId1
      ruleId: $ruleid1
    api: api/contract_manage/contractNo/contractNo_del.yml
    validate:
      - eq: [ "content.code",0 ]
      - contained_by: [ "content.message", [ "成功","缺少参数" ] ]
