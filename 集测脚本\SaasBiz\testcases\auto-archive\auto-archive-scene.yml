- config:
    name: 归档条件串场景
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      menuId1: 16b969cb07fe4f6c95cb16cfed24e93d
      menuName1: 集测专用
      formId1: aa11061a77f44b019146d6575a05e912

- test:
    name: 校验菜单是否存在规则-不存在
    api: api/auto-archive/exist-menu-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", false]

- test:
    name: 归档系统条件
    api: api/auto-archive/system-rule-config.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      bizType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fieldId1: content.data.0.fieldId
      - fieldName1: content.data.0.fieldName

- test:
    name: 规则条件校验
    api: api/auto-archive/check-rule-conditions.yml
    variables:
      tenantId: $orgId1
      operators:
        [
        {
          "fieldId":"$fieldId1",
          "key":"${getTimeStamp_ms()}",
          "value":[
            "集测归档条件"
          ],
          "errorMessage":"",
          "childOperatorType":2,
          "startNum":"",
          "endNum":"",
          "matchType":3,
          "operatorType":1,
          "childOperators":[
          {
            "operatorType":2,
            "conditionParams":"[\"集测归档条件\"]"
          }
          ],
          "fieldType":3
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", []]

- test:
    name: 保存归档规则-不设置台账
    api: api/auto-archive/save-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      bindingFormId: ""
      bindingMenuId: $menuId1
      conditions:
        [
        {
          "fieldId":"$fieldId1",
          "key":"${getTimeStamp_ms()}",
          "value":[
            "集测归档条件"
          ],
          "errorMessage":"",
          "childOperatorType":2,
          "startNum":"",
          "endNum":"",
          "matchType":3,
          "operatorType":1,
          "childOperators":[
          {
            "operatorType":2,
            "conditionParams":"[\"集测归档条件\"]"
          }
          ],
          "fieldType":3
        }
        ]
      removeContract: 1
      status: 0
      unityForm: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 归档列表
    api: api/auto-archive/rule-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formName: ""
      menuId: $menuId1
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.menuList.0.fieldNames.0", $fieldName1]
      - contained_by: ["content.data.menuList.0.ruleStatus", [0,3]]
    extract:
      - ruleId1: content.data.menuList.0.ruleId

- test:
    name: 获取归档进度
    api: api/auto-archive/get-rule-progress.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      ruleId: $ruleId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ge: ["content.data.completed", 0]

- test:
    name: 校验菜单是否存在规则-不存在
    api: api/auto-archive/exist-menu-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", true]

- test:
    name: 查询归档规则
    api: api/auto-archive/get-rule-detail.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      ruleId: $ruleId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.name", $menuName1]
      - eq: ["content.data.menuId", $menuId1]
      - ne: ["content.data.conditions", []]

- test:
    name: 归档条件置顶
    api: api/auto-archive/top-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      ruleId: $ruleId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 归档列表-验证置顶成功
    api: api/auto-archive/rule-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formName: ""
      menuId: ""
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.menuList.0.menuId", $menuId1]
      - ne: ["content.data.menuList.0.topTime", null]

- test:
    name: 归档条件取消置顶
    api: api/auto-archive/top-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      ruleId: $ruleId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 归档列表-验证取消置顶成功
    api: api/auto-archive/rule-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formName: ""
      menuId: $menuId1
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.menuList.0.topTime", null]

- test:
    name: 停止运行归档规则
    api: api/auto-archive/stop-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      ruleId: $ruleId1
    validate:
      - contained_by: ["content.code", [0,*********]]
      - contained_by: ["content.message", ["成功","已经运行完毕，无法停止，您可以修改设置重新运行"]]

- test:
    name: 修改归档规则-设置台账
    api: api/auto-archive/update-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      bindingFormId: $formId1
      bindingMenuId: $menuId1
      conditions:
        [
        {
          "fieldId":"$fieldId1",
          "key":"${getTimeStamp_ms()}",
          "value":[
            "集测归档条件"
          ],
          "errorMessage":"",
          "childOperatorType":2,
          "startNum":"",
          "endNum":"",
          "matchType":3,
          "operatorType":1,
          "childOperators":[
          {
            "operatorType":2,
            "conditionParams":"[\"集测归档条件\"]"
          }
          ],
          "fieldType":3
        }
        ]
      modifyMark: true
      removeContract: 1
      ruleId: $ruleId1
      sourceBindingFormId: $formId1
      status: 0
      unityForm: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 停止运行归档规则
    api: api/auto-archive/stop-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      ruleId: $ruleId1
    validate:
      - contained_by: ["content.code", [0,*********]]
      - contained_by: ["content.message", ["成功","已经运行完毕，无法停止，您可以修改设置重新运行"]]

#- test:
#    name: 修改归档状态-启用
#    api: api/auto-archive/update-rule-status.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId1
#      ruleId: $ruleId1
#      status: 0
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]

#- test:
#    name: 重新运行归档规则
#    api: api/auto-archive/rerun-rule.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId1
#      ruleId: $ruleId1
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]

- test:
    name: 归档列表-验证台账状态
    api: api/auto-archive/rule-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formName: ""
      menuId: $menuId1
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
#      - contained_by: ["content.data.menuList.0.ruleStatus", [0,3]]

- test:
    name: 获取归档进度
    api: api/auto-archive/get-rule-progress.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      ruleId: $ruleId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ge: ["content.data.completed", 0]

- test:
    name: 停止运行归档规则
    api: api/auto-archive/stop-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      ruleId: $ruleId1
    validate:
      - contained_by: ["content.code", [0,*********]]
      - contained_by: ["content.message", ["成功","已经运行完毕，无法停止，您可以修改设置重新运行"]]

- test:
    name: 修改归档状态-启用
    api: api/auto-archive/update-rule-status.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      ruleId: $ruleId1
      status: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 修改归档状态-停用
    api: api/auto-archive/update-rule-status.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      ruleId: $ruleId1
      status: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 归档列表-验证停用成功
    api: api/auto-archive/rule-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formName: ""
      menuId: $menuId1
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
 #     - eq: ["content.data.menuList.0.ruleStatus", 1]

- test:
    name: 删除归档条件
    api: api/auto-archive/delete-rule.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
