- config:
    name: 条件场景增配
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 单个增配功能新增
    api: api/rpc/vipAdditionConditionAdd.yml
    variables:
      subjectOid: cf37549b29cb4c7bb52103b0eab3b590
      subjectGid: 89562cc6dfd94914b2d343af143fd09f
      funcCode: info_collect
      effectiveEndTime: *************
      effectiveStartTime: *************
    validate:
      - eq: ["status_code", 200]

- test:
    name: 增配功能启停
    api: api/rpc/changeVipAdditionStatus.yml
    variables:
      accountGid: "89562cc6dfd94914b2d343af143fd09f"
      alias: "岸止"
      funcCode:  "info_collect"
      status: "DISABLE"
    validate:
      - eq: [ "status_code", 200 ]

- test:
    name: 单个增配功能删除
    api: api/rpc/deleteVipAddition.yml
    variables:
      json:
        {
          "accountGid": "89562cc6dfd94914b2d343af143fd09f",
          "alias": "岸止",
          "funcCode": "info_collect"
        }
    validate:
      - eq: [ "status_code", 200 ]
