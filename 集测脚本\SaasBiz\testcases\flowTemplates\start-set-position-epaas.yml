- config:
    name: 指定位置发起（epaas文档）
    variables:
      fileMd5: m49PzxWxAG8sMwNkZrlJDw==
      fileType1: application/pdf
      fileName1: "test.pdf"
      fileSize1: 99580
      filePath1: "data/test.pdf"
      taskName1: 指定位置发起(epaas)-${getTimeStamp_ms()}
      orgId1: 2963014d2c9a4d0ca78ee05cde06f835
      orgName1: esigntest测试企业epaas模板专用1
      accountId1: ${ENV(mx_accountId)}
      account1: <EMAIL>
      accountName1: 明绣
      db_name1: doc-cooperation
      db_name2: epaas_doc_template


- test:
    name: 文件直传创建文件
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      contentMd5: $fileMd5
      contentType: $fileType1
      convert2Pdf: false
      fileName: $fileName1
      fileSize: $fileSize1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId1: content.data.fileId
      - uploadUrl1: content.data.uploadUrl

- test:
    name: 上传文件到oss
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl1
      contentType: $fileType1
      contentMd5: $fileMd5
      filePath: $filePath1
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 基于文件/模板创建ePaas文档
    api: api/flow-templates/create-doc.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      resourceList:
        [
        {
          "fileId": "$fileId1",
          "templateType": 0,
          "templateName": "$fileName1"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - epaasFileId1: content.data.templates.0.templateId

- test:
    name: 保存流程模板
    api: api/contract-manager2/save_flowTemplate.yml
    variables:
      tenantId: $orgId1
      approveTemplateId: null
      businessType: 0
      ccs: []
      files:
        [
        {
          "fileId": $epaasFileId1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $fileName1,
          "from":4,                 #文件来自 1-模板文件 2-合同文件 3-动态文件 4-epaas文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: ""
      initiatorAccountId: $accountId1
      participants:
        [
        {
          "participantSubjectType":1,
          "role":"3",
          "sealType":null,
          "signRequirements":"1",
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":null,
          "instances":[
          {
            "account":$account1,
            "accountOid":$accountId1,
            "accountName":$accountName1,
            "accountRealName":true,
            "comment":"",
            "subjectId":$orgId1,
            "subjectName":$orgName1,
            "subjectRealName":true,
            "subjectType":1,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[]
        }
        ]
      previewType: 0
      scene: 5
      taskName: $taskName1
      epaasTag: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - flowTemplateId1: content.data.flowTemplateId

- test:
    name: 获取epaas文档编辑地址-获取tplToken
    api: api/epaas-doc-template/getDocumentsEditUrl.yml
    variables:
      json: {
        "serviceId": "doc-cooperation",
        "businessContext": {
          "clientId": "WEB",
          "flowTemplateId": $flowTemplateId1,
          "limitLoginUserOid": $accountId1,
          "batchDropSeal": "true",
          "bizScene": "SPECIFY"
        },
        "pageParams": {
          "template.global.showFieldsAddByAI": false,
          "template.global.showReplaceFile": false,
          "template.global.showCustomFields": false,
          "template.global.showFieldsAddByKeywords": false,
          "template.global.showDownloadSourceFile": false,
          "template.global.showWatermark": true,
          "template.global.showCustomFieldsCreate": false,
          "template.global.showCopyTemplateId": false,
          "template.edit.title": "直接发起"
        },
        "fieldOwner": {
          "oid": $orgId1,
          "appId": "${ENV(app_id)}"
        },
        "expireAt": "${todayEnd_getTimeStamp_ms()}",
        "ids": [$epaasFileId1],
        "invalidAfterSave": false,
        "needLogin": true,
        "tenantId": $orgId1,
        "validationConfig": {
          "allFieldBindRole": true,
          "sealType": true
        }
      }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - tplToken1: content.data.tplToken

- test:
    name: 获取resourceId
    api: api/epaas-doc-template/content-draft.yml
    variables:
      tenantId: $orgId1
      token: $tplToken1
      sql1: "select content_id from test_epaas_doc_template.content where entity_id='$epaasFileId1';"
      contentId: ${select_sql($sql1, $db_name2)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - resourceId1: content.data.originFile.resourceId

- test:
    name: 获取templateRoleId
    api: api/epaas-doc-template/list-template-role.yml
    variables:
      tenantId: $orgId1
      token: $tplToken1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - templateRoleId1: content.data.0.id

- test:
    name: 获取参与方可指定的企业印章列表
    api: api/flow-templates/participant-seals.yml
    variables:
      queryType: "ASSIGNABLE_SEAL"
      flowTemplateId: $flowTemplateId1
      queryParam:
        {
          participantId: $templateRoleId1,
          signRequirement: "1"
        }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: epaas批量保存草稿
    api: api/epaas-doc-template/batch-save-draft.yml
    variables:
      tenantId: $orgId1
      token: $tplToken1
      sql1: "select content_id from test_epaas_doc_template.content where entity_id='$epaasFileId1';"
      data:
        [
        {
          "contentId": "${select_sql($sql1, $db_name2)}",
          "baseFile": {
            "resourceId": $resourceId1,
            "fileType": "PDF"
          },
          "fields": [
          {
            "label": "企业章",
            "custom": false,
            "type": "SIGN",
            "sort": 1,
            "style": {
              "font": 1,
              "fontSize": 12,
              "textColor": "#000",
              "width": 150,
              "height": 150,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": 1,
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "ext": "{}"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": "yyyy-MM-dd HH:mm:ss",
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "dataSource": null,
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": null,
              "overflowType": 2,
              "minFontSize": 8,
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": "1",
              "tickOptions": null,
              "configExt": {
                "signRequirements": "1",
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-stamp",
                "assignedPosbean": false,
                "fastCheck": true,
                "addSealRule": "followSeal",
                "keyPosX": 0,
                "keyPosY": 0
              },
              "sealTypes": [
                "ORG"
              ]
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "",
            "templateRoleId": $templateRoleId1,
            "fieldKey": null,
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 439.98572254076,
              "y": 731.4534*********,
              "page": 1,
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          }
          ],
          "pageFormatInfoParam": null
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]

- test:
    name: 指定位置后发起
    api: api/contract-manager2/startByFlowTemplate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      batchDropSeal: false
      businessType: 0
      flowTemplateId: $flowTemplateId1
      initiatorAccountOid: $accountId1
      scene: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.processId", null]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    extract:
      - flowId1: content.data.flowId
      - processId1: content.data.processId

- test:
    name: 查询异步发起结果
    api: api/contract-manager2/process-result-poll.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: $processId1
    setup_hooks:
      - ${hook_sleep_n_secs(10)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.done", true]

- test:
    name: 查询企业合同列表，验证流程状态为签署中
    api: api/offline_contract/grouping_lists.yml
    variables:
      tenantId: $orgId1
      OperatorId: $accountId1
      pageSize: 10
      pageNum: 1
      menuId: null
      matching: '[{"key":"processId","value":["$processId1"],"sort":"","isPublic":false},{"key":"processCreateTime","value":null,"sort":"","isPublic":false},{"key":"personName","value":[""],"sort":"","isPublic":false}]'
      withApproving: true
    setup_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.groupingProcessList.0.processStatus", 2]

- test:
    name: 撤回流程
    api: api/contract-manager/revoke-process.yml
    variables:
      operatorId: $accountId1
      processId: $processId1
      revokeReason: 集测撤回
      subjectId: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]