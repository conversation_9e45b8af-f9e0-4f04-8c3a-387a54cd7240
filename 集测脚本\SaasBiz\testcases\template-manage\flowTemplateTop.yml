- config:
    name: 模板置顶/取消置顶
    variables:
      orgId1: ${ENV(orgId2)}                   #基础版以上的企业
      flowTemplateId1: ${ENV(flowTemplateId1)} #orgId1下的流程模板
      roleId1: ${ENV(roleId1)}                 #orgId1企业下编辑模板权限的角色id
      accountId1: ${ENV(accountId1)}           #orgId1和orgId2的管理员
      accountId2: ${ENV(accountId4)}           #orgId1下的普通成员
      accountId3: ${ENV(accountId3)}           #不是orgId1下的成员
      orgId2: ${ENV(orgId3)}                   #基础版的企业
      flowTemplateId2: ${ENV(flowTemplateId3)} #orgId2下的流程模板
      db_name1: contract_manager
      sql1: "SELECT case when COUNT(*) > 0 then FALSE else TRUE end FROM doc_cooperation.resource_top where resource_id='$flowTemplateId1' and oid='$orgId1';"


- test:
    name: 模板置顶/取消置顶-flowTemplateId为空
    api: api/template-manage/flowTemplateTop.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: ""
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 流程模板id不能为空]

- test:
    name: 模板置顶/取消置顶-flowTemplateId不存在
    api: api/template-manage/flowTemplateTop.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: 123
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 流程模板不存在]

- test:
    name: 模板置顶/取消置顶-flowTemplateId不属于当前企业
    api: api/template-manage/flowTemplateTop.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
#      - contains: ["content.message", 流程模板拥有者不匹配]

- test:
    name: 模板置顶/取消置顶-操作人不是企业成员
    api: api/template-manage/flowTemplateTop.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId3
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
#      - contains: ["content.message", 企业成员不存在]

#- test:
#    name: 模板置顶/取消置顶-操作人无该模板的编辑权限
#    api: api/template-manage/flowTemplateTop.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId2
#      flowTemplateId: $flowTemplateId1
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", *********]
#      - contains: ["content.message", "您没有模板编辑权限"]



- test:
    name: 根据分组获取可操作的角色列表
    api: api/role/getRoleList.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      group: TEMP_GROUP
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - roleList: content.data     #企业下模板授权的roleList

- test:
    name: 流程模板批量授权-给accountId2设置模板的编辑权限
    api: api/template-manage/batchAuth.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      roleId2: ${getValue($roleList, roleKey, TEMP_USE, id)}  #企业下可使用的roleId
      roleId3: ${getValue($roleList, roleKey, TEMP_UPDATE, id)}  #企业下可编辑的roleId
      flowTemplateAuthList:
        [
        {
          "authList":[
          {
            "authId":"ALL",
            "accountOid":"",
            "roleId":"$roleId2",
            "roleKey":"TEMP_USE",
            "type":1
          },
          {
            "authId":null,
            "accountOid":"$accountId2",
            "roleId":"$roleId3",
            "roleKey":"TEMP_UPDATE",
            "type":2
          }
          ],
          "flowTemplateId":"$flowTemplateId1"
        }
        ]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

#- test:
#    name: 模板置顶/取消置顶-操作人有该模板的编辑权限
#    api: api/template-manage/flowTemplateTop.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId2
#      flowTemplateId: $flowTemplateId1
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]

- test:
    skipIf: ${select_sql($sql1, $db_name1)}   #上一步操作的是置顶则不跳过
    name: 流程模板列表
    api: api/template-manage/flowTemplateList.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 1
      status: ""
      flowTemplateName: ""
      queryLabel: true
      label: ""
      containShared: true
      excludeLabels: ""
      queryUse: false
      containsDynamic: true
      categoryId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.list.0.flowTemplateId", $flowTemplateId1]
      - eq: ["content.data.list.0.topped", true]

- test:
    name: 流程模板批量授权-取消accountId2的模板编辑权限
    api: api/template-manage/batchAuth.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      roleId2: ${getValue($roleList, roleKey, TEMP_USE, id)}  #企业下可使用的roleId
      flowTemplateAuthList:
        [
        {
          "authList":[
          {
            "authId":"ALL",
            "accountOid":"",
            "roleId":"$roleId2",
            "roleKey":"TEMP_USE",
            "type":1
          }
          ],
          "flowTemplateId":"$flowTemplateId1"
        }
        ]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-给accountId2设置全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 模板置顶/取消置顶-操作人有全局模板编辑权限
    api: api/template-manage/flowTemplateTop.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    skipIf: ${select_sql($sql1, $db_name1)}   #上一步操作的是置顶则不跳过
    name: 流程模板列表
    api: api/template-manage/flowTemplateList.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 1
      status: ""
      flowTemplateName: ""
      queryLabel: true
      label: ""
      containShared: true
      excludeLabels: ""
      queryUse: false
      containsDynamic: true
      categoryId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.list.0.flowTemplateId", $flowTemplateId1]

- test:
    name: 更新成员所有信息-取消accountId2的全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: []
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 模板置顶/取消置顶-管理员默认有权限
    api: api/template-manage/flowTemplateTop.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 模板置顶/取消置顶-基础版的企业
    api: api/template-manage/flowTemplateTop.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
