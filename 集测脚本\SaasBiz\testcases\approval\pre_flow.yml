- config:
    name: 选择模版时查看审批流


- test:
    name: 选择模版时查看审批流--成功
    api: api/approval/pre_flow.yml
    variables:
      operatorId: 291e8283e53f4402baf85956d3b7f7b3
      tenantId: 60b8595f05444c7c981de67eb7540856
      "approvalTemplateId": "AT70537fc4a5f24a8ab4e357d812f153f5"
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 选择模版时查看审批流--审批流模板不存在
    api: api/approval/pre_flow.yml
    variables:
      operatorId: 291e8283e53f4402baf85956d3b7f7b3
      tenantId: 60b8595f05444c7c981de67eb7540856
      "approvalTemplateId": "123456"
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ "content.code", 110000001 ]
      - eq: [ "content.message", 审批流模版不存在 ]
