- config:
    name: 企业空间+仅签模板+签署份数不足
    variables:
      tenantId_1: ${ENV(ls_orgId2)}                       # 当前空间主体
      operatorId_1: ${ENV(ls_oid)}                        # 操作人oid
      initiatorAccountId: ${ENV(ls_oid)}                  # 发起人的oid
      account_1: ${ENV(ls_account)}                       # 参与人手机号
      account_2: ${ENV(mx_account2)}
      accountOid_1: ${ENV(ls_oid)}                        # 参与人oid
      accountOid_2: ${ENV(mx_accountId2)}
      subject_1: ${ENV(ls_orgId2)}                        # 参与人主体oid
      subject_2: ${ENV(mx_accountId2)}
      accountName_1: ${ENV(ls_accountName)}               # 参与人姓名
      accountName_2: ${ENV(mx_accountName2)}
      subjectName_1: ${ENV(ls_orgName2)}                  # 参与人的主体名称
      subjectName_2: ${ENV(mx_accountName2)}
      css_account_1: ${ENV(mx_account1)}                  # 抄送人的手机号
      css_account_name_1: ${ENV(mx_accountName1)}         # 抄送人姓名
      css_accountOid_1: ${ENV(mx_accountId1)}             # 抄送人oid
      css_subject_1: ${ENV(mx_orgId1)}                    # 抄送人主体oid
      css_subjectName_1: ${ENV(mx_orgName1)}
      fileId_1: 71a6d397844f4e37a94d303acaaea151          # 模板的文件id
      file_name1: 劳动合同.pdf                            # 文件名称
      taskName: 仅签模板_份数不足${generate_random_str(3)}
      flowTemplateId: ${ENV(ls_flowTemplateId_4)}         # 流程模板ID
      approveTemplateId: ""                               # 合同审批ID
      
      
- test:
    name: 获取流程发起的详情信息
    api: api/contract_manage/processDetail.yml
    variables:
      tenantId: $tenantId_1
      operatorId: $operatorId_1
      flowTemplateId: $flowTemplateId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - participantId1: content.data.participants.0.participantId
      - participantId2: content.data.participants.1.participantId
      
- test:
    name: 使用模板发起(签署份数不足)
    api: api/contract_manage/startProcess.yml
    variables:
      tenantId: $tenantId_1
      operatorId: $operatorId_1
      businessType: 0
      ccs: [
            {
              "account": $css_account_1,
              "accountOid": $css_accountOid_1,
              "accountName": $css_account_name_1,
              "accountNick":"",
              "accountRealName":true,
              "comment":"",
              "subjectId": $css_subject_1,
              "subjectName": $css_subjectName_1,
              "subjectRealName":true,
              "subjectType": 1
            }
           ]
      files:
        [
        {
          "fileId": $fileId_1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $file_name1,
          "from":2,                 #文件来自 1-模板文件 2-合同文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: $flowTemplateId
      initiatorAccountId: $initiatorAccountId
      approveTemplateId: $approveTemplateId
      signEndTime: *************
      fileEndTime: ""
      signValidityConfig:
        {
          "validityType":3,
         "durationYear":null,
         "durationMonth":null,
         "durationDay":null
        }
      fileValidityConfig:
        {
          "validityType":3,
         "durationYear":null,
         "durationMonth":null,
         "durationDay":null
        }
      participants:
        [
        {
          "participantSubjectType": 1,
          "role": "3",
          "sealType": null,
          "signRequirements": 1,
          "roleSet":1,
          "type": 1,
          "signSealType": 1,
          "fillOrder": 1,
          "signOrder": 1,
          "participantLabel":"签署方1",
          "participantId": $participantId1,
          "instances":[
          {
            "account": $account_1,
            "accountOid": $accountOid_1,
            "accountName": $accountName_1,
            "accountRealName":true,
            "comment":"",
            "subjectId": $subject_1,
            "subjectName": $subjectName_1,
            "subjectRealName":true,
            "subjectType": 1,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[ ]
        },
        {
          "participantSubjectType": 0,
          "role": "3",
          "sealType": "0,1",
          "signRequirements": null,
          "roleSet":1,
          "type": 1,
          "signSealType": 1,
          "fillOrder": 2,
          "signOrder": 1,
          "participantLabel":"签署方2",
          "participantId": $participantId2,
          "instances":[
          {
            "account": $account_2,
            "accountOid": $accountOid_2,
            "accountName": $accountName_2,
            "accountRealName": true,
            "comment":"",
            "subjectId":  $subject_2,
            "subjectName": $subjectName_2,
            "subjectRealName": true,
            "subjectType": 0,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[ ]
        }
        ]
      scene: 2
      taskName: $taskName
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "您的套餐剩余为0份，不足本次发起签署扣费，请订购套餐后重新发起"]
    teardown_hooks:
      - ${hook_sleep_n_secs(10)}
