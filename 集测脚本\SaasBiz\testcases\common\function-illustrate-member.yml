- config:
    name: 查询功能在当前企业是否有使用记录
    variables:
      orgId1: b06239b4e90b43eeb091a088280d19a8
      accountId1: e63c5554153646d6a1eccff4aac746de
      accountId2: 025a5285b9934833b1c6d753485d2af7

- test:
    name: 体验版-管理员-人数超限-引导
    api: api/common/function-illustrate-member.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      json:
        {
          "functionCode": "member_manage",
          "newFunctionIllustrate": true,
          "functionLimitIllustrate": true,
          "functionLimitScene": "DEFAULT"
       }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: ["content.data.needGuide", true]
      - eq: ["content.data.illustrate.docTitle", "企业成员数量超出限制"]

- test:
    name: 体验版-普通员工-人数超限-引导
    api: api/common/function-illustrate-member.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      json:
        {
          "functionCode": "member_manage",
          "newFunctionIllustrate": true,
          "functionLimitIllustrate": true,
          "functionLimitScene": "DEFAULT"
       }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: ["content.data.needGuide", true]
      - eq: ["content.data.illustrate.docTitle", "企业成员数量超出限制"]


