- config:
    name: 获取近n天会员版本到期列表
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
#      orgId1: ${ENV(orgId3)}
      orgGid1: ${ENV(org_gid3)}
      accountId1: ${ENV(accountId1)}


- test:
    name: 获取近n天会员版本到期列表-expireDay非法
    api: api/vipmanage/expireInDays.yml
    variables:
      operatorId: $accountId1
      expireDay: test
      userIdList: ["$orgGid1"]
    validate:
      - eq: ["status_code", 400]


- test:
    name: 获取近n天会员版本到期列表-userIdList为空
    api: api/vipmanage/expireInDays.yml
    variables:
      operatorId: $accountId1
      expireDay: 30
      userIdList: []
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 用户列表不能为空]


- test:
    name: 开通高级版会员-有效期为当天
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr: ${today_getTimeStamp_ms()}
      expireDateEndStr: ${todayEnd_getTimeStamp_ms()}
      gidList: ["$orgGid1"]
      vipLevel: 2
      vipCode: SENIOR


- test:
    name: 获取近n天会员版本到期列表-成功
    api: api/vipmanage/expireInDays.yml
    variables:
      operatorId: $accountId1
      expireDay: 10000
      userIdList: [$orgGid1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data", [] ]
