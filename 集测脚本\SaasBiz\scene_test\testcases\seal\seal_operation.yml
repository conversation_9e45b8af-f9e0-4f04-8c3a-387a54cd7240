- config:
    name: 印章增删改查授权
    variables:
      accountId1: 0bc26b27ede64cc48e7a800f4c5cbac7
      orgId1: e0b17fbb6b3744e886bc971d5b302def
      orgName1: esigntest自动化测试企业22
      orgCode1: 910000005656252146
      accountId2: 3a52720ee8a6488ab09021d61236a2d9
      flowtemplateId1: 2bd9387e0c1b46b1abe8ec951352f8b0
      grantedUserCode1: 9100000056418279X1
      grantedUserName1: esigntest自动化测试企业11
      orgId2: 08486d61823d4c2086122b26fb3e615a

#创建企业模板印章
- test:
    name: 创建企业模板印章-人事章
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        alias: 企业人事章${getTimeStamp_ms()}
        bottomText: ren
        color: RED
        horizontalText: shi
        opacity: 80
        style: NONE
        surroundTextInner: ${getTimeStamp_ms()}
        templateType: PERSONNEL_ROUND_STAR
        widthHeight: "38_38"
        sealOwnerOid: $orgId1
    api: api/seal/add_organizationstemplate.yml
    extract:
        - Cu_sealId: content.data.sealId
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

#新增印章授权
- test:
    name: 印章授权-授权印章审批权限
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        appScheme: null
        autoFall: null
        effectiveTime: ${getTimeStamp_ms()}
        expireTime: ${get_next_year_timestamp()}
        fallType: null
        grantRedirectUrl: ""
        grantType: 1
        grantedAccountIds:
           - $accountId2
        grantedUserCode: null
        grantedUserName: null
        grantedUserRole: null
        h5: null
        notifySetting: false
        notifyUrl: ""
        orgId: $orgId1
        resourceId: $Cu_sealId
        roleKey: "SEAL_USER"
        scopeList:
           - $flowtemplateId1
        token: ""
        sealOwnerOid: $orgId1
    api: api/seal/add-rule-grants.yml
    extract:
        - ruleGrantId1: content.data.ruleGrantIds.0
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#查询企业模板章详情
- test:
    name: 查询企业模板章详情
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        sealId: $Cu_sealId
        sealOwnerOid: $orgId1
    api: api/seal/official-template-detail.yml
    validate:
        - eq: ["content.code",0]
        - eq: ["content.data.status", 1]
        - contained_by: ["content.message", ["成功","缺少参数"]]
- test:
    name: 印章授权-查看已经授权的数量2
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        orgId: $orgId1
        resourceId: $Cu_sealId
        ruleGrantStatus: ALL
        offset: 0
        size: 20
        type: 1
        sealOwnerOid: $orgId1
    api: api/seal/rule-grant-list.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.data.total", 2]

#设置接受审批通知
- test:
    name: 授权列表-设置接受审批通知
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        orgId: $orgId1
        ruleGrantedId: $ruleGrantId1
        shouldNotify: true
        sealOwnerOid: $orgId1
    api: api/seal/setting-rule-grant-notify.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]



#获取印章授权书下载地址
- test:
    name: 获取印章授权书下载地址-印章授权书未签署（企业内授权）
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        orgId: $orgId1
        ruleGrantedId: $ruleGrantId1
        sealOwnerOid: $orgId1
    api: api/seal/download-rule-grant.yml
    validate:
#        - eq: ["content.code",********]
        - contained_by: ["content.code", [0,********]]
        - contained_by: ["content.message", ["成功","印章授权书未签署"]]

#删除企业内授权
- test:
    name: 删除授权-企业内授权
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        orgId: $orgId1
        ruleGrantedId: $ruleGrantId1
        sealOwnerOid: $orgId1
    api: api/seal/delete-rule-grant.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#新增印章授权
- test:
    name: 印章授权-跨企业授权
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        appScheme: null
        autoFall: true
        effectiveTime: ${getTimeStamp_ms()}
        expireTime: ${get_next_year_timestamp()}
        fallType: null
        grantRedirectUrl: ""
        grantType: 2
        grantedAccountIds: null
        grantedUserCode: $grantedUserCode1
        grantedUserName: $grantedUserName1
        grantedUserRole: null
        h5: null
        notifySetting: true
        notifyUrl: ""
        orgId: $orgId1
        resourceId: $Cu_sealId
        roleKey: "SEAL_USER"
        scopeList:
           - "ALL"
        token: ""
        sealOwnerOid: $orgId1
    api: api/seal/add-rule-grants.yml
    extract:
        - ruleGrantId2: content.data.ruleGrantIds.0
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#授权企业/成员列表
- test:
    name: 授权企业列表-跨企业授权列表
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        orgId: $orgId1
        resourceId: $Cu_sealId
        ruleGrantStatus: ALL
        offset: 0
        size: 20
        type: 2
        sealOwnerOid: $orgId1
    api: api/seal/rule-grant-list.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
        - eq: ["content.data.grantlist.0.fallType", 1]

#获取印章授权书下载地址
- test:
    name: 获取印章授权书下载地址-印章授权书未签署（跨企业授权）
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        orgId: $orgId2
        ruleGrantedId: $ruleGrantId2
        sealOwnerOid: $orgId1
    api: api/seal/download-rule-grant.yml
    validate:
        - contained_by: ["content.code", [0,********]]
        - contained_by: ["content.message", ["成功","印章授权书未签署"]]

#删除企业外授权
- test:
    name: 删除授权-跨企业授权
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        orgId: $orgId2
        ruleGrantedId: $ruleGrantId2
        sealOwnerOid: $orgId1
    api: api/seal/delete-rule-grant.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 删除人事印章
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        sealId: $Cu_sealId
        sealOwnerOid: $orgId1
    api: api/seal/delete-seal.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","成功"]

#新增法人模板章
- test:
    name: 新增法人模板章
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        alias: 法人模板章${getTimeStamp()}
        color: PURPLE
        opacity: 95
        stampRule: "1"
        style: NONE
        templateType: RECTANGLE_BORDER
        widthHeight: "20_10"
    extract:
        - Fa_sealId: content.data.sealId
    api: api/seal/create-legal-template.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#预览法人章
- test:
    name: 预览法人章
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        color: BLACK
        opacity: 99
        stampRule: "0"
        style: NONE
        templateType: SQUARE_RIGHT_BORDER
        widthHeight: "18_18"
    api: api/seal/preview-legal-template.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#查询法人模板章详情
- test:
    name: 查询法人模板章详情
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        sealId: $Fa_sealId
    api: api/seal/legal-template-detail.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.status", 1]
        - contains: ["content.data.sealTemplate.text", "测试吴四印"]

- test:
    name: 删除法人模板章
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        sealId: $Fa_sealId
        sealOwnerOid: $orgId1
    api: api/seal/delete-seal.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","成功"]

#创建企业法人图片印章
- test:
    name: 创建企业法人图片印章
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        alias: 本地上传${getTimeStamp()}
        data: ${ENV(data)}
        downloadFlag: true
        height: 156
        notifyUrl: null
        operateType: submit
        sealType: 3
        transparentFlag: true
        type: FILEKEY
        uploadFileMd5: ""
        width: 156
    extract:
        - Bf_sealId: content.data.sealId
    api: api/seal/create-legal-image.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

- test:
    name: 查询法人图片章详情
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        sealIds: $Bf_sealId
        orgId: $orgId1
        downloadFlag: true
        operator:
    api: api/seal/get_bySealId.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.seals.0.status", 2]

- test:
    name: 删除法人图片章
    variables:
        tenantId: $orgId1
        operatorId: $accountId1
        sealId: $Bf_sealId
        sealOwnerOid: $orgId1
    api: api/seal/delete-seal.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","成功"]