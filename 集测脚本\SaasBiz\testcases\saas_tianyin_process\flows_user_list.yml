- config:
    name: 获取用户参与的天印流程列表
    variables:
      pageSize: 10
      pageNum: 1

- test:
    name: 获取用户参与的天印流程列表-Y
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: ""
      keyword: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表-流程状态签署中
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: 1
      keyword: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表-流程状态完成
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: 2
      keyword: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表-流程状态撤回
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: 3
      keyword: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表-流程状态拒签
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: 4
      keyword: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表-流程状态过期
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: 5
      keyword: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表-流程状态作废
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: 6
      keyword: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表-keyword匹配成功
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: 1
      keyword: "个人"
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表-keyword匹配失败
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: ""
      keyword: "霸天虎"
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表-N
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: ""
      keyword: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户参与的天印流程列表-企业空间
    api: api/saas_tianyin_process/flows_user_list.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      status: ""
      keyword: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"