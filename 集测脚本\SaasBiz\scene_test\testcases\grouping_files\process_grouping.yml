- config:
    name: 企业合同流程归档、更换分类、移出分类
    variables:
      orgId1: 5546e67033104443b5c7f509ccb060f1
      accountId1: 7c1d00fb6f3e473d9a55697b9b9abe8b    #企业1的普通成员
      account1: ***********
      accountName1: 测试四十二
      accountId2: 8a17e0b48e5948738a990d09b190d45b    #企业1的管理员
      roleId1: 6100557fb3294c459d062b794d8018c1       #企业1的企业合同的归档角色id
      roleId2: 3a585c7660aa4579ab40b1993ede1ced       #企业1的企业合同的更换分类角色id
      roleId3: f56a6f03c7534537a04d6f71b7cbcd6b       #企业1的企业合同的移出分类角色id
      processId1: 187c3f07f3fb4529892f0ce24a603854
      processId2: a53107a6c2844622a76c3878a97fd13f
      menuId1: 6c0452644dd64b699c7bb67fc4a02937       #企业1的分类1
      menuId2: ac17f310fae84a02989fbcefcae8dd70       #企业1的分类2
      roleId6: c2d56230469a47d88b30ba0058eb3492       #企业1的普通成员角色id
      roleId7: 186d7eab1ee34c2e81d4bddb93c6ffa2       #企业1的设置合同保密角色id

- test:
    name: 流程归档-企业管理员默认有权限
    api: api/grouping_files/grouping.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuIdList: [$menuId1]
      processIdList: [$processId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同列表-已归档分类1列表能查到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: $menuId1
      matching: '[{"key":"title","value":["集测归档用的流程2"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]

- test:
    name: 企业合同列表-已归档根目录能查到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: MENU_ALL
      matching: '[{"key":"title","value":["集测归档用的流程2"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.groupingProcessList.0.menuIdList.0", $menuId1]

- test:
    name: 企业合同列表-待归档列表查不到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: null
      matching: '[{"key":"title","value":["集测归档用的流程2"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]

- test:
    name: 流程更换分类-企业管理员默认有权限
    api: api/grouping_files/moveMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuIdList: []
      processIds: [$processId2]
      sourceMenuId: $menuId1
      targetMenuIdList: [$menuId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同列表-已归档分类2列表能查到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: $menuId2
      matching: '[{"key":"title","value":["集测归档用的流程2"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]

- test:
    name: 企业合同列表-已归档分类1列表查不到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: $menuId1
      matching: '[{"key":"title","value":["集测归档用的流程2"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]

- test:
    name: 企业合同列表-已归档根目录能查到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: MENU_ALL
      matching: '[{"key":"title","value":["集测归档用的流程2"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.groupingProcessList.0.menuIdList.0", $menuId2]

- test:
    name: 流程移出分类-企业管理员默认有权限
    api: api/grouping_files/removeMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuIdList: []
      processIds: [$processId2]
      targetMenuId: $menuId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同列表-已归档分类2列表查不到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: $menuId2
      matching: '[{"key":"title","value":["集测归档用的流程2"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]

- test:
    name: 企业合同列表-已归档分根目录查不到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: MENU_ALL
      matching: '[{"key":"title","value":["集测归档用的流程2"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]

- test:
    name: 企业合同列表-待归档列表能查到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: null
      matching: '[{"key":"title","value":["集测归档用的流程2"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]

- test:
    name: 流程归档-企业普通成员无权限
    api: api/grouping_files/grouping.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: [$menuId1]
      processIdList: [$processId1,$processId2]
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 流程一键归档-企业普通成员无权限
    api: api/grouping_files/grouping_all.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      matching: '[{"key":"title","value":["集测归档用的流程"],"sort":"","isPublic":false}]'
      toMenuId: $menuId1
      withApproving: true
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同归档的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1,$roleId6,$roleId7]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程一键归档-企业普通成员有权限
    api: api/grouping_files/grouping_all.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      matching: '[{"key":"title","value":["集测归档用的流程"],"sort":"","isPublic":false}]'
      toMenuId: $menuId1
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同列表-待归档列表查不到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: null
      matching: '[{"key":"title","value":["集测归档用的流程"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]

- test:
    name: 企业合同列表-已归档分类1列表能查到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: $menuId1
      matching: '[{"key":"title","value":["集测归档用的流程"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 2]

- test:
    name: 企业合同列表-已归档根目录能查到流程
    api: api/contract_manage/process_query/v2_grouping_lists_list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      menuId: MENU_ALL
      matching: '[{"key":"title","value":["集测归档用的流程"],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 2]

- test:
    name: 流程归档-企业普通成员无分类查看权限
    api: api/grouping_files/grouping.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: [$menuId1]
      processIdList: [$processId1,$processId2]
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 获取可操作的角色列表
    api: api/grouping_permission/roleByAuthorizer.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
      authorizer: $accountId1
      authorizeType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - roleList1: content.data

- test:
    name: 添加目录用户及授权-给accountId1分配menuId1的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId4: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId4",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程归档-企业普通成员有权限
    api: api/grouping_files/grouping.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: [$menuId1]
      processIdList: [$processId1,$processId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同归档的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId6,$roleId7]
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 添加目录用户及授权-取消accountId1的menuId1的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId4: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId4",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程更换分类-企业普通成员无权限
    api: api/grouping_files/moveMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: []
      processIds: [$processId1,$processId2]
      sourceMenuId: $menuId1
      targetMenuIdList: [$menuId2]
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同更换分类的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId2,$roleId6,$roleId7]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程更换分类-企业普通成员无分类查看权限
    api: api/grouping_files/moveMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: []
      processIds: [$processId1,$processId2]
      sourceMenuId: $menuId1
      targetMenuIdList: [$menuId2]
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 添加目录用户及授权-给accountId1分配menuId2的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId2
      tenantId: $orgId1
      accountId: $accountId2
      roleId4: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId4",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程更换分类-企业普通成员有权限
    api: api/grouping_files/moveMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: []
      processIds: [$processId1,$processId2]
      sourceMenuId: $menuId1
      targetMenuIdList: [$menuId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同更换分类的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId6,$roleId7]
      memberName: ""
      revokeRoleIds: [$roleId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 添加目录用户及授权-取消accountId1的menuId2的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId2
      tenantId: $orgId1
      accountId: $accountId2
      roleId4: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId4",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程移出分类-企业普通成员无权限
    api: api/grouping_files/removeMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: []
      processIds: [$processId1,$processId2]
      targetMenuId: $menuId2
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同移出分类的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId3,$roleId6,$roleId7]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程移出分类-企业普通成员无查看权限
    api: api/grouping_files/removeMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: []
      processIds: [$processId1,$processId2]
      targetMenuId: $menuId2
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 添加目录用户及授权-给accountId1分配menuId2的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId2
      tenantId: $orgId1
      accountId: $accountId2
      roleId4: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId4",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程移出分类-企业普通成员有权限
    api: api/grouping_files/removeMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: []
      processIds: [$processId1,$processId2]
      targetMenuId: $menuId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同移出分类的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId6,$roleId7]
      memberName: ""
      revokeRoleIds: [$roleId3]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 添加目录用户及授权-取消accountId1的menuId2的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId2
      tenantId: $orgId1
      accountId: $accountId2
      roleId4: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId4",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程一键归档-企业管理员默认有权限
    api: api/grouping_files/grouping_all.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      matching: '[{"key":"title","value":["集测归档用的流程"],"sort":"","isPublic":false}]'
      toMenuId: $menuId1
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]

- test:
    name: 添加目录用户及授权-给accountId设置menuId2的分类管理员的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId2
      tenantId: $orgId1
      accountId: $accountId2
      roleId5: ${getValue($roleList1, roleKey, MENU_ADMIN, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId5",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程更换分类-企业普通成员有权限
    api: api/grouping_files/moveMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: []
      processIds: [$processId2]
      sourceMenuId: $menuId1
      targetMenuIdList: [$menuId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 流程移出分类-企业普通成员有权限
    api: api/grouping_files/removeMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuIdList: []
      processIds: [$processId2]
      targetMenuId: $menuId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 添加目录用户及授权-取消accountId的menuId2的分类管理员的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId2
      tenantId: $orgId1
      accountId: $accountId2
      roleId5: ${getValue($roleList1, roleKey, MENU_ADMIN, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId5",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}
