- config:
    name: 创建任务组及子任务相关操作
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
        name1: 创建测试任务组-${getTimeStamp_ms()}
        bizId1: ${getTimeStamp_ms()}
        type: 7
        accountId1: ${ENV(accountId1)}
        db_name: saas_base_manage
        total1: 2


- test:
    name: 创建任务组
    api: api/taskmanage/rpc/addTaskGroup.yml
    variables:
        groupId: $bizId1
        groupName: $name1
        taskTotal: $total1
        accountOid: $accountId1
    validate:
        - eq: ["status_code", 200]
    extract:
        - uuid1: content.uuid


- test:
      name: 创建子任务1-成功
      api: api/taskmanage/rpc/addSubTask.yml
      variables:
          parentId: $uuid1
          bizId: 1-$bizId1
          name: 测试子任务1
          accountOid: $accountId1
          status: 2
      validate:
          - eq: ["status_code", 200]


- test:
      name: 创建子任务2-失败
      api: api/taskmanage/rpc/addSubTask.yml
      variables:
          parentId: $uuid1
          bizId: 2-$bizId1
          name: 测试子任务2
          accountOid: $accountId1
          status: 4
      validate:
          - eq: ["status_code", 200]


- test:
      name: 创建子任务3-超过任务组总数
      api: api/taskmanage/rpc/addSubTask.yml
      variables:
          parentId: $uuid1
          bizId: 3-$bizId1
          name: 测试子任务3
          accountOid: $accountId1
          status: 1
          sql1: "SELECT count(1) FROM saas_base_manage.saas_task_info where type=$type and parent_id='$uuid1';"
      validate:
          - eq: ["status_code", 200]
          - eq: [$total1, "${select_sql($sql1, $db_name)}"]


- test:
      name: 校验用户是否有进行中的任务
      api: api/taskmanage/rpc/existExecutingParentTaskByAccountIdAndType.yml
      variables:
          accountId: $accountId1
          sql2: "select case when COUNT(*) > 0 then TRUE else FALSE end from saas_base_manage.saas_task_info where creator_oid='$accountId1' and type=$type and status in (0,1) and sub_task=0;"
      validate:
          - eq: ["status_code", 200]
          - eq: ["content", "${select_sql($sql2, $db_name)}"]

- test:
      name: 校验用户是否有进行中的任务
      api: api/taskmanage/rpc/queryUnderwayTask.yml
      variables:
          accountId: $accountId1
          taskType: 12
      validate:
          - eq: ["status_code", 200]
#          - eq: ["content", "${select_sql($sql2, $db_name)}"]
