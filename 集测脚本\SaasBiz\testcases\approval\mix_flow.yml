- config:
    name: 查看审批流


- test:
    name: 查看审批流--成功
    api: api/approval/mix_flow.yml
    variables:
      operatorId: 291e8283e53f4402baf85956d3b7f7b3
      tenantId: 60b8595f05444c7c981de67eb7540856
      approvalId: "20565f2a133047fe97123defcdfe4c88"
      approvalType: 2
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]


- test:
    name: 查看审批流--审批流程不存在
    api: api/approval/mix_flow.yml
    variables:
      operatorId: 291e8283e53f4402baf85956d3b7f7b3
      tenantId: 60b8595f05444c7c981de67eb7540856
      approvalId: "111"
      approvalType: 1
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]