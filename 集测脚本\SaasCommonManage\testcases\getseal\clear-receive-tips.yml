- config:
    name: 首页清除用户领章提示
#    base_url: ${ENV(saas_common_manage_url)}



- test:
    name: 传oid和appId
    api: api/getseal/clear-receive-tips.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 只传oid
    api: api/getseal/clear-receive-tips.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: ""

    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message", appId不能为空]

- test:
    name: appId不正确
    api: api/getseal/clear-receive-tips.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 12

    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

- test:
    name: appId空
    api: api/getseal/clear-receive-tips.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: ""

    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message", appId不能为空]

   # teardown_hooks:
    #  - ${hook_sleep_n_secs(2)}


