import os
import datetime
import time
import hashlib
import base64
import random
import importlib
import time
from time import sleep
import platform
import pymysql as mdb
from httprunner import utils
import pymysql
import string
import requests
import json
import ssl
from time import strptime, mktime
from urllib.parse import urlparse, parse_qs


def ENV(key_name):
    """
    获取.env文件里的变量值
    :param key_name:
    :return:
    """
    return os.environ.get(key_name, '')


app_id = os.environ['app_id']
app_secret = os.environ['app_secret']
hosturl = os.environ['hosturl']
tenantId = os.environ['mx_orgId1']
operatorId = os.environ['mx_accountId1']
open_url = os.environ['open_url']
account_host = open_url + '/account-webserver/login/commit/user'
service_group = ENV('service_group')
# token_way = os.environ['X-Tsign-token-way']
# tenant_id = os.environ['x-tsign-Open-Tenant-Id']
# isv_app_id = os.environ['X-Tsign-Isv-App-Id']
# client_id = os.environ['X-Tsign-Client-Id']
# corpId = os.environ['X-Tsign-Ding-Corp-Id']
# user_id = os.environ['X-Tsign-Ding-User-Id']
username = os.environ['username']
password = os.environ['password']
token = ''

sep = os.path.sep  # 路径分隔符
cur_dir = os.path.abspath('.') + sep


def get_day_time(n):
    the_date = datetime.datetime.now()  # 当前时间;
    modify_date = (the_date + datetime.timedelta(days=n)).strftime('%Y-%m-%d %H:%M:%S')
    modify_time = strptime(modify_date, "%Y-%m-%d %H:%M:%S")  # 将时间转化为数组形式
    print(modify_date)
    modify_stamp = int(round(mktime(modify_time)) * 1000)  # 将时间转化为时间戳形式
    print(modify_stamp)
    return modify_stamp

def get_next_year_timestamp():
    # 获取当前时间
    current_time = time.time()

    # 加上一年
    next_year = current_time + 31608696

    # 将时间转换为毫秒级别时间戳
    timestamp = int(next_year * 1000)

    return timestamp

def gen_headers(**kwargs):
    header = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": app_id,
        "X-Tsign-Open-Auth-Mode": "Secret",
        "X-Tsign-Open-App-Secret": app_secret,
        "X-Tsign-Open-Tenant-Id": tenantId,
        "X-Tsign-Open-Operator-Id": operatorId,
        "X-Tsign-Service-Group": service_group
    }
    headers = {**header, **kwargs}
    return headers


def gen_headers2(**kwargs):
    header = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": app_id,
        "X-Tsign-Open-Auth-Mode": "simple",
        "X-Tsign-Service-Group": service_group
    }
    headers = {**header, **kwargs}
    return headers


def gen_headers3(app_id, **kwargs):
    header = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": app_id,
        "X-Tsign-Open-Auth-Mode": "simple",
        "X-Tsign-Service-Group": service_group,
        "X-Tsign-Client-Id": "WEB"
    }
    headers = {**header, **kwargs}
    return headers

def gen_headers4(tenantId,operatorId,**kwargs):
    header = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": app_id,
        "X-Tsign-Open-Auth-Mode": "Secret",
        "X-Tsign-Open-App-Secret": app_secret,
        "X-Tsign-Open-Tenant-Id": tenantId,
        "X-Tsign-Open-Operator-Id": operatorId
    }
    headers = {**header, **kwargs}
    return headers


service_group = os.environ['service_group']


# # 钉签登录所用的header(主要是多了一个获取token的步骤)
# def gen_headers_dingtalk(**kwargs):
#     headers = {"Content-Type": "application/json", "x-tsign-open-app-id": app_id,
#                "X-Tsign-Ding-User-Id": user_id, "X-Tsign-token-way": token_way, "x-tsign-Open-Tenant-Id": tenant_id,
#                "X-Tsign-Isv-App-Id": isv_app_id,  "X-Tsign-Ding-Corp-Id": corpId,
#                "X-Tsign-Client-Id": client_id, "X-Tsign-token": init_token()}
#
#     for key, value in kwargs.items():
#         headers[key] = value
#     # print(headers)
#     return headers


# 使用md5进行加密
def md5_password(password):
    password = str(password).encode('utf-8')
    md5 = hashlib.md5()
    md5.update(password)
    password = md5.hexdigest()
    return password


# 获取token（登录E签宝微应用时，需要用到）
def get_token(host, username, password):
    url = ''.join(host)
    headers = {"content-type": "application/json", "X-Tsign-Service-Group": "DEFAULT",
               "X-Tsign-Open-App-Id": app_id, "X-Tsign-Open-Auth-Mode": "Secret",
               "X-Tsign-Open-App-Secret": app_secret, "Host": hosturl,
               # "X-Tsign-Open-App-Secret": app_secret, "Host": "tapi.esign.cn",
               "X-Tsign-token-way": "header"}
    data = {"loginParams": {"expireTime": 2592000, "endpoint": "DING_TALK_INNER"},
            "credentials": md5_password(password), "principal": username}
    # 处理SSL证书认证的校验
    requests.packages.urllib3.disable_warnings()
    try:
        _create_unverified_https_context = ssl._create_unverified_context
    except AttributeError:
        pass
    else:
        ssl._create_default_https_context = _create_unverified_https_context

    response = requests.post(url, headers=headers, json=data)
    response = response.headers
    # print(response['X-Tsign-token'])
    return response['X-Tsign-token']


def init_token():
    global token
    token = get_token(account_host, username, password)
    # print(token)
    return token


# new_token = init_token()


# 默认数据路径都放在data下
def open_file(local_path):
    fd = open(cur_dir + local_path, 'rb')
    return fd


def hook_sleep_n_secs(n_secs):
    time.sleep(n_secs)


def now_time(n):
    # return int(time.time())
    timeArray = (datetime.datetime.now() + datetime.timedelta(days=n)).strftime("%Y/%m/%d %H:%M:%S")
    timeArray1 = time.strptime(timeArray, "%Y/%m/%d %H:%M:%S")
    return int(time.mktime(timeArray1) * 1000)


def now_timeh(n):
    # return int(time.time())
    timeArray = (datetime.datetime.now() + datetime.timedelta(hours=n)).strftime("%Y/%m/%d %H:%M:%S")
    timeArray1 = time.strptime(timeArray, "%Y/%m/%d %H:%M:%S")
    return int(time.mktime(timeArray1) * 1000)


# 获取文件的大小
def get_file_size(local_path):
    size = os.path.getsize(cur_dir + local_path)
    return size


# 获取当前时间的时间戳
def getTimeStamp():
    t = time.time()
    return int(t)


def getTimeStamp_ms():
    t = time.time()
    return int(t * 1000)


def hook_sleep_n_secs(n_secs):
    time.sleep(n_secs)


def hook_sleep_n_secss(response, n_secs):
    if response.status_code == 200:  # 接口请求code等于200 则等待n_secs 秒
        time.sleep(n_secs)
    else:  # 接口请求code不等于200 则等待0.5 秒
        time.sleep(0.5)


# 获取一个随机长度的字符串
def generate_random_str(randomlength):
    random_str = ''
    base_str = 'abcdefghigklmnopqrstuvwxyzabcdefghigklmnopqrstuvwxyz0123456789'
    length = len(base_str) - 1
    for i in range(randomlength):
        random_str += base_str[random.randint(0, length)]
    return random_str


def teardown_hook_sleep_N_secs(num):
    x = time.sleep(num)
    return x


def groupingNum():
    y = 1609430400000 + random.randint(0, 9) * 86400000
    return y


# 连接数据库
def connect_db(name):
    if (name == 'doc-cooperation'):
        connect = pymysql.Connect(
            host=os.environ['db_host'],
            port=3306,
            user=os.environ['db_user'],
            password=os.environ['db_pwd'],
            database='doc_cooperation',
            charset='utf8'
        )
    elif (name == 'contract_manager'):
        connect = pymysql.Connect(
            host=os.environ['db_host'],
            port=3306,
            user=os.environ['db_user'],
            password=os.environ['db_pwd'],
            database='contract_manager',
            charset='utf8'
        )
    elif (name == 'account-flow'):
        connect = pymysql.Connect(
            host=os.environ['db_host2'],
            port=3306,
            user=os.environ['db_user2'],
            password=os.environ['db_pwd2'],
            database='account_flow',
            charset='utf8'
        )
    elif (name == 'flowmanager'):
        connect = pymysql.Connect(
            host=os.environ['db_host3'],
            port=3306,
            user=os.environ['db_user3'],
            password=os.environ['db_pwd3'],
            database='flowmanager',
            charset='utf8'
        )
    else:
        connect = pymysql.Connect(
            host=os.environ['db_host'],
            port=3306,
            user=os.environ['db_user'],
            password=os.environ['db_pwd'],
            database='contract_manager',
            charset='utf8'
        )
    return connect


# 查询数据库,返回一个string字符串
def select_sql(sql, db_name):
    db = connect_db(db_name)
    cur = db.cursor()
    try:
        cur.execute(sql)
        result = cur.fetchall()
        print("查询的结果：{}".format(result[0][0]))
        return result[0][0]
    except Exception as e:
        print(e)
    finally:
        cur.close()
    return


def delete_sql(host, user, pswd, db, sql):
    print(sql)
    status = 0
    conn = mdb.connect(host, user, pswd, db, charset='utf8')
    cur = conn.cursor()
    # 执行SQL语句
    cur.execute(sql)
    conn.commit()
    conn.cursor()
    conn.close()
    return status


# 数据库执行delete、update、insert操作
def execute_sql(db, sql):
    cur = db.cursor()
    try:
        cur.execute(sql)
        db.commit()
    except Exception as e:
        db.rollback()
        print(e)
    finally:
        db.close()


# hook__清理、准备测试数据
def hook_db_data(sql, db_name):
    db = connect_db(db_name)
    print("正在执行SQL：{0}".format(sql))
    execute_sql(db, sql)


# 版本号自增
def versionIncrement(v):
    str1 = v.replace('.', '')
    print(str1)
    num = int(str1) + 1
    str2 = str(num)
    b = []
    for i in range(len(str2)):
        b.append(str2[i:i + 1])
    print('.'.join(b))
    return '.'.join(b)


def code():
    # 获取26个大小写字母
    letters = string.ascii_letters
    # 获取26个小写字母
    Lowercase_letters = string.ascii_lowercase
    # 获取26个大写字母
    Capital = string.ascii_uppercase
    # 获取阿拉伯数字
    digits = string.digits
    # s是小写字母和数字的集合
    s = Lowercase_letters + digits
    # 生成28位小写和数字的集合，并将列表转字符串
    code = ''.join(random.sample(s, 5))
    print('随机code:%s' % code)
    return code


# 通用查询
def select_1(host, user, pswd, db, selectsql):
    print(db)
    print(selectsql)
    status = 0
    conn = mdb.connect(host, user, pswd, db, charset='utf8')
    cur = conn.cursor()
    # SQL 查询语句
    selectSQL = selectsql
    print(selectSQL)
    cur.execute(selectSQL)
    result = cur.fetchall()
    print(result)
    no11 = result[0][0]
    conn.commit()
    conn.cursor()
    conn.close()
    return no11


# 解约前查询及处理数据
def relate_query(selectsql1, selectsql2, updatesql):
    flag = True
    exist = select_sql(selectsql1, "contract_manager")
    if exist != None:
        status = select_sql(selectsql2, "contract_manager")
        if status == 4:
            hook_db_data(updatesql, "contract_manager")
        else:
            flag = False
    return flag


def update_sql(host, user, pswd, db, sql):
    print(sql)
    status = 0
    conn = mdb.connect(host, user, pswd, db, charset='utf8')
    cur = conn.cursor()
    # 执行SQL语句
    updateSql = sql
    conn.commit()
    conn.cursor()
    conn.close()


# 获取dict中对应key的value值
def getValue(dict, key1, value1, key2):
    value2 = ""
    for i in range(len(dict)):
        if dict[i][key1] == value1:
            value2 = dict[i][key2]
    return value2


# 获取当前日期的毫秒级时间戳(开始时间)
def today_getTimeStamp_ms():
    t = datetime.datetime.now()
    # 当前日期
    t1 = t.strftime('%Y-%m-%d 00:00:00')
    # 转为秒级时间戳
    start_time = time.mktime(time.strptime(t1, '%Y-%m-%d %H:%M:%S'))
    return int(start_time * 1000)


# 获取当前日期的毫秒级时间戳(结束时间)
def todayEnd_getTimeStamp_ms():
    t = datetime.datetime.now()
    # 当前日期
    t2 = t.strftime('%Y-%m-%d 23:59:59')
    # 转为秒级时间戳
    end_time = time.mktime(time.strptime(t2, '%Y-%m-%d %H:%M:%S'))
    return int(end_time * 1000)


# 获取经过base64转换的md5值,
# 参考https://yq.aliyun.com/articles/27523?spm=5176.11065265.1996646101.searchclickresult.3b6d4025K8qbp3
def get_file_base64_md5(local_path):
    fd = open(cur_dir + local_path, 'rb')
    m = hashlib.md5()
    while True:
        d = fd.read(8096)
        if not d:
            break
        m.update(d)
    byte = base64.b64encode(m.digest())
    return bytes.decode(byte)


# 两个数字相减
def minus(a, b):
    res = a - b
    return res


# 两个数字相减
def sum(a, b):
    res = a + b
    return res


def now_time_before(n):
    timeArray = (datetime.datetime.now() - datetime.timedelta(days=n)).strftime("%Y/%m/%d %H:%M:%S")
    timeArray1 = time.strptime(timeArray, "%Y/%m/%d %H:%M:%S")
    return int(time.mktime(timeArray1) * 1000)


def now_time_after(n):
    timeArray = (datetime.datetime.now() + datetime.timedelta(days=n)).strftime("%Y/%m/%d %H:%M:%S")
    timeArray1 = time.strptime(timeArray, "%Y/%m/%d %H:%M:%S")
    return int(time.mktime(timeArray1) * 1000)


def compareNum(a, b):
    return a > b


# 循环查询列表多次
def waitAndSearch(url1, params1, headers1, times):
    for i in range(times):
        data = process_list(url1, params1, headers1)
        if (data > 0):
            break
        time.sleep(2)
    return data


def process_list(url1, params1, headers1):
    baseUrl = os.environ['open_url']
    params = params1
    headers = headers1
    r = requests.get(url=baseUrl + url1, params=params, headers=headers)
    # print(r.status_code)
    total = r.json()['data']['total']
    print(total)
    return total


# 获取当前日期开始时间
def today_getTime_ms():
    t = datetime.datetime.now()
    # 当前日期
    t1 = t.strftime('%Y-%m-%d 00:00:00')
    return t1


# 获取当前日期结束时间
def todayEnd_getTime_ms():
    t = datetime.datetime.now()
    # 当前日期
    t1 = t.strftime('%Y-%m-%d 23:59:59')
    return t1


def toStr(data):
    return str(data)


# 获取今天的日期
def today_date():
    t = datetime.datetime.now()
    # 当前日期
    t1 = t.strftime('%Y-%m-%d')
    return t1


# 定制化方法，用来从/approval/flow/list接口返回中取特定的值，作为后续接口的入参
def find_dict_by_key(input_list, key_string, key_name):
    # 遍历列表，找到包含目标键值的字典
    target_dict = next((d for d in input_list if d.get('processId') == key_string), None)
    if target_dict is not None:
        if key_name == 'approvalId':
            return target_dict.get('approvalId')
        elif key_name == "taskId":
            return target_dict.get('currentOperatorTask').get('taskId')
    else:
        return None


# 从url中获取path中的值
def get_path_from_url(url, key):
    parsed_url = urlparse(url)
    query_dict = parse_qs(parsed_url.query)
    key_name = query_dict.get(key)
    if key_name:
        return key_name[0]
    else:
        return None


# 把json转化为str
def json_to_string(json_obj):
    return json.dumps(json_obj)


# 从json中提取出想要的字段(最终返回的是extract_id的值)
def extract_json_value(json_data, object_name, extract_id, index):
    data = json.loads(json_data)
    # 获取包含符合要求的数据的结构体列表
    structs_list = data.get(object_name, [])
    # 从每个结构体中提取 ID 值到列表中
    values = [struct.get(extract_id) for struct in structs_list]
    # 根据索引，返回list中具体的值
    return values[index]


#  获取list 长度
def get_list_len(list):
    size = len(list)
    return size


# 获取批量任务进度是否有进行中或失败的任务
def get_addTaskStatus(data):
    print(data)
    if data == None:
        return True
    else:
        return False


# 两个数字相减
def minus(a, b):
    res = a - b
    return res

#查询功能灰度是否支持
def queryFunctionGary(orgId, functionKey):
    baseUrl = open_url
    url= "/v1/grayscale/user/"+orgId+"/checkFunctionGray"
    params = {
        "functionKeys": functionKey
    }
    headers = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": app_id,
        "X-Tsign-Open-Auth-Mode": "simple"
    }
    r = requests.get(url=baseUrl + url, params=params, headers=headers)
    flag = r.json()['data']['functionMap'][functionKey]
    print(flag)
    return flag

# 循环获取批量任务进度
def waitTillDone2(url1, params1, headers1, times):
    for i in range(times):
        baseUrl = 'http://in-test-openapi.tsign.cn'
        params = params1
        headers = headers1
        r = requests.get(url=baseUrl + url1, params=params, headers=headers)
        # print(r.status_code)
        data = r.json()['data']
        # print(data)
        if (data == None):
            break
        time.sleep(2)

def str_append(str1,str2):
    list = []
    list.append(str1)
    list.append(str2)
    data = ",".join(list)
    return str(data)

def get_next_taskId(task):
    task += 1
    return task