- config:
    name: 查询指定数据范围配置
#    base_url: ${ENV(contract_manager_url)}

- test:
    name: 查询指定数据范围配置-
    api: api/flowTemplates/get-template-auth-config.yml
    variables:
        flowTemplateName: ""
        authId: "e911556aee074ce69d60a29a0e402fbb"
        tenantId: "9eef7945e968420294dbdd30a89a12a5"
        operatorId: "a690089d48a14707a1cb78c453a0d991"
        pageSize: 1
        pageNum: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]