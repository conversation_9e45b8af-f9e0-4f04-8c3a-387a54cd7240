- config:
    name: 创建仅签静态epaas模板，使用模板发起流程
    variables:
      fileMd5: m49PzxWxAG8sMwNkZrlJDw==
      fileType1: application/pdf
      fileName1: "test.pdf"
      fileSize1: 99580
      filePath1: "data/test.pdf"
      templateName1: 仅签静态epaas模板-${getTimeStamp_ms()}
      orgId1: 2963014d2c9a4d0ca78ee05cde06f835
      orgName1: esigntest测试企业epaas模板专用1
      accountId1: ${ENV(mx_accountId)}
      account1: <EMAIL>
      accountName1: 明绣
      db_name1: doc-cooperation
      db_name2: epaas_doc_template
      taskName1: 使用仅签静态epaas模板发起-${getTimeStamp_ms()}


- test:
    name: 文件直传创建文件
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      contentMd5: $fileMd5
      contentType: $fileType1
      convert2Pdf: false
      fileName: $fileName1
      fileSize: $fileSize1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId1: content.data.fileId
      - uploadUrl1: content.data.uploadUrl

- test:
    name: 上传文件到oss
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl1
      contentType: $fileType1
      contentMd5: $fileMd5
      filePath: $filePath1
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]
#    teardown_hooks:
#      - ${hook_sleep_n_secs(1)}

- test:
    name: 批量查询文件状态
    api: api/footstone-doc/files-status.yml
    variables:
      tenantId: $orgId1
      fileIds1: [$fileId1]
      json:
        {
          "fileIds": $fileIds1,
          "isCheck": true
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.fileStatusList.0.status", 2]
    setup_hooks:
      - ${waitFileStatus($fileIds1, $orgId1, 30)}

- test:
    name: 创建epaas文件模板
    api: api/footstone-doc/create-doc-template.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fileId: $fileId1
      docTemplateName: $fileName1
      docTemplateType: 0
      epaasTag: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - docTemplateId1: content.data.docTemplateId

- test:
    name: 创建仅签静态epaas模板
    api: api/contract-manager2/save_flowTemplate.yml
    variables:
      tenantId: $orgId1
      businessType: 0
      ccs: []
      files:
        [
        {
          "fileId": $docTemplateId1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $fileName1,
          "from":4,                 #文件来自 1-模板文件 2-合同文件 3-动态文件 4-epaas文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: ""
      initiatorAccountId: $accountId1
      participants:
        [
        {
          "participantSubjectType":0,
          "role":"3",
          "sealType":"0,1",
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":null,
          "instances":null,
          "willTypes":[]
        }
        ]
      previewType: 0
      scene: 3
      taskName: $templateName1
      epaasTag: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.processId", null]
    extract:
      - flowTemplateId1: content.data.flowTemplateId

- test:
    name: 获取epaas文档编辑地址-获取tplToken
    api: api/epaas-doc-template/getDocumentsEditUrl.yml
    variables:
      json: {
        "serviceId": "doc-cooperation",
        "businessContext": {
          "clientId": "WEB",
          "flowTemplateId": $flowTemplateId1,
          "limitLoginUserOid": $accountId1,
          "batchDropSeal": "true",
          "bizScene": "FLOW"
        },
        pageParams: {
          "template.global.showWatermark": true
        },
        "fieldOwner": {
          "oid": $orgId1,
          "appId": "${ENV(app_id)}"
        },
        "expireAt": "${todayEnd_getTimeStamp_ms()}",
        "ids": [$docTemplateId1],
        "invalidAfterSave": false,
        "needLogin": true,
        "tenantId": $orgId1,
        "validationConfig": {
          "allFieldBindRole": true,
          "sealType": true
        }
      }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - tplToken1: content.data.tplToken

- test:
    name: 获取resourceId
    api: api/epaas-doc-template/content-draft.yml
    variables:
      tenantId: $orgId1
      token: $tplToken1
      sql1: "select content_id from test_epaas_doc_template.content where entity_id='$docTemplateId1';"
      contentId: ${select_sql($sql1, $db_name2)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - resourceId1: content.data.originFile.resourceId

- test:
    name: 获取templateRoleId
    api: api/epaas-doc-template/list-template-role.yml
    variables:
      tenantId: $orgId1
      token: $tplToken1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]
    extract:
      - templateRoleId1: content.data.0.id

- test:
    name: epaas批量保存草稿
    api: api/epaas-doc-template/batch-save-draft.yml
    variables:
      tenantId: $orgId1
      token: $tplToken1
      sql1: "select content_id from test_epaas_doc_template.content where entity_id='$docTemplateId1';"
      data:
        [
        {
          "contentId": "${select_sql($sql1, $db_name2)}",
          "baseFile": {
            "resourceId": $resourceId1,
            "fileType": "PDF"
          },
          "fields": [
          {
            "label": "个人章/签名",
            "custom": false,
            "type": "SIGN",
            "sort": 1,
            "style": {
              "font": 1,
              "fontSize": 12,
              "textColor": "#000",
              "width": 150,
              "height": 60,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": 1,
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "ext": "{}"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": "yyyy-MM-dd HH:mm:ss",
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "dataSource": null,
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": null,
              "overflowType": 2,
              "minFontSize": 8,
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": "1",
              "tickOptions": null,
              "configExt": {
                "signRequirements": "",
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-stamp",
                "assignedPosbean": false,
                "fastCheck": true,
                "addSealRule": "followSeal",
                "keyPosX": 0,
                "keyPosY": 0
              },
              "sealTypes": [
                "PSN"
              ]
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "",
            "templateRoleId": $templateRoleId1,
            "fieldKey": null,
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 220.4257425742574,
              "y": 697.*************,
              "page": 2,
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          }
          ],
          "pageFormatInfoParam": null
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.success", true]

- test:
    name: 流程模板列表
    api: api/flowTemplates/flowTemplateList.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      flowTemplateName: $templateName1
      queryUse: null
      queryLabel: null
      status: null
    setup_hooks:
      - ${hook_sleep_n_secs(2)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ge: ["content.data.total", 1]

- test:
    name: 启用流程模板
    api: api/flowTemplates/enable_flowTemplate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 流程发起详情信息
    api: api/contract-manager2/flowTemplateInfo.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - participantId1: content.data.participants.0.participantId
      - fileId2: content.data.files.0.fileId
      - fileName2: content.data.files.0.fileName

- test:
    name: 获取流程模板预览地址
    api: api/flow-templates/preview-url.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateIds: [$flowTemplateId1]
      previewScene: template_start
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.previewUrl", null]

- test:
    name: 使用模板发起
    api: api/contract-manager2/start-async.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      businessType: 0
      epaasTag: true
      ccs: []
      files:
        [
        {
          "fileId": $fileId2,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $fileName2,
          "from":4,                 #文件来自 1-模板文件 2-合同文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: $flowTemplateId1
      initiatorAccountId: $accountId1
      participants:
        [
        {
          "participantSubjectType":0,
          "role":"3",
          "sealType":"0,1",
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":$participantId1,
          "instances":[
          {
            "account":$account1,
            "accountOid":$accountId1,
            "accountName":$accountName1,
            "accountRealName":true,
            "comment":"",
            "subjectId":$accountId1,
            "subjectName":$accountName1,
            "subjectRealName":true,
            "subjectType":0,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[]
        }
        ]
      scene: 2
      taskName: $taskName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - processId1: content.data.processId
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

#todo 循环查询结果
- test:
    name: 查询异步发起结果
    api: api/contract-manager2/process-result-poll.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: $processId1
    setup_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.success", true]
#      - eq: ["content.data.done", true]

- test:
    name: 查询企业合同列表，验证流程状态为签署中
    api: api/offline_contract/grouping_lists.yml
    variables:
      tenantId: $orgId1
      OperatorId: $accountId1
      pageSize: 10
      pageNum: 1
      menuId: null
      matching: '[{"key":"processId","value":["$processId1"],"sort":"","isPublic":false},{"key":"processCreateTime","value":null,"sort":"","isPublic":false},{"key":"personName","value":[""],"sort":"","isPublic":false}]'
      withApproving: true
    setup_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
#      - eq: ["content.data.groupingProcessList.0.processStatus", 2]

- test:
    name: 撤回流程
    api: api/contract-manager/revoke-process.yml
    variables:
      operatorId: $accountId1
      processId: $processId1
      revokeReason: 集测撤回
      subjectId: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

#清理测试数据
- test:
    name: 删除流程模板
    api: api/contract-manager2/delete_flowTemplate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      sql1: "SELECT flow_template_id FROM doc_cooperation.flow_template where oid='$orgId1' and type=1 and flow_template_name='$templateName1' limit 1;"
      flowTemplateId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]