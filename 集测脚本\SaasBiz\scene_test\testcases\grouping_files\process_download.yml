- config:
    name: 企业合同流程下载
    variables:
      orgId1: 5546e67033104443b5c7f509ccb060f1
      accountId1: 7c1d00fb6f3e473d9a55697b9b9abe8b    #企业1的普通成员1
      account1: ***********
      accountName1: 测试四十二
      accountId2: 8a17e0b48e5948738a990d09b190d45b    #企业1的管理员
      accountId3: 196bb99c0716402892d7118c6243ac36    #企业1的普通成员2
      account3: ***********
      accountName3: 测试四十
      processId1: 676994c2d4864058a607273df730488e    #已完成的单文件流程
      processId2: 60b0452cdda349ce83258e6fd043ba26    #签署中的多文件流程
      roleId1: 77aa3d031f14486eac94e5bfa4e900a1       #企业1待归档下载的角色id
      roleId2: 61652bdcfb6242b5a491951557095cbe       #企业1已归档下载的角色id
      menuId1: 86d1415d9c9b4318968d92043e0cf5df       #企业1的分类1
      roleId5: c2d56230469a47d88b30ba0058eb3492       #企业1的普通成员角色id
      roleId6: 186d7eab1ee34c2e81d4bddb93c6ffa2       #企业1的设置合同保密角色id

- test:
    name: 企业合同待归档列表批量下载已完成的单文件流程-企业管理员默认有权限
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: -1
      processIds: [$processId1]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
    extract:
      - downloadCode1: content.data.downloadCode

- test:
    name: 获取批量下载地址-直接下载
    api: api/contract_manage/asyncDownload_v3/getBatchDownloadUrl.yml
    variables:
      subjectId: $orgId1
      accountId: $accountId2
      downloadCode: $downloadCode1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.downloadUrl", ".pdf?"]

- test:
    name: 企业合同待归档列表批量下载签署中的多文件流程-企业管理员默认有权限
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: -1
      processIds: [$processId2]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
    extract:
      - downloadCode2: content.data.downloadCode

- test:
    name: 获取批量下载地址-打包zip文件下载
    api: api/contract_manage/asyncDownload_v3/getBatchDownloadUrl.yml
    variables:
      subjectId: $orgId1
      accountId: $accountId2
      downloadCode: $downloadCode2
    setup_hooks:
      - ${hook_sleep_n_secs(3)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - contains: ["content.data.downloadUrl", ".zip?"]

- test:
    name: 企业合同待归档列表一键下载-企业管理员默认有权限
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuId: null
      matching: '[{"key":"title","value":["文件下载"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
    extract:
      - downloadCode3: content.data.downloadCode

- test:
    name: 获取批量下载地址-打包zip文件下载
    api: api/contract_manage/asyncDownload_v3/getBatchDownloadUrl.yml
    variables:
      subjectId: $orgId1
      accountId: $accountId2
      downloadCode: $downloadCode3
    setup_hooks:
      - ${hook_sleep_n_secs(3)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - contains: ["content.data.downloadUrl", ".zip?"]

- test:
    name: 企业合同待归档列表批量下载已完成的单文件流程-企业普通成员无权限
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: -1
      processIds: [$processId1]
      source: 2
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 企业合同待归档列表一键下载-企业普通成员无权限
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: null
      matching: '[{"key":"title","value":["文件下载"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同待归档下载的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1,$roleId5,$roleId6]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同待归档列表批量下载已完成的单文件流程-企业普通成员有权限
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: -1
      processIds: [$processId1]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同待归档列表一键下载-企业普通成员有权限
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: null
      matching: '[{"key":"title","value":["文件下载"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同待归档下载的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId5,$roleId6]
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程归档-企业管理员默认有权限
    api: api/grouping_files/grouping.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuIdList: [$menuId1]
      processIdList: [$processId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 企业合同已归档根目录批量下载已完成的单文件流程-企业普通成员无权限
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: MENU_ALL
      processIds: [$processId1]
      source: 2
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 企业合同已归档根目录一键下载-企业普通成员无权限
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: MENU_ALL
      matching: '[{"key":"title","value":["文件下载"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 企业合同已归档分类1列表批量下载已完成的单文件流程-企业普通成员无权限
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
      processIds: [$processId1]
      source: 2
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 企业合同已归档分类1列表一键下载-企业普通成员无权限
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
      matching: '[{"key":"title","value":["文件下载"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同已归档下载的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId2,$roleId5,$roleId6]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同已归档根目录批量下载已完成的单文件流程-企业普通成员有权限
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: MENU_ALL
      processIds: [$processId1]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同已归档根目录一键下载-企业普通成员有权限
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: MENU_ALL
      matching: '[{"key":"title","value":["文件下载"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同已归档分类1列表批量下载已完成的单文件流程-企业普通成员无权限
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
      processIds: [$processId1]
      source: 2
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 企业合同已归档分类1列表一键下载-企业普通成员无权限
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
      matching: '[{"key":"title","value":["文件下载"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 获取可操作的角色列表
    api: api/grouping_permission/roleByAuthorizer.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
      authorizer: $accountId1
      authorizeType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - roleList1: content.data

- test:
    name: 添加目录用户及授权-给accountId1分配menuId1的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId3: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId3",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同已归档分类1列表批量下载已完成的单文件流程-企业普通成员有权限
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
      processIds: [$processId1]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同已归档分类1列表一键下载-企业普通成员有权限
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
      matching: '[{"key":"title","value":["文件下载"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同已归档下载的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId5,$roleId6]
      memberName: ""
      revokeRoleIds: [$roleId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 添加目录用户及授权-取消accountId1的menuId1的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId3: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId3",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 添加目录用户及授权-给accountId3分配menuId1的下载的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId4: ${getValue($roleList1, roleKey, DOC_DOWNLOAD, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account3",
          "name":"$accountName3",
          "oid":"$accountId3",
          "roleId":"$roleId4",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 企业合同已归档分类1列表批量下载已完成的单文件流程-企业普通成员有权限
    api: api/contract_manage/asyncDownload_v3/processes_batch_download_v3.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId3
      menuId: $menuId1
      processIds: [$processId1]
      source: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 企业合同已归档分类1列表一键下载-企业普通成员有权限
    api: api/contract_manage/asyncDownload_v3/download_all_code.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId3
      menuId: $menuId1
      matching: '[{"key":"title","value":["文件下载"],"sort":"","isPublic":false}]'
      withApproving: true
      downloadMapStatus: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.goToJobCenter", false]
      - ne: ["content.data.downloadCode", null]

- test:
    name: 添加目录用户及授权-取消accountId3分配menuId1的下载的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId4: ${getValue($roleList1, roleKey, DOC_DOWNLOAD, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account3",
          "name":"$accountName3",
          "oid":"$accountId3",
          "roleId":"$roleId4",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程移出分类-企业管理员默认有权限
    api: api/grouping_files/removeMenu.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      menuIdList: []
      processIds: [$processId1]
      targetMenuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
