- config:
    name: 查询关联企业《有效》的授权关系
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 子企业是线下授权的
    api: api/auth_relation/getEffectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGid": "22bba3e6af5a43ebaaac89a81150dac9",
          "bizScene": "contractManage"
        }

#    validate:
#      - eq: [content.authRelationId, 374]
#      - eq: [content.childTenantName, esigntest蜀汉]


- test:
    name: 子企业是正式授权过的
    api: api/auth_relation/getEffectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGid": "39a5a137052447d5934867bf5cbd7834",
          "bizScene": "contractManage"
        }

#    validate:
#      - eq: [content.authRelationId, 374]
#      - eq: [content.childTenantName, "esigntest蜀汉"]


- test:
    name: 场景不存在
    api: api/auth_relation/getEffectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGid": "39a5a137052447d5934867bf5cbd7834",
          "bizScene": "test"
        }
    validate:
      - eq: [content.success, false]

- test:
    name: bizScene为空
    api: api/auth_relation/getEffectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGid": "39a5a137052447d5934867bf5cbd7834",
          "bizScene": ""
        }
    validate:
      - eq: [content.success, false]

- test:
    name: childTenantGid为空
    api: api/auth_relation/getEffectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGid": "",
          "bizScene": "contractManage"
        }
    validate:
      - eq: [content.success, false]
