- config:
    name: 查询导出记录


- test:
    name: 查询下载审计日志结果
    api: api/audit-log/export-record.yml
    variables:
        pageNo: "10"
        pageSize: "10"
        orgId_lige: "752cbb97b722461d891f4682042a15c4 "
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", "成功"]

- test:
    name: 查询下载审计日志结果-非企业成员
    api: api/audit-log/export-record.yml
    variables:
        pageNo: ""
        pageSize: ""
        orgId_lige: "00ccae92ea86444ca5fbadb42026c943"
    validate:
        - eq: ["content.code", 10000015]
        - contains: ["content.message", "您不是该企业成员，请联系企业管理员加入企业。"]