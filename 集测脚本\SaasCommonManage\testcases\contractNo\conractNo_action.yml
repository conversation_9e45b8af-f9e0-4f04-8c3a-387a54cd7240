- config:
    name: 合同编号相关操作
    variables:
      orgId: "0001095aa4bb4f178dafd418ac274a62"  #企业oid 阮小五测试企业
      adminId: "a46d29697c50498eb9ff75655ba9bad1"  #管理员oid   阮小五
      oid1: "219461ffede84f629235751ca4967b8e" #企业成员  阮小七
      effectiveTime0: "${getTimeStamp()}" #生效开始时间
      expireTime0: "${getTimeStamp()}"  #有效期截止时间
      data: ${ENV(data)}
      refuse_fileId: "66f9b050-588f-4d23-bbee-7751127716c5"
      flowtemplateId: "029626cede054b8a921411541fa35585"  #流程模板
      grantedUserCode1: "912345098765432345"
      grantedUserName1: "esigntest你是真的秀"
      orgId2: "52b72c6d9ac941bebdd0431d97f2f8ab" # esigntest你是真的秀


#查询规则列表
- test:
    name: 查询规则列表
    variables:
        tenantGid: "5c37ff3bfd934f7e8b283b7a6f50aff2"
        pageSize: 10
        pageNum: 1

    api: api/contractNo/query_contractNo.yml
#    extract:
#        - Cu_sealId: content.data.sealId
    validate:
        - eq: ["status_code",200]
#        - contained_by: ["content.message", ["成功","缺少参数"]]
#    teardown_hooks:
#      - ${hook_sleep_n_secs(2)}

#保存编号规则
- test:
    name: 保存编号规则
    variables:
        tenantGid: "5c37ff3bfd934f7e8b283b7a6f50aff2"
        initNumber: 10001
        prefix: lian${getTimeStamp()}
        ruleName: 测试合同编号
        tailNumber: 5
        tailType: 2
        timeType: 3
        createByOid: "691e9d2a1aae49929cf9c2b446a1e157"
        modifiedByOid: "691e9d2a1aae49929cf9c2b446a1e157"

    api: api/contractNo/save_contractNo.yml
    extract:
        - ruleId1: content.ruleId
    validate:
        - eq: ["status_code",200]
#        - contained_by: ["content.message", ["成功","缺少参数"]]
#    teardown_hooks:
#      - ${hook_sleep_n_secs(2)}

#更新编号规则
- test:
    name: 更新编号规则
    variables:
        ruleId: $ruleId1
        tenantGid: "5c37ff3bfd934f7e8b283b7a6f50aff2"
        initNumber: 10000001
        prefix: lian${getTimeStamp()}
        ruleName: 测试合同编号
        tailNumber: 8
        tailType: 1
        timeType: 0
        createByOid: "691e9d2a1aae49929cf9c2b446a1e157"
        modifiedByOid: "691e9d2a1aae49929cf9c2b446a1e157"

    api: api/contractNo/update_contractNo.yml
#    extract:
#        - ruleId1: content.ruleId
    validate:
        - eq: ["status_code",200]
#        - contained_by: ["content.message", ["成功","缺少参数"]]
#    teardown_hooks:
#      - ${hook_sleep_n_secs(2)}

#查询规则详情
- test:
    name: 查询规则详情
    variables:
        tenantGid: "5c37ff3bfd934f7e8b283b7a6f50aff2"
        ruleId: $ruleId1

    api: api/contractNo/getByRuleId.yml
#    extract:
#        - ruleId1: content.ruleId
    validate:
        - eq: ["status_code",200]

#查询规则详情2
- test:
    name: 查询规则详情2
    variables:
        ruleId: $ruleId1

    api: api/contractNo/getByRuleId2.yml
#    extract:
#        - ruleId1: content.ruleId
    validate:
        - eq: ["status_code",200]


#删除编号规则
- test:
    name: 删除编号规则
    variables:
        tenantGid: "5c37ff3bfd934f7e8b283b7a6f50aff2"
        modifiedOid: "691e9d2a1aae49929cf9c2b446a1e157"
        ruleId: $ruleId1


    api: api/contractNo/del_contractNo.yml
    validate:
        - eq: ["status_code",200]

#查询印章列表简要信息
- test:
    name: 查询印章列表简要信息
    variables:
        tenantId: "52b72c6d9ac941bebdd0431d97f2f8ab"
        OperatorId: "69f8e39686744698ba5ffe303c8d1501"

    api: api/contractNo/seals_simplelist.yml
    validate:
        - eq: ["status_code",200]
        - eq: ["content.code",0]

