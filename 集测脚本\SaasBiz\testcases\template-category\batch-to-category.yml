- config:
    name: 批量添加流程模板到分类下
    variables:
      orgId1: ${ENV(orgId2)}                   #基础版以上的企业
      accountId1: ${ENV(accountId1)}           #orgId1和orgId2的管理员
      categoryId1: ${ENV(categoryId1)}         #orgId1下的模板分类
      flowTemplateId1: ${ENV(flowTemplateId1)} #orgId1下的流程模板
      accountId2: ${ENV(accountId2)}           #orgId1下的普通成员
      accountId3: ${ENV(accountId3)}           #不是orgId1下的成员
      roleId1: ${ENV(roleId1)}                 #orgId1企业下编辑模板权限的角色id
      orgId2: ${ENV(orgId3)}                   #基础版的企业
      categoryId2: ${ENV(categoryId2)}         #orgId2下的模板分类
      flowTemplateId2: ${ENV(flowTemplateId3)} #orgId2下的流程模板
      db_name1: contract_manager


- test:
    name: 批量添加流程模板到分类下-categoryId为空
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      flowTemplateIds: [$flowTemplateId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类id不能为空]

- test:
    name: 批量添加流程模板到分类下-categoryId不存在
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: 123
      flowTemplateIds: [$flowTemplateId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类不存在]

- test:
    name: 批量添加流程模板到分类下-categoryId不属于当前企业
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId2
      flowTemplateIds: [$flowTemplateId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类不存在]

- test:
    name: 批量添加流程模板到分类下-flowTemplateIds有不存在的flowTemplateId
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
      flowTemplateIds: [$flowTemplateId1,"123"]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 流程模板不存在]

- test:
    name: 批量添加流程模板到分类下-flowTemplateIds有不属于当前企业的flowTemplateId
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
      flowTemplateIds: [$flowTemplateId1,$flowTemplateId2]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 流程模板不存在]

- test:
    name: 批量添加流程模板到分类下-操作人不是企业成员
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId3
      categoryId: $categoryId1
      flowTemplateIds: [$flowTemplateId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", "企业成员不存在"]

- test:
    name: 批量添加流程模板到分类下-操作人无该模板的编辑权限
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      categoryId: $categoryId1
      flowTemplateIds: [$flowTemplateId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", "您没有模板编辑权限"]



- test:
    name: 根据分组获取可操作的角色列表
    api: api/role/getRoleList.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      group: TEMP_GROUP
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
#    extract:
#      - roleId3: content.data.0.id     #企业下可编辑的roleId

#- test:
#    name: 流程模板批量授权-给accountId2设置模板的编辑权限
#    api: api/template-manage/batchAuth.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId1
#      flowTemplateAuthList:
#        [
#        {
#          "authList":[
#          {
#            "authId":null,
#            "accountOid":"$accountId2",
#            "roleId":"$roleId3",
#            "roleKey":"TEMP_UPDATE",
#            "type":2
#          }
#          ],
#          "flowTemplateId":"$flowTemplateId1"
#        }
#        ]
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#
#- test:
#    name: 批量添加流程模板到分类下-操作人有该模板的编辑权限，无权操作
#    api: api/template-category/batch-to-category.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId2
#      categoryId: $categoryId1
#      flowTemplateIds: [$flowTemplateId1]
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", *********]
#      - contains: ["content.message", "您没有模板编辑权限"]


- test:
    name: 更新成员所有信息-给accountId2设置全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 批量添加流程模板到分类下-操作人有全局模板编辑权限
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      categoryId: $categoryId1
      flowTemplateIds: [$flowTemplateId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-取消accountId2的全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: []
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 批量添加流程模板到分类下-管理员默认有权限
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
      flowTemplateIds: []
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 批量添加流程模板到分类下-基础版企业不支持
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      categoryId: $categoryId2
      flowTemplateIds: [$flowTemplateId2]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 版本功能不支持]
