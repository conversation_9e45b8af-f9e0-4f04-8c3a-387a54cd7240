- config:
    name: 台账异常case
    variables:
      orgId1: 92e7df785d36440c89096b71a83fcf06
      accountId1: ${ENV(mx_accountId)}
      menuId1: 114abc2ecd5549b9aed53e1b5ee4f8d8  #有指定该分类的台账，无归档条件
      menuId2: 5f30298c14df4cca906af634141c868a  #有归档条件且绑定了非指定分类的台账
      fieldId1: 09b4fb23fc674c6f97fc34f4e99f56af
      formId1: 971a17a8f7084047ab0ea9c0a3c95cc0  #直接发起的台账
      formId2: 615dd7833011478f813a3725d25d12a3  #指定分类的台账，指定的分类是menuId1

- test:
    name: 创建台账-指定分类已存在台账
    api: api/contract-ledger/save-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: [$menuId1]
      conditions: []
      extractType: 6
      fieldIds: [$fieldId1]
      formName: 指定归档分类1-${getTimeStamp_ms()}
      ledgerTemplateMapping: []
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "集测指定分类1已关联其他台账，不可重复创建"]

- test:
    name: 创建台账-指定分类的归档条件已绑定其他台账
    api: api/contract-ledger/save-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: [$menuId2]
      conditions: []
      extractType: 6
      fieldIds: [$fieldId1]
      formName: 指定归档分类2-${getTimeStamp_ms()}
      ledgerTemplateMapping: []
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "集测指定分类2已关联其他台账，不可重复创建"]

- test:
    name: 编辑台账-修改提取方式
    api: api/contract-ledger/update-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: [$menuId1]
      conditions: []
      extractType: 6
      fieldIds: [$fieldId1]
      formId: $formId1
      formName: 指定归档分类台账
      ledgerTemplateMapping: []
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 台账类型不允许修改]

- test:
    name: 编辑台账-修改的分类已存在台账
    api: api/contract-ledger/update-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: [$menuId1]
      conditions: []
      extractType: 6
      fieldIds: [$fieldId1]
      formId: $formId2
      formName: 集测指定归档分类台账2-勿删
      ledgerTemplateMapping: []
      message1: " 已存在"
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", '${strJoin($formName, $message1)}']

- test:
    name: 编辑台账-修改的分类的归档条件已绑定其他台账
    api: api/contract-ledger/update-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: [$menuId2]
      conditions: []
      extractType: 6
      fieldIds: [$fieldId1]
      formId: $formId2
      formName: 集测指定归档分类台账2-勿删
      ledgerTemplateMapping: []
      message1: " 已存在"
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "集测指定分类2已关联其他台账，不可重复创建"]

- test:
    name: 删除台账-台账被归档条件绑定
    api: api/contract-ledger/delete-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", "当前台账有关联的分类，请在分类管理中取消对应'关联台账'后再做此操作"]
