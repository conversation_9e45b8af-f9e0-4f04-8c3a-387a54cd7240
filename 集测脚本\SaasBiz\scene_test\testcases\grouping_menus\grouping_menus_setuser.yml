- config:
    name: 企业合同分类设置权限
    variables:
      orgId1: 26df081c55b74a4f8f080310c3252aaa
      accountId1: 7c7eefda74ff44c4b42daa833901bd78   #企业1的普通成员
      account1: ***********
      accountName1: 测试四十二
      accountId2: 8a17e0b48e5948738a990d09b190d45b   #企业1的管理员
      roleId1: 2f3b3327489b46b0b58c3d32609fd4ed      #企业1的企业合同的分类设置权限角色id
      menuName1: 分类${getTimeStamp_ms()}-新1
      roleId4: 7bac6ad3bc6d4872aaeeaced47a0fcd1      #企业1的普通成员角色id

- test:
    name: 造数据-新增分类
    api: api/grouping_menus/add_grouping_menus.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
      name: $menuName1
      parentMenuId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - menuId1: content.data

- test:
    name: 获取可操作的角色列表
    api: api/grouping_permission/roleByAuthorizer.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
      authorizer: $accountId1
      authorizeType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - roleList1: content.data

- test:
    name: 1添加目录用户及授权-企业普通成员无权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId1
      roleId2: ${getValue($roleList1, roleKey, MENU_ADMIN, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId2",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 2添加目录用户及授权-企业管理员默认有权限，给accountId1设置查看权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId2: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId2",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 查询目录下的授权用户列表-管理员查询
    api: api/grouping_menus/grouping_menus_queryuser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      keyWord: $accountName1
      roleId2: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
    extract:
      - data1: content.data
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["${getValue($data1, oid, $accountId1, roleId)}", $roleId2]

- test:
    name: 3添加目录用户及授权-企业普通成员无分类设置权限的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId1
      roleId3: ${getValue($roleList1, roleKey, MENU_ADMIN, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId3",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同分类设置权限的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1,$roleId4]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 4添加目录用户及授权-企业普通成员有全局企业合同分类设置权限的权限和menuId1的查看权限，取消accountId1的menuId1查看权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId1
      roleId2: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId2",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 查询目录下的授权用户列表-管理员查询
    api: api/grouping_menus/grouping_menus_queryuser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      keyWord: $accountName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 5添加目录用户及授权-企业普通成员无查看权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId1
      roleId3: ${getValue($roleList1, roleKey, MENU_ADMIN, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId3",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同分类设置权限的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId4]
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 添加目录用户及授权-企业管理员默认有权限，给accountId1设置分类管理员权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId3: ${getValue($roleList1, roleKey, MENU_ADMIN, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId3",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 查询目录下的授权用户列表-管理员查询
    api: api/grouping_menus/grouping_menus_queryuser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      keyWord: $accountName1
      roleId3: ${getValue($roleList1, roleKey, MENU_ADMIN, id)}
    extract:
      - data2: content.data
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["${getValue($data2, oid, $accountId1, roleId)}", $roleId3]

- test:
    name: 6添加目录用户及授权-企业普通成员有menuId1的分类管理员权限，取消accountId1的menuId1分类管理员权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId1
      roleId2: ${getValue($roleList1, roleKey, MENU_ADMIN, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId2",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 查询目录下的授权用户列表-管理员查询
    api: api/grouping_menus/grouping_menus_queryuser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      operatorId: $accountId2
      pageSize: 10
      pageNum: 1
      keyWord: $accountName1
    extract:
      - data3: content.data
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["${get_list_len($data3)}", 1]

- test:
    name: 清理数据-删除分类
    api: api/grouping_menus/delete_grouping_menus.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
