- config:
    name: 查询审批业务流程合同文件列表

- test:
    name: 查询审批业务流程合同文件列表-成功
    api: api/approval/biz_contract_files.yml
    variables:
        operatorId: add4951550e84dea9c1cb3b0c07285bd
        tenantId: caf27d6356b14203b8dd9c354c3751b5
        approvalCode: AF-13b8f94a5806471ebd1feda070760e70
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: 查询审批业务流程合同文件列表-非企业成员查看
    api: api/approval/biz_contract_files.yml
    variables:
        operatorId: 3d10fa8ef5b1455faf8be67b1f4dec2a
        tenantId: caf27d6356b14203b8dd9c354c3751b5
        approvalCode: AF-13b8f94a5806471ebd1feda070760e70
    validate:
        - eq: [ "content.code", 10000015 ]
        - eq: [ "content.message", "您不是该企业成员，请联系企业管理员加入企业。" ]

- test:
      name: 查询审批业务流程合同文件列表-企业无权限成员查看
      api: api/approval/biz_contract_files.yml
      variables:
          operatorId: ff0ce63aa86d472a8e83ee7b14e295f1
          tenantId: caf27d6356b14203b8dd9c354c3751b5
          approvalCode: AF-ad0b77dfe0404c7aae546ccc96462e18
      validate:
          - eq: [ "content.code", 120000600 ]
          - eq: [ "content.message", "您无权限查看" ]