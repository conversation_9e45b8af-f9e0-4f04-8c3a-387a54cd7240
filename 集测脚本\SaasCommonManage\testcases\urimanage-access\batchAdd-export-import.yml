- config:
    name: 批量添加接口访问限制，并且导入导出配置
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      uri1: "/var3"
      delete_sql: "delete from saas_request_access_limit where uri = '/var3'"
      db_name: saas-common-manage


- test:
    name: 批量添加接口访问限制
    api: api/urimanage-access/batch-add.yml
    variables:
      bizDomain: "saas"
      desc: 测试批量添加123
      limits: ["NEED_LOGIN","WHITE_LIST","MEMBER_SKIP_CHECK"]
      owner: testsuite
      uri: $uri1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_db_data($delete_sql, $db_name)}

- test:
    name: 根据id导出接口访问限制
    api: api/urimanage-access/export.yml
    variables:
      ids: ["3944"]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - exportUrl: content.data.exportUrl

- test:
    name: 导入接口访问限制
    api: api/urimanage-access/import.yml
    variables:
      importUrl: $exportUrl
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
