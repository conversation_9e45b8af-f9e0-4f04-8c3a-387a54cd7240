- config:
    name: 更新活动
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 更新活动，成功
    api: api/activities/updateactivities.yml
    variables:
      activityName: '测试测试2'
      activityCode: 'testtest1'
      startTime: '1649347200000'
      endTime: '1650124799000'
      pic: 'http://trial-node.oss-cn-hangzhou.aliyuncs.com/upload/c1def064-e518-5272-ae16-a153a760b44a!!4-8.png'
      url: 'https://miniapp-test.tsign.cn/share-guide/testtest/home'
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", '成功']


- test:
    name: 更新活动，活动名称为空
    api: api/activities/updateactivities.yml
    variables:
      activityName: ''
      activityCode: 'testtest1'
      startTime: '1649347200000'
      endTime: '1650124799000'
      pic: 'http://trial-node.oss-cn-hangzhou.aliyuncs.com/upload/c1def064-e518-5272-ae16-a153a760b44a!!4-8.png'
      url: 'https://miniapp-test.tsign.cn/share-guide/testtest/home'
    validate:
      - eq: ["content.code", 70000002]
      - eq: ["content.message", '参数异常: 活动名称不能为空']

