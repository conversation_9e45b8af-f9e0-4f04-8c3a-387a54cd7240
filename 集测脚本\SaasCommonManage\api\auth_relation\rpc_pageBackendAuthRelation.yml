name: 查关联企业列表
variables:
  parentTenantOid: ""
  minEffectiveStartTime: ""
  maxEffectiveStartTime: ""
  minEffectiveEndTime: ""
  maxEffectiveEndTime: ""
  searchContent: ""
  authRelationStatusList: []
  source: ""
request:
  url: ${ENV(saas_common_manage_url)}/pageBackendAuthRelation/input
  method: POST
  headers:
    Content-Type: application/json
  json:
    {
      "pageIndex": $pageIndex,
      "pageSize": $pageSize,
      "parentTenantOid": $parentTenantOid,
      "parentTenantGid": $parentTenantGid,
      "minEffectiveStartTime": $minEffectiveStartTime,
      "maxEffectiveStartTime": $maxEffectiveStartTime,
      "minEffectiveEndTime": $minEffectiveEndTime,
      "maxEffectiveEndTime": $maxEffectiveEndTime,
      "searchContent": $searchContent,
      "authRelationStatusList": $authRelationStatusList,
      "source": $source
    }
