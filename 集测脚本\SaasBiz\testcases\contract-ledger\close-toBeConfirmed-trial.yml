- config:
    name: 关闭待确认体验完成台账通知
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      orgId2: 913ed9f6ac5742a999ca8c002775f490
      formId1: 9f690f83c1044836a97aa638607c237b

- test:
    name: 关闭待确认体验完成台账通知-台账id为空
    api: api/contract-ledger/close-toBeConfirmed-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: null
    validate:
      - eq: ["content.code", *********,]
      - contains: ["content.message", 台账id不能为空]

- test:
    name: 关闭待确认体验完成台账通知-台账id不存在
    api: api/contract-ledger/close-toBeConfirmed-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: 123
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 台账不存在]

- test:
    name: 关闭待确认体验完成台账通知-操作人不是企业成员
    api: api/contract-ledger/close-toBeConfirmed-trial.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 企业成员不存在]