- config:
    name: 合同比对嵌入-发起
    variables:
      taskName1: 直接发起-${getTimeStamp()}
      accountId1: ${ENV(mx_accountId3)}
      account1: ${ENV(mx_account3)}
      accountName1: ${ENV(mx_accountName3)}
      orgId2: ${ENV(mx_orgId5)}
      orgName2: ${ENV(mx_orgName5)}
      fileName1: "test.pdf"
      contentMd5_1: 2FwUG2Zm3BtLVOuuYCPh7g==
      fileSize1: 99580
      filePath1: "data/test.pdf"
      contentType1: application/pdf

- test:
    name: 创建合同比对
    api: api/contract_compare/embed-compare-create.yml
    variables:
      fileHash1: ""
      fileName1: $fileName1
      fileId1: "26a9ff2ceb3c475382a7f6fc7bdd321f"
      processId1: ac358ea5fef24c9180a8564eb0c47d03
      fileHash2: ""
      fileName2: $fileName1
      fileId2: "26a9ff2ceb3c475382a7f6fc7bdd321f"
      processId2: ac358ea5fef24c9180a8564eb0c47d03
      tenantId: $orgId2
      operatorId: $accountId1
    extract:
      compareId: json.data.compareId
    validate:
      - eq: [ "status_code",200 ]
      - eq: [ "json.message",成功 ]
      - ne: [ "json.data.compareId",null ]

