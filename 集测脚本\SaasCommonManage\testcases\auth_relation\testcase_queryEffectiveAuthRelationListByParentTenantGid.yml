- config:
    name: 查询企业下《有效》的关联企业列表
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 可以正常查询出企业下有效的关联企业
    api: api/auth_relation/queryEffectiveAuthRelationListByParentTenantGid.yml
    variables:
      json:
        {
          "parentTenantGid": "39a5a137052447d5934867bf5cbd7834",
          "bizScene": "contractManage"
        }
    validate:
      - eq: [content.authRelationList.0.authRelationId, 325]
      - eq: [content.authRelationList.0.childTenantName, esigntest齐国]

- test:
    name: parentTenantGid为空
    api: api/auth_relation/queryEffectiveAuthRelationListByParentTenantGid.yml
    variables:
      json:
        {
          "parentTenantGid": "",
          "bizScene": "contractManage"
        }
    validate:
      - eq: [content.success, false]

- test:
    name: bizScene为空
    api: api/auth_relation/queryEffectiveAuthRelationListByParentTenantGid.yml
    variables:
      json:
        {
          "parentTenantGid": "39a5a137052447d5934867bf5cbd7834",
          "bizScene": ""
        }
    validate:
      - eq: [content.success, false]
