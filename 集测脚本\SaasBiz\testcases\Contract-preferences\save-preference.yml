- config:
    name: 保存用户合同偏好设置


- test:
    name: 保存用户合同偏好设置
    api: api/Contract-preferences/save-preference.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        orgId: "752cbb97b722461d891f4682042a15c4"
        preferenceKey: "seal_use_apply_way"
        preferenceValue: 1
#    validate:
#        - eq: ["content.code", 0]
#        - contains: ["content.message", 成功]

- test:
    name: 保存用户合同偏好设置-oid为空
    api: api/Contract-preferences/save-preference.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        orgId: ""
        preferenceKey: "seal_use_apply_way"
        preferenceValue: 1
#    validate:
#        - eq: ["content.code", 120000004]
#        - contains: ["content.message", "参数错误: oid不能为空"]

- test:
    name: 保存用户合同偏好设置-非企业成员
    api: api/Contract-preferences/save-preference.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        orgId: "752cbb97b722461d891f4682a15c4"
        preferenceKey: "seal_use_apply_way"
        preferenceValue: 1
#    validate:
#        - eq: ["content.code", 10000015]
#        - contains: ["content.message", "您不是该企业成员，请联系企业管理员加入企业。"]