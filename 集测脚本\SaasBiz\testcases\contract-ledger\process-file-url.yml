- config:
    name: 获取台账合同url
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      orgId2: 913ed9f6ac5742a999ca8c002775f490
      processId1: c6266c7d33404b8192e4e20039103bf4  #非保密合同
      fileId1: 35afcf4d1b8644f0b514ca3d3cc6883f
      processId2: 8dbcd58eb03b4e1c854ad4a0fc21d6b3  #保密合同
      fileId2: 5254d14d026e4966818ca819e96a076d

- test:
    name: 获取台账合同url-processId为空
    api: api/contract-ledger/process-file-url.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: null
      fileId: 123
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", 合同id不能为空]

- test:
    name: 获取台账合同url-processId不存在
    api: api/contract-ledger/process-file-url.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: 123
      fileId: 123
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 合同流程不存在]

- test:
    name: 获取台账合同url-fileId为空
    api: api/contract-ledger/process-file-url.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: 123
      fileId: null
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", 文件id 不能为空]

- test:
    name: 获取台账合同url-fileId不存在
    api: api/contract-ledger/process-file-url.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: $processId1
      fileId: 123
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.canView", false]
      - eq: ["content.data.downloadUrl", null]

- test:
    name: 获取台账合同url-fileId和processId不匹配
    api: api/contract-ledger/process-file-url.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: $processId1
      fileId: $fileId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.canView", false]
      - eq: ["content.data.downloadUrl", null]

- test:
    name: 获取台账合同url-操作人不是企业成员
    api: api/contract-ledger/process-file-url.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      processId: 123
      fileId: 123
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 企业成员不存在]

- test:
    name: 获取台账合同url-管理员查看非保密合同
    api: api/contract-ledger/process-file-url.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: $processId1
      fileId: $fileId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.canView", true]
      - ne: ["content.data.downloadUrl", null]

- test:
    name: 获取台账合同url-管理员查看保密合同
    api: api/contract-ledger/process-file-url.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      processId: $processId2
      fileId: $fileId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.canView", false]
      - eq: ["content.data.downloadUrl", null]