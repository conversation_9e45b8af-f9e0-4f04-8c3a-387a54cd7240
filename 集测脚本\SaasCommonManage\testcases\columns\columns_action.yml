- config:
    name: 栏目相关操作
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      - name1: 测试一下
      - productCode1: mx_test
      - areaCode1: test2
      - accountId1: ${ENV(accountId1)}
      - columnId1: 74
      - db_name1: saas_base_manage
      - sql1: "select case when COUNT(*) > 1 then true else false end from column_info where product_code='mx_test' and area_code='test2' and deleted=0"
      - sql2: "select click_num from saas_base_manage.column_info where id='$columnId1';"
      - clickNum: ${select_sql($sql2, $db_name1)}


- test:
    name: 栏目内容上架
    api: api/columns/enable_columns.yml
    variables:
      columnId: $columnId1
      status: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取应用端栏目展示内容
    api: api/columns/get_columns.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      hasCount: true
      pageNo: 1
      pageSize: 1
      flag: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ge: ["content.data.total", 1]
      - eq: ["content.data.hasMore", $flag]
    extract:
      total: content.data.total


- test:
    name: 栏目内容操作统计
    api: api/columns/columns_hits.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      columnId: $columnId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 栏目内容分页查询
    api: api/columns/columns_list.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      name: $name1
      status: 1
      clickNum1: ${sum($clickNum, 1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.columnList.0.clickNum", $clickNum1]
    setup_hooks:
      - ${hook_sleep_n_secs(3)}


- test:
    name: 栏目内容下架
    api: api/columns/enable_columns.yml
    variables:
      columnId: $columnId1
      status: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取应用端栏目展示内容
    api: api/columns/get_columns.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      hasCount: true
      total1: ${minus($total,1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", $total1]
