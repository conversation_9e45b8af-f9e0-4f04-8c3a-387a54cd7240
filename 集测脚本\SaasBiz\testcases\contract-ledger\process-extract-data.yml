- config:
    name: 查询合同抽取结果
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      orgId2: 913ed9f6ac5742a999ca8c002775f490
      formId1: aa11061a77f44b019146d6575a05e912
      processId1: 5da867d68ce640499c2d63ff059023e4

- test:
    name: 查询合同抽取结果-台账id为空
    api: api/contract-ledger/process-extract-data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: null
      processId: 123
    validate:
      - eq: ["content.code", *********,]
      - contains: ["content.message", 台账id不能为空]

- test:
    name: 查询合同抽取结果-台账id不存在
    api: api/contract-ledger/process-extract-data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: 123
      processId: 123
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 台账不存在]

- test:
    name: 查询合同抽取结果-processId为空
    api: api/contract-ledger/process-extract-data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
      processId: null
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", 合同id不能为空]

- test:
    name: 查询合同抽取结果-processId不存在
    api: api/contract-ledger/process-extract-data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
      processId: 123
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.fieldDataList.0.data", null]

- test:
    name: 查询合同抽取结果-台账id和processId不匹配
    api: api/contract-ledger/process-extract-data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
      processId: $processId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.fieldDataList.0.data", null]

- test:
    name: 查询合同抽取结果-操作人不是企业成员
    api: api/contract-ledger/process-extract-data.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      formId: 123
      processId: 123
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 企业成员不存在]