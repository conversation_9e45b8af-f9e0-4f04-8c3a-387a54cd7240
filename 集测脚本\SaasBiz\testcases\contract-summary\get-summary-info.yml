- config:
    name: 获取合同提取信息


- test:
    name: 获取合同提取信息-成功
    api: api/contract-summary/get-summary-info.yml
    variables:
      "fileId": "49a76364896a4664a28e018528c63174"
      "menuId": "sys_menuId"
      "processId": "53073a756cd9499bb7c9dc1ee1740236"
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]


- test:
    name: 获取合同提取信息-合同id为空
    api: api/contract-summary/get-summary-info.yml
    variables:
      "fileId": "49a76364896a4664a28e018528c63174"
      "menuId": "sys_menuId"
      "processId": ""
    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: 合同流程id不能为空"]

- test:
    name: 获取合同提取信息-合同id错误
    api: api/contract-summary/get-summary-info.yml
    variables:
      "fileId": "49a76364896a4664a28e018528c63174"
      "menuId": "sys_menuId"
      "processId": "111"
    validate:
        - eq: ["content.code", 31202001]
        - eq: ["content.message", "合同流程不存在"]