 #编辑印章授权
    variables:
        appid: ${ENV(appid)}
    request:
        url: ${ENV(inner_open_url)}/v1/saas-common/rules-grant/seals/update-rule-grant
        method: PUT
        headers: ${gen_headers($appid, X-Tsign-Open-Tenant-Id=$tenantid, X-Tsign-Open-Operator-Id=$operatorid)}
        json:
          {
            "appScheme": $appScheme,
            "authKey": $authKey,
            "autoFall": $autoFall,
            "effectiveTime": $effectiveTime,
            "expireReason": $expireReason,
            "expireTime": $expireTime,
            "fallType": $fallType,
            "grantRedirectUrl": $grantRedirectUrl,
            "grantType": $grantType,
            "grantedAccountIds": $grantedAccountIds,
            "grantedUser": $grantedUser,
            "grantedUserCode": $grantedUserCode,
            "grantedUserName": $grantedUserName,
            "granter": $granter,
            "granterCode": $granterCode,
            "granterName": $granterName,
            "h5": $h5,
            "notifySetting": $notifySetting,
            "orgId": $orgId,
            "resourceId": $resourceId,
            "resourceType": $resourceType,
            "roleKey": $roleKey,
            "ruleGrantedId": $ruleGrantedId,
            "scope": $scope,
            "templateKey": $templateKey,
            "token": $token
          }
