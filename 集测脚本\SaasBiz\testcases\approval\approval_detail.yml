- config:
    name: 审批详情（没写完）


- test:
    name: 审批详情，审批不存在
    api: api/approval/approval_detail.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalId: AF-269e154f42080730
      approvalType: 1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 审批不存在]
      - eq: ["content.code", 120000004]


- test:
    name: 审批详情，查询详情成功
    api: api/approval/approval_detail.yml
    variables:
      operatorId: 691e9d2a1aae49929cf9c2b446a1e157
      tenantId: 52b72c6d9ac941bebdd0431d97f2f8ab
      approvalId: AF-81d81e55500441788ca64c8371bc2149
      approvalType: 2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]