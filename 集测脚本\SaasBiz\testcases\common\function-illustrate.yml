- config:
    name: 商机引导
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      db_name1: cm


- test:
    name: 商机引导-functionCode为空
    api: api/common/function-illustrate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: ""
      extendParam: {}
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", functionCode不能为空]

- test:
    name: 商机引导-functionCode不存在
    api: api/common/function-illustrate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: 123
      extendParam: {}
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 会员功能不存在]

- test:
    name: 商机引导--信息采集
    api: api/common/function-illustrate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: info_collect
      extendParam: {}
      newFunctionIllustrate: false
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 关联企业的商机引导
    api: api/common/function-illustrate.yml
    variables:
      tenantId: bc7a0b1bf2bf42f1b99818f660b97420
      operatorId: a959b44e7e0f47c18e205cec5bdd861e
      functionCode: affiliated_enterprises
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.needGuide", true]

- test:
    name: 印章可见范围的商机引导
    api: api/common/function-illustrate.yml
    variables:
      tenantId: bc7a0b1bf2bf42f1b99818f660b97420
      operatorId: a959b44e7e0f47c18e205cec5bdd861e
      functionCode: seal_visible_scope
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.needGuide", False]

- test:
    name: AI手绘的商机引导
    api: api/common/function-illustrate.yml
    variables:
      tenantId: bc7a0b1bf2bf42f1b99818f660b97420
      operatorId: a959b44e7e0f47c18e205cec5bdd861e
      functionCode: ai_hand_draw
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 最低阅读时长_商机引导
    api: api/common/function-illustrate.yml
    variables:
      tenantId: bc7a0b1bf2bf42f1b99818f660b97420
      operatorId: a959b44e7e0f47c18e205cec5bdd861e
      functionCode: saas_start_force_read
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 指定签署附件_商机引导
    api: api/common/function-illustrate.yml
    variables:
      tenantId: bc7a0b1bf2bf42f1b99818f660b97420
      operatorId: a959b44e7e0f47c18e205cec5bdd861e
      functionCode: attachment_config_num
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 管理员查看-定时发送的商机引导
    api: api/common/function-illustrate.yml
    variables:
      tenantId: bdc78b6acf45466289d42dd5cf3a90ec
      operatorId: 0bc26b27ede64cc48e7a800f4c5cbac7
      functionCode: initiate_schedule
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.noFunctionIllustrateType", "NO_FUNCTION_CODE"]
      - eq: ["content.data.trialInfo.baseButtonText", "免费试用7天"]

- test:
    name: 普通员工查看-定时发送的商机引导
    api: api/common/function-illustrate.yml
    variables:
      tenantId: bdc78b6acf45466289d42dd5cf3a90ec
      operatorId: a25720cea1a64970889278bf7af5ef8d
      functionCode: initiate_schedule
      extendParam: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.needGuide", true]
      - eq: ["content.data.trialInfo.baseButtonText", "通知管理员开通试用"]