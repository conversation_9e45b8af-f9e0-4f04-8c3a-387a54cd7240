- config:
    name: 编辑授权（完成）
    variables:
        - code: 0
        - message: 成功

#表单被删除了导致case失败，先注释
- test:
    name: 编辑授权
    api: api/info-collect/task_edit_auth.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        operatorId: "b6366e3a555048f79d12c5fead854124"
        formId: "form6505082ce4b0aae76a743306"
        infoCollectTaskId: ""
#    validate:
#        - eq: ["content.code", $code]
#        - eq: ["content.message", $message]
#        - ne: ["content.data", None]


- test:
    name: 编辑授权，无formId
    api: api/info-collect/task_edit_auth.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        operatorId: "b6366e3a555048f79d12c5fead854124"
        formId: ""
        infoCollectTaskId: ""
    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: must not be blank"]
#        - ne: ["content.data", None]
