- config:
    name: 解析批量添加excel
    variables:
      orgId1: 3c28ba6286b640a4b5480f4758584c54
      accountId1: 475955db49aa4289a8cb9422e200988c
      fileKey1: ${ENV(fileKey2)}
      orgName1: esigntest集测多组织测试企业G
      orgCode1: 9100000043123441A3
      accountId2: ${ENV(accountId1)}

- test:
    name: 解析批量添加excel-fileKey为空
    api: api/org-auth-relation/analysis-batch-add-excel.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fileKey: null
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 文件未上传]

- test:
    name: 解析批量添加excel-fileKey不存在
    api: api/org-auth-relation/analysis-batch-add-excel.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fileKey: 123
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 文件未上传]

- test:
    name: 解析批量添加excel-操作人不是企业成员
    api: api/org-auth-relation/analysis-batch-add-excel.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      fileKey: $fileKey1
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 您不是该企业成员]

- test:
    name: 解析批量添加excel-操作人是企业成员
    api: api/org-auth-relation/analysis-batch-add-excel.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fileKey: $fileKey1
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
      - ne: [ "content.data",null ]