- config:
    name: 解析批量添加excel
    variables:
      orgId1: a29f052269d740acbde042409c698cbf
      accountId1: 475955db49aa4289a8cb9422e200988c
      fileKey1: ${ENV(fileKey_2)}
      orgName1: esigntest集测多组织测试企业G1
      orgCode1: 9100000063360642CP
      accountId2: ${ENV(accountId1)}

- test:
    name: 解析批量添加excel-fileKey为空
    api: api/org-auth-relation/analysis-batch-add-excel.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fileKey: null
#    validate:
#      - eq: ["content.code", ********]
#      - eq: ["content.message", 文件未上传]

- test:
    name: 解析批量添加excel-fileKey不存在
    api: api/org-auth-relation/analysis-batch-add-excel.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fileKey: 123
#    validate:
#      - eq: ["content.code", ********]
#      - eq: ["content.message", 文件未上传]

- test:
    name: 解析批量添加excel-操作人不是企业成员
    api: api/org-auth-relation/analysis-batch-add-excel.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      fileKey: $fileKey1
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 您不是该企业成员]

- test:
    name: 解析批量添加excel-成功
    api: api/org-auth-relation/analysis-batch-add-excel.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fileKey: $fileKey1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
 #     - eq: ["content.data.0.authResources", [“200”, “150”, “100”]]
      - eq: ["content.data.0.childTenantName", $orgName1]
      - eq: ["content.data.0.childUsccCode", $orgCode1]
 #     - eq: ["content.data.0.shareConfigs", ["shareVip"]]
