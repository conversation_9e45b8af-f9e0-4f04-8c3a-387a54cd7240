config:
  name: 数据源相关


testcases:
  -
    name: 查询企业下某渠道的数据源信息
    testcase: testcases/dataSource/testcase_channelDataSourceList.yml

  -
    name: 企业下模版数据源渠道
    testcase: testcases/dataSource/testcase_DataSourceChannel.yml

  - name: 获取参与方数据源字段和关联规则
    testcase: testcases/dataSource/testcase_participantDataSourceField.yml

  - name: 获取模版关联数据源异常信息
    testcase: testcases/dataSource/testcase_getDataSourceError.yml

  - name: 获取数据源字段和关联规则
    testcase: testcases/dataSource/testcase_dataSourceField.yml