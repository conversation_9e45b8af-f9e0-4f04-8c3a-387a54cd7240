- config:
    name: 根据appId获取应用信息
#    base_url: ${ENV(saas_common_manage_url)}



- test:
    name: 传oid和appId
    api: api/getseal/platform-info.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 只传oid
    api: api/getseal/platform-info.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: ""

    validate:
      - eq: ["content.code", 10000007]
      - contains: ["content.message", appId不能为空]

- test:
    name: oid不存在
    api: api/getseal/platform-info.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9db999
      appId: 3876547293

    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

- test:
    name: appId不存在
    api: api/getseal/platform-info.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 12

    validate:
      - eq: ["content.code", 70000603]
      - contains: ["content.message", 授权平台信息错误，请联系客服反馈]





    #teardown_hooks:
     # - ${hook_sleep_n_secs(2)}


