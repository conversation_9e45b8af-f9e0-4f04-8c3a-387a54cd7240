- config:
    name: 审批列表（完成）

- test:
    name: 审批列表，合同审批
    api: api/approval/approval_list.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalName: "发起钉钉审批"
      approvalType: "2"
      approveStartType: "1"
      fromPage: 0
      pageNum: 1
      pageSize: 10
      queryType: 1

    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 0]

- test:
    name: 审批列表，合同审批
    api: api/approval/approval_list.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalName: "测试"
      approvalType: "2"
      approveStartType: "1"
      fromPage: 0
      pageNum: 1
      pageSize: 10
      queryType: 1

    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
#      - eq: ["content.data.total", 24]


- test:
    name: 审批列表，用印审批
    api: api/approval/approval_list.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalName: "666"
      approvalType: "1"
      approveStartType: "1"
      fromPage: 0
      pageNum: 1
      pageSize: 10
      queryType: 1

    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
#      - eq: ["content.data.total",1]


- test:
    name: 审批列表，用印审批
    api: api/approval/approval_list.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalName: "111"
      approvalType: "1"
      approveStartType: "1"
      fromPage: 0
      pageNum: 1
      pageSize: 10
      queryType: 1

    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
#      - eq: ["content.data.total", 10]

- test:
    name: 审批列表，合同审批
    api: api/approval/approval_list.yml
    variables:
      operatorId: c4b417e1c80d4792828823355c6672fc
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalName: "测试"
      approvalType: "2"
      approveStartType: "1"
      fromPage: 0
      pageNum: 1
      pageSize: 10
      queryType: 3

    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
#      - eq: ["content.data.total", 24]

- test:
    name: 审批列表，用印审批
    api: api/approval/approval_list.yml
    variables:
      operatorId: c4b417e1c80d4792828823355c6672fc
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalName: "111"
      approvalType: "1"
      approveStartType: "1"
      fromPage: 0
      pageNum: 1
      pageSize: 10
      queryType: 3

    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
#      - eq: ["content.data.total", 10]