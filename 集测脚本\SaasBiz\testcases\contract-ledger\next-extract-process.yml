- config:
    name: 获取下一条台账提取记录
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      orgId2: 913ed9f6ac5742a999ca8c002775f490
      formId1: aa11061a77f44b019146d6575a05e912
      processId1: 5da867d68ce640499c2d63ff059023e4

- test:
    name: 获取下一条台账提取记录-台账id不存在
    api: api/contract-ledger/next-extract-process.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: 123
      currentProcessId: $processId1
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 台账不存在]

- test:
    name: 获取下一条台账提取记录-processId不存在
    api: api/contract-ledger/next-extract-process.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
      currentProcessId: 123
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.processId", null]

- test:
    name: 获取下一条台账提取记录-操作人不是企业成员
    api: api/contract-ledger/next-extract-process.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      formId: 123
      currentProcessId: 123
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 企业成员不存在]