- config:
    name: 审批操作前置校验


- test:
    name: 同意审批-同一批次的审批流程一起操作--成功
    api: api/approval/operate-pre-check.yml
    variables:
      operatorId: 77b2130de6c3423483d54e44012e3ee2
      tenantId: 01c319f1c2764eaba361d42e59daa983
      "operateDatas": [{"datas":[{"processId":"5733888549fe42a0b2f559e361a66fbf","approvalId":"AF-0008d3f075114469ac12c460d6d59135"}],"approvalType":"1"}]
      "approverId": 77b2130de6c3423483d54e44012e3ee2
      "approvalType": "1"
      "operateType": "AGREE"
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]