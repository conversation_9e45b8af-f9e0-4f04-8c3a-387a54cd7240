- config:
    name: 记录用户行为
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 以个人oid的维度记录用户行为
    api: api/user_action/action_record.yml
    variables:
      json:
        {
          "accountId": "565a742760cc485185bbd3cfc1e47e80",
          "accountType": 0,
          "action": "lansheng_test"
        }
    validate:
      - eq: [content.code, 0]
      - eq: [content.message, 成功]

- test:
    name: 个人gid的维度记录用户行为
    api: api/user_action/action_record.yml
    variables:
      json:
        {
          "accountId": "2e6211b50db54777b8510223a7723c41",
          "accountType": 1,
          "action": "lansheng_test2"
        }
    validate:
      - eq: [content.code, 0]
      - eq: [content.message, 成功]
