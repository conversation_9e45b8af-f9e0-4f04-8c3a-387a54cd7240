- config:
    name: 审查规则创建-异常
    variables:
        appId: "7876701657"
        groupId: "b807dd041732494e937c4af0965836a4"

- test:
    name: 规则保存-不传入审查点
    api: api/contract-review-part1/rule/rule-save.yml
    variables:
        appId: $appId
        groupId: $groupId
        reviewRuleRisks: ["ruleRiskId":null]
        riskLevel: "low"
        ruleId: ""
        ruleName: "123"
        ruleRemark: ""
        convertedStatus: "done"
    validate:
        - eq: [ "content.code", 120001603 ]
        - contains: [ "content.message", 规则保存失败:规则ID不能为空 ]
- test:
    name: 规则保存-名称超过20字
    api: api/contract-review-part1/rule/rule-save.yml
    variables:
        appId: $appId
        groupId: $groupId
        reviewRuleRisks: ["ruleRiskId":"59e7af62dabf40f3bb0b55768c38282d"]
        riskLevel: "low"
        ruleId: "35e93e2e2dad4e84a70d9b44409c6c8f"
        ruleName: "12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901"
        ruleRemark: ""
        convertedStatus: "done"
    validate:
        - ne: [ "content.code", 0 ]
        - contains: [ "content.message", 规则名称不能超过100字符 ]