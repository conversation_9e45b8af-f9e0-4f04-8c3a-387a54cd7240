- config:
    name: 非标模板的相关操作
    variables:
      accountId1: ${ENV(mx_accountId1)}
      account1: ${ENV(mx_account1)}
      accountName1: ${ENV(mx_accountName1)}
      orgId1: ${ENV(mx_orgId1)}
      orgName1: ${ENV(mx_orgName1)}
      fileName1: "test.pdf"
      contentMd5_1: 2FwUG2Zm3BtLVOuuYCPh7g==
      fileSize1: 99580
      filePath1: "data/test.pdf"
      contentType1: application/pdf
      templateName1: 两人仅签模板-${getTimeStamp()}
      taskName1: 使用仅签模板发起-${getTimeStamp()}
#      flowTemplateId2: ${ENV(flowTemplateId2)}
      name1: 模板1-${getTimeStamp()}
      name2: 模板2-${getTimeStamp()}


- test:
    name: 文件直传创建文件
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      contentType: $contentType1
      contentMd5: $contentMd5_1
      convert2Pdf: false
      fileName: $fileName1
      fileSize: $fileSize1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId1: content.data.fileId
      - uploadUrl1: content.data.uploadUrl

- test:
    name: 上传文件到oss
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl1
      contentType: application/pdf
      contentMd5: $contentMd5_1
      filePath: $filePath1
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 创建非标模板
    api: api/contract_manage/create_flowTemplates_new.yml
    variables:
      operatorId: $orgId1
      initiatorAccountId: $accountId1
      tenantId: $orgId1
      taskName: 非标模板测试-${generate_random_str(10)}
      appId: "**********"
      json:
        {
          "approveTemplateId": null,
          "refFlowTemplateId": null,
          "flowTemplateId": "",
          "scene": 3,
          "ccs": [],
          "files": [{
                      "fileId": $fileId1,
                      "fileType": 1,
                      "fileName": $fileName1,
                      "from": 2,
                      "fileSecret": false,
                      "supportReplace": true,
                      "originalFileId": ""
                    }],
          "participants": [{
                             "participantSubjectType": 1,
                             "role": "1,3",
                             "sealType": null,
                             "signRequirements": "1",
                             "roleSet": 1,
                             "fillOrder": 1,
                             "signOrder": 1,
                             "participantLabel": "签署方1",
                             "participantId": null,
                             "instances": null,
                             "willTypes": []
                           }],
          "initiatorAccountId":  $accountId1,
          "taskName": $taskName,
          "signEndTime": null,
          "fileEndTime": null,
          "signValidityConfig": {
            "validityType": 3
          },
          "fileValidityConfig": {
            "validityType": 3
          },
          "platform": 5,
          "skipFill": false,
          "previewType": 0,
          "visibleAccounts": [],
          "supportAddDoc": true
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - templateId: content.data.processId


- test:
    name: 流程模板列表
    api: api/template_manage/flowTemplates.yml
    variables:
      - tenantId: $orgId1
      - accountId: $accountId1
      - flowTemplateName: $templateName1
    validate:
      - eq: ["content.code", 0]
#      - eq: ["content.data.total", 0]

- test:
    name: 禁用流程模板
    api: api/template_manage/template_disable.yml
    variables:
      - flowTemplateId: $templateId
      - tenantId: $orgId1
      - accountId: $accountId1
      - operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除非标模板
    api: api/template_manage/template_delete.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $templateId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

