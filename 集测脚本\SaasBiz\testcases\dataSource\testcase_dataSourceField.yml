- config:
    name: 企业某个渠道的数据源列表
    variables:
      appId: ${ENV(appid)}
      tenantId: 9c6b87f1e2004a37bb5a3a9220d6e103
      operatorId: 934dcca924de4521a1589798d2987f00

- test:
    name: 获取数据源字段和关联规则
    api: api/dataSource/dataSourceField.yml
    variables:
      appId: $appId
      tenantId: $tenantId
      operatorId: $operatorId
      json:
        {
          "fieldTypes": [
            "SAAS_SYS_DATA_APPROVAL_USER"
          ],
          "dataSourceIds": [
            "73D87D24-C069-490E-BEA6-01876D45C57B"
          ]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data", null]

- test:
    name: 获取数据源字段和关联规则---Sourceid错误
    api: api/dataSource/dataSourceField.yml
    variables:
      appId: $appId
      tenantId: $tenantId
      operatorId: $operatorId
      json:
        {
          "fieldTypes": [
            "SAAS_SYS_DATA_APPROVAL_USER"
          ],
          "dataSourceIds": [
            "73D87D24-C069-490E-BEA6-01876D45C57c"
          ]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]
