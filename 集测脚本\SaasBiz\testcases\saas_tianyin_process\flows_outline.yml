- config:
    name: 获取签署任务概要

- test:
    name: 获取签署任务概要-Y
    api: api/saas_tianyin_process/flows_outline.yml
    variables:
      flowId: ${ENV(saaatianyin_flowId)}
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      code: *********
      message: "您没有查看当前流程的权限"

- test:
  name: 获取签署任务概要-成功
  api: api/saas_tianyin_process/flows_outline.yml
  variables:
    flowId: 89f4cad896d64d04b99e6d1024941e77
    accountId: ${ENV(sasstianyin_subjectId_oid)}
    code: 0
    message: "成功"
- test:
    name: 获取签署任务概要-flowId不匹配
    api: api/saas_tianyin_process/flows_outline.yml
    variables:
      flowId: "123456789987654321"
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      code: *********
      message: "流程不存在"

- test:
    name: 获取签署任务概要-accountId不匹配
    api: api/saas_tianyin_process/flows_outline.yml
    variables:
      flowId: ${ENV(saaatianyin_flowId)}
      accountId: "123456789987654321"
      code: *********
      message: "您没有查看当前流程的权限"

- test:
    name: 获取签署任务概要-flowId为空
    api: api/saas_tianyin_process/flows_outline.yml
    variables:
      flowId: ""
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      code: *********
      message: "参数错误: 签署流程id不能为空"

- test:
    name: 获取签署任务概要-accountId为空
    api: api/saas_tianyin_process/flows_outline.yml
    variables:
      flowId: ${ENV(saaatianyin_flowId)}
      accountId: ""
      code: *********
      message: "参数错误: 个人Oid不为空"
