 #获取印章授权书下载地址
    variables:
        appid: ${ENV(appid)}
        operatorid: ""
        sealOwnerOid: ""
    request:
        url: ${ENV(inner_open_url)}/v1/saas-common/rules-grant/seals/download-rule-grant
        method: GET
        headers: ${gen_headers($appid, X-Tsign-Open-Tenant-Id=$tenantid, X-Tsign-Open-Operator-Id=$operatorid)}
        params:
            orgId: $orgId
            ruleGrantedId: $ruleGrantedId
            sealOwnerOid: $sealOwnerOid
