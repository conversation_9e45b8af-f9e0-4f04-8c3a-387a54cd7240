- config:
    name: 关键字搜索


- test:
    name: 关键字搜索-成功
    api: api/contract-summary/extract-keyword-pos.yml
    variables:
      "processId": "c3ac343204fd488f87346ff9b0533aa1"
      "fileId": "69acc03aca58483bb3e740ab34ca5a16"
      "menuId": "sys_menuId"
      "seq1": 1
      "aiValue1": "王明"

    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]

- test:
    name: 关键字搜索-合同ID为空
    api: api/contract-summary/extract-keyword-pos.yml
    variables:
      "processId": ""
      "fileId": "69acc03aca58483bb3e740ab34ca5a16"
      "menuId": "sys_menuId"
      "seq1": 1
      "aiValue1": "王明"

    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: 合同ID不能为空"]

- test:
    name: 关键字搜索-合同ID错误
    api: api/contract-summary/extract-keyword-pos.yml
    variables:
      "processId": "111"
      "fileId": "69acc03aca58483bb3e740ab34ca5a16"
      "menuId": "sys_menuId"
      "seq1": 1
      "aiValue1": "王明"

    validate:
        - eq: ["content.code", 31202001]
        - eq: ["content.message", "合同流程不存在"]

- test:
    name: 关键字搜索-文件id为空
    api: api/contract-summary/extract-keyword-pos.yml
    variables:
      "processId": "c3ac343204fd488f87346ff9b0533aa1"
      "fileId": ""
      "menuId": "sys_menuId"
      "seq1": 1
      "aiValue1": "王明"

    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: 文件ID不能为空"]

- test:
    name: 关键字搜索-文件id错误
    api: api/contract-summary/extract-keyword-pos.yml
    variables:
      "processId": "c3ac343204fd488f87346ff9b0533aa1"
      "fileId": "111"
      "menuId": "sys_menuId"
      "seq1": 1
      "aiValue1": "王明"

    validate:
        - eq: ["content.code", 10000001]
        - eq: ["content.message", "合同不存在"]

