- config:
    name: 编辑表单前获取配置（老接口，不需要）
    variables:
        - code: 0
        - message: 成功

- test:
    name: 编辑表单前获取配置
    api: api/info-collect/form_config.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d "
        operatorId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        formId: "form64f849d4e4b0937d65ec9b98"
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]


- test:
    name: 编辑表单前获取配置,无formid
    api: api/info-collect/form_config.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d "
        operatorId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        formId: "form64f849d4e4b0937d65ec9b98"
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]