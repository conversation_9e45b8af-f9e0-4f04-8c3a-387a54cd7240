- config:
    name: 批量查询子企业有效授权列表
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 校验可以批量查询子企业的有效授权列表
    api: api/auth_relation/effectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGidList": [
            "39a5a137052447d5934867bf5cbd7834",
            "22bba3e6af5a43ebaaac89a81150dac9"
          ],
          "bizScene": "contractManage"
        }

#    validate:
#      - eq: [content.authRelationList.0.authRelationId, 374]
#      - eq: [content.authRelationList.0.childTenantName, esigntest蜀汉]

- test:
    name: 子企业列表为空
    api: api/auth_relation/effectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGidList": [

          ],
          "bizScene": "contractManage"
        }
    validate:
      - eq: [content.success, false]

- test:
    name: bizScene为空
    api: api/auth_relation/effectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGidList": [
            "39a5a137052447d5934867bf5cbd7834"
          ],
          "bizScene": ""
        }
    validate:
      - eq: [content.success, false]

- test:
    name: childTenantGidList传一个空数组
    api: api/auth_relation/effectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGidList": [
            ""
          ],
          "bizScene": "contractManage"
        }
    validate:
      - eq: [content.authRelationList, []]
