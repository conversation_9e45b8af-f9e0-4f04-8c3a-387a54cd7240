- config:
    name: 保存水印模板


- test:
    name: 保存水印模板
    api: api/watermark-template/save_watermark-template.yml
    variables:
      "color": "rgb(217,217,217)"
      "fontFamily": 1
      "size": 16
      "imgHeight": 44
      "imgWidth": 44
      "opacity": 0.7
      "floatMode": "DOWN"
      "leftX": 0
      "pages": [ ]
      "positionType": "TILE"
      "tileDensity": 0.5
      "topY": 0
      "rotate": 30
      "content": "集测新增水印测试1"
      "contentType": 1
      "name": "集测新增水印1"
      "type": 1
      "watermarkId": ""
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - watermarkId: content.data.watermarkId

- test:
    name: 删除水印模板-删除水印
    api: api/watermark-template/delete_watermark-template.yml
    variables:
      watermarkId: watermarkId=$watermarkId
      tenantId: ${ENV(orgId1)}
      operatorid: ${ENV(accountId1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]