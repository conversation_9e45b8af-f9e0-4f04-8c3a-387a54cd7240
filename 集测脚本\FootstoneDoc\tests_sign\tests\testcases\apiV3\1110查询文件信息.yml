- config:
        name: "1110迭代-查询文件信息"
        base_url: ${ENV(footstone_doc_url)}
        variables:
#            - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
            -   path_pdf: data/个人借贷合同.pdf
            -   path_pdf1: data/劳动合同书.pdf
            -   path_pdf2: data/劳动合同书12.pdf
            -   group:
            -   app_id: ${ENV(X-Tsign-Open-App-Id)}
            -   app_secret: "123"
            -   fileId: ${get_file_id($app_id,$path_pdf1)}
            -   docTemplateId: ${ENV(docTemplateId)}
#            -   fileId1: ${ENV(file_id_in_tengqing_PaaS_id_without_real_with_willingness)}

-   test:
        name: "V1查询PDF文件详情-查文件大小"
        variables:
            -  fileId: $fileId
            -  pageSize: true
        api: api/apiV1/V1fileDetail.yml
        validate:
            -   eq: ["content.data.fileId", $fileId]
            -   eq: ["content.data.status", 2]
            -   gt: [content.data.pageWidth, 0]
            -   gt: [content.data.pageHeight, 0]


-   test:
        name: "V1查询PDF文件详情-不查文件大小"
        variables:
            -  fileId: $fileId
            -  pageSize: false
        api: api/apiV1/V1fileDetail.yml
        validate:
            -   eq: ["content.data.fileId", $fileId]
            -   eq: ["content.data.status", 2]
            -   str_eq: [content.data.pageWidth, None]
            -   str_eq: [content.data.pageHeight, None]


-   test:
        name: "V1查询PDF文件详情-不查文件大小"
        variables:
            -  fileId: $fileId
            -  pageSize: ""
        api: api/apiV1/V1fileDetail.yml
        validate:
            -   eq: ["content.data.fileId", $fileId]
            -   eq: ["content.data.status", 2]
            -   str_eq: [content.data.pageWidth, None]
            -   str_eq: [content.data.pageHeight, None]



-   test:
        name: "V3查询PDF文件详情-查文件大小"
        variables:
            -  fileId: $fileId
            -  pageSize: true
        api: api/apiV3/V3fileDetailWithSize.yml
        validate:
            -   eq: ["content.data.fileId", $fileId]
            -   eq: ["content.data.fileStatus", 2]
            -   gt: [content.data.pageWidth, 0]
            -   gt: [content.data.pageHeight, 0]


-   test:
        name: "V3查询PDF文件详情-不查文件大小"
        variables:
            -  fileId: $fileId
            -  pageSize: false
        api: api/apiV3/V3fileDetailWithSize.yml
        validate:
            -   eq: ["content.data.fileId", $fileId]
            -   eq: ["content.data.fileStatus", 2]
            -   str_eq: [content.data.pageWidth, None]
            -   str_eq: [content.data.pageHeight, None]


-   test:
        name: "V3查询PDF文件详情-不查文件大小"
        variables:
            -  fileId: $fileId
            -  pageSize: ""
        api: api/apiV3/V3fileDetailWithSize.yml
        validate:
            -   eq: ["content.data.fileId", $fileId]
            -   eq: ["content.data.fileStatus", 2]
            -   str_eq: [content.data.pageWidth, None]
            -   str_eq: [content.data.pageHeight, None]



-   test:
        name: "查询模板详情-存在自定义控件"
        variables:
          - docTemplateId: $docTemplateId
        api: api/apiV3/V3getTemplateDetail.yml
        extract:
          - data: content.data
        validate:
          - eq: [ "content.code", 0 ]
          - contains: ["content.data.components.2", "originCustomComponentId"]

