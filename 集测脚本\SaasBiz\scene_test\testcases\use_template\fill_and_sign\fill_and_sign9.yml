- config:
    name: 填写方是新的企业和个人账号（需修改）
    variables:
      tenantId_1: ${ENV(mx_orgId1)}                       # 当前空间主体
      tenantId_2: ${ENV(mx_accountId2)}
      operatorId_1: ${ENV(ls_oid)}                        # 操作人oid
      operatorId_2: ${ENV(mx_accountId1)}
      operatorId_3: ${ENV(mx_accountId2)}                 # 第二个填写方
      initiatorAccountId: ${ENV(ls_oid)}                  # 发起人的oid
      account_1: ${ENV(ls_account)}                       # 参与人手机号
      account_2: ${ENV(mx_account2)}
      accountOid_1: ${ENV(ls_oid)}                        # 参与人oid
      accountOid_2: ${ENV(mx_accountId2)}
      subject_1: ${ENV(mx_orgId1)}                        # 参与人主体oid
      subject_2: ${ENV(mx_accountId2)}
      accountName_1: ${ENV(ls_accountName)}               # 参与人姓名
      accountName_2: ${ENV(mx_accountName2)}
      subjectName_1: ${ENV(mx_orgName1)}                    # 参与人的主体名称
      subjectName_2: ${ENV(mx_accountName2)}
      css_account_1: ${ENV(mx_account1)}                    # 抄送人的手机号
      css_account_name_1: ${ENV(mx_accountName1)}          # 抄送人姓名
      css_accountOid_1: ${ENV(mx_accountId1)}               # 抄送人oid
      css_subject_1: ${ENV(mx_orgId1)}                      # 抄送人主体oid
      css_subjectName_1: ${ENV(mx_orgName1)}               # 抄送人主体名称
      fileId_1: 8b843fd6c90d4c87a888061cced947dd             # 模板的文件id
      file_name1: 劳动合同.pdf                               # 文件名称
      fileId_2: b52cfad6b6294daf91cfa41d23aac844             # 附件id
      file_name2: 测试的文件.docx                            # 附件名称
      taskName: 主企业+2个填写方+水印${generate_random_str(3)}
      flowTemplateId: ${ENV(ls_flowTemplateId_8)}                   # 流程模板ID
      approveTemplateId: ""                                       # 合同审批ID
      accountId1: ${ENV(mx_accountId1)}
      credCodeOrg: "5591000000NWLPL0KFHP"
      credTypeOrgan: "CRED_ORG_UNKNOWN"
      name: "esignTest实名组织删除测试"
      pass: true
      

- test:
    name: setup - 创建企业1
    variables:
      json: {"creater": $accountId1, "properties": {"name": $name}}
    api: api/account/org_create.yml
    extract:
      - orgId1: content.data.accountId
    validate:
      - eq: ["content.code", 0]
      - len_gt: ["$orgId1", 0]

- test:
    name: setup - 企业1实名
    variables:
      ouid: $orgId1
      credCode: $credCodeOrg
      credType: "CRED_ORG_UNKNOWN"
      name: "esignTest测试企业$credCodeOrg"
      pass: true
      serviceId: "${get_timestamp(16)}"
    api: api/account/completeRealname.yml
    validate:
       - eq: ["status_code", 200]
       - eq: ["content.success", true]
       - eq: ["content.data.passed", true]
       
-   test:
        name: 获取赠送场景菜单
        api: api/account/api_get_gift_menus.yml
        validate:
            -   eq: [ content.code, ******** ]
            -   eq: [ content.message, "执行成功" ]

-   test:
        name: 获取电子签名商品详情
        api: api/account/api_commodity_detail.yml
        variables:
            commodityId: ${ENV(dianziqianming)}
        extract:
            -   k_commodityName: content.data.commodityName
        validate:
            -   eq: [ content.code, ******** ]
            -   eq: [ content.message, "执行成功" ]

- test:
    name: 机构账号测试 - 根据机构id - 查询机构getBaseInfo成功 - ${ENV(app_id)}
    variables:
        appId: "${ENV(app_id)}"
        accountId: $orgId1
    api: api/account/get_org_base_info.yml
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.ouid", $accountId]
      - eq: ["content.data.appId", "${ENV(app_id)}"]
    extract:
      - guid: content.data.guid

-   test:
        name: 创建特殊审批赠送-电子签名
        api: api/account/api_create_gift_approval.yml
        variables:
            approvalType: 1
            commodityName: $k_commodityName
            gid: $guid
            commodityId: ${ENV(dianziqianming)}
            price: 3
        validate:
            -   eq: [ content.code, ******** ]
            -   eq: [ content.message, "执行成功" ]
        extract:
            -   check_approvalId: content.data
         
      
- test:
    name: 获取流程发起的详情信息
    api: api/contract_manage/processDetail.yml
    variables:
      tenantId: $tenantId_1
      operatorId: $operatorId_1
      flowTemplateId: $flowTemplateId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - participantId1: content.data.participants.0.participantId
      - participantId2: content.data.participants.1.participantId
      
- test:
    name: 使用模板发起填写流程(2个填写方)
    api: api/contract_manage/startProcess.yml
    variables:
      tenantId: $tenantId_1
      operatorId: $operatorId_1
      businessType: 0
      ccs: [
        {
          "account": $css_account_1,
          "accountOid": $css_accountOid_1,
          "accountName": $css_account_name_1,
          "accountNick":"",
          "accountRealName":true,
          "comment":"",
          "subjectId": $css_subject_1,
          "subjectName": $css_subjectName_1,
          "subjectRealName":true,
          "subjectType":1
        }
      ]
      files:
        [
          {
            "fileId": $fileId_1,
            "fileType":1,             #文件类型：1-合同文件 2-附件
            "fileName": $file_name1,
            "from":2,                 #文件来自：1-模板文件 2-合同文件
            "fileSecret":false        #文件是否保密
          },
          {
            "fileId": $fileId_2,
            "fileType":2,
            "fileName": $file_name2,
            "from":2,
            "fileSecret":false
          }
        ]
      flowTemplateId: $flowTemplateId
      initiatorAccountId: $initiatorAccountId
      approveTemplateId: $approveTemplateId
      signEndTime: ""
      fileEndTime: ""
      signValidityConfig:
        {
         "validityType":3,
         "durationYear":null,
         "durationMonth":null,
         "durationDay":null
        }
      fileValidityConfig:
        {
         "validityType":3,
         "durationYear":null,
         "durationMonth":null,
         "durationDay":null
        }
      participants:
        [
        {
          "participantSubjectType": 1,
          "role": "3,1",
          "sealType": null,
          "signRequirements": "3,1",
          "roleSet":1,
          "type": 1,
          "signSealType": 1,
          "fillOrder": 1,
          "signOrder": 1,
          "participantLabel":"签署方1",
          "participantId": $participantId1,
          "instances":[
          {
            "account": $account_1,
            "accountOid": $accountOid_1,
            "accountName": $accountName_1,
            "accountRealName":true,
            "comment":"",
            "subjectId": $subject_1,
            "subjectName": $subjectName_1,
            "subjectRealName":true,
            "subjectType": 1,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[
            "EMAIL",
            "FACE",
            "CODE_SMS",
            "SIGN_PWD"
            ]
        },
        {
          "participantSubjectType": 0,
          "role": "3,1",
          "sealType": "1",
          "signRequirements": null,
          "roleSet":1,
          "type": 1,
          "signSealType": 1,
          "fillOrder": 2,
          "signOrder": 1,
          "participantLabel":"签署方2",
          "participantId": $participantId2,
          "instances":[
          {
            "account": $account_2,
            "accountOid": $accountOid_2,
            "accountName": $accountName_2,
            "accountRealName":true,
            "comment":"",
            "subjectId": $subject_2,
            "subjectName": $subjectName_2,
            "subjectRealName":true,
            "subjectType": 0,
            "preFillValues":null,
            "subTaskName":""
          }
          ],
          "willTypes":[
            "CODE_SMS",
            "FACE",
            "EMAIL",
            "SIGN_PWD"
          ]
        }
        ]
      scene: 2
      taskName: $taskName
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.flowId", null]
      - ne: ["content.data.realProcessId", null]
    extract:
#      - flowId1: content.data.flowId
      - traceId1: headers.X-Tsign-Trace-Id
      - processId1: content.data.processId
      - cooperationId1: content.data.cooperationId
    teardown_hooks:
      - ${hook_sleep_n_secs(10)}

- test:
    name: 获取协作流程详情
    api: api/footstone-doc/getCooperationInfo.yml
    variables:
      cooperationId: $cooperationId1
      loginAccountId: null
      menuId: null
      queryAccountId: $operatorId_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - cooperationerId1: content.data.cooperationers.0.cooperationerId
      - cooperationerId2: content.data.cooperationers.1.cooperationerId

- test:
    name: 根据协作方获取结构项数据
    api: api/footstone-doc/doccooperations/getComponents.yml
    variables:
      cooperationId: $cooperationId1
      cooperationerId: $cooperationerId1
      menuId: null
      queryAccountId: $operatorId_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - templateId1: content.data.cooperationTaskStructsList.0.cooperationStucts.0.templateId
      - structId1: content.data.cooperationTaskStructsList.0.cooperationStucts.0.structId  #单行文本
      - structId2: content.data.cooperationTaskStructsList.0.cooperationStucts.1.structId  #多行文本
      - structId3: content.data.cooperationTaskStructsList.0.cooperationStucts.2.structId  #身份证号
      - structId4: content.data.cooperationTaskStructsList.0.cooperationStucts.3.structId  #数字
      - structId5: content.data.cooperationTaskStructsList.0.cooperationStucts.4.structId  #日期
      - structId6: content.data.cooperationTaskStructsList.1.cooperationStucts.0.structId  #下拉选择
      - structId7: content.data.cooperationTaskStructsList.1.cooperationStucts.1.structId  #单选
      - structId8: content.data.cooperationTaskStructsList.1.cooperationStucts.2.structId  #多选

- test:
    name: 提交填写(参与人1)
    api: api/footstone-doc/commitContents.yml
    variables:
      cooperationId: $cooperationId1
      cooperationerId: $cooperationerId1
      contentDatas:
        [
        {
          "content":"单行测试",
          "structId": $structId1,
          "templateId": $templateId1
        },
        {
          "content":"多行文本test\n哈哈",
          "structId": $structId2,
          "templateId": $templateId1
        },
        {
          "content":"***************",
          "structId": $structId3,
          "templateId": $templateId1
        },
        {
          "content":"123456",
          "structId": $structId4,
          "templateId": $templateId1
        },
        {
          "content": "${today_date()}",
          "structId": $structId5,
          "templateId": $templateId1
        }
        ]
      operateAccountId: $operatorId_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
#    extract:
#      - flowId1: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}
      
- test:
    name: 提交填写(参与人2)
    api: api/footstone-doc/commitContents.yml
    variables:
      cooperationId: $cooperationId1
      cooperationerId: $cooperationerId2
      contentDatas:
        [
        {
          "content": 0,
          "structId": $structId6,
          "templateId": $templateId1
        },
        {
          "content": 0,
          "structId": $structId7,
          "templateId": $templateId1
        },
        {
          "content":"[0,1,2]",
          "structId": $structId8,
          "templateId": $templateId1
        }
        ]
      operateAccountId: $operatorId_3
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
#    extract:
#      - flowId1: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}
      
      
- test:
    name: teardown - 删除测试企业1
    variables:
          organId: $orgId1
    api: api/account/org_delete.yml
    validate:
      - eq: ["content.code", 0]
      - eq: ["status_code", 200]