- config:
    name: 变更saasbiz信息
#    base_url: ${ENV(saas_common_manage_url)}


- test:
    name: 变更saasbiz信息
    api: api/rpc/changeBizConfigs.yml
    variables:
      oid: ccb284fadd34405ba68a0c710cccc74f
      gid:
      saasBizConfigList: [
      {
        "configInfo":"{\"type\":\"admin\",\"userOid\":\"haha\"}",
        "key":"contract_approval_transfer"
      }
      ]
    validate:
      - eq: ["status_code", 200]