- config:
    name: 下载审计日志
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 查询下载审计日志结果--taskId不存在
    api: api/audit-log/get-download-url.yml
    variables:
      firstModule: 'saas_seal_management'
      operatorId: '8723abe263f142009d98471ce7a70bd8'
      tenantId: 'd9ec3169a6e04d148e5a8cc08ab3c13d'
      taskId: '123456'
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.downloadUrl", null]

- test:
    name: 下载审计日志
    api: api/audit-log/download-log.yml
    variables:
      endTime: 1667974401599
      event: ''
      firstModule: "saas_seal_management"
      innerCall: false
      operatorId: '8723abe263f142009d98471ce7a70bd8'
      tenantId: 'd9ec3169a6e04d148e5a8cc08ab3c13d'
      startTime: 1666627200678
    extract:
      - taskId: content.data.taskId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询下载审计日志结果
    api: api/audit-log/get-download-url.yml
    variables:
      firstModule: 'saas_seal_management'
      operatorId: '8723abe263f142009d98471ce7a70bd8'
      tenantId: 'd9ec3169a6e04d148e5a8cc08ab3c13d'
      taskId: $taskId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询导出记录
    api: api/audit-log/export-record.yml
    variables:
      operatorId: '8723abe263f142009d98471ce7a70bd8'
      tenantId: 'd9ec3169a6e04d148e5a8cc08ab3c13d'
      pageNo: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
