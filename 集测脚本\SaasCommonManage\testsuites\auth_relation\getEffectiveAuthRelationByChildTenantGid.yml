config:
  name: 关联企业《有效》的授权关系

testcases:
  -
    name: 查询关联企业《有效》的授权关系
    testcase: testcases/auth_relation/testcase_getEffectiveAuthRelationByChildTenantGid.yml
    
  -
    name: 批量查询子企业有效授权列表
    testcase: testcases/auth_relation/testcase_effectiveAuthRelationByChildTenantGid.yml
    

  -
    name: 查询现在有效 + 以前有效的授权列表
    testcase: testcases/auth_relation/testcase_historyOrNowEffectiveAuthRelationByChildTenantGid.yml
    
  -
    name: 查询企业下《有效》的关联子企业列表
    testcase: testcases/auth_relation/testcase_queryEffectiveAuthRelationListByParentTenantGid.yml