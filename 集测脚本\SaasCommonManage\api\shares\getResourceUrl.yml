name: 获取资源最终地址
variables:
  redirectUrl: ""
  token: ""
  platform: ""
  appId: ""
  clientId: ""
  appid: ${ENV(appid)}
request:
  url: ${ENV(inner_open_url)}/v1/saas-common/shares/getResourceUrl
  method: GET
  headers: ${gen_headers($appid)}
  params:
    resourceShareId: $resourceShareId
    accountId: $accountId
    subjectId: $subjectId
    redirectUrl: $redirectUrl
    token: $token
    platform: $platform
    appId: $appId
    clientId: $clientId
