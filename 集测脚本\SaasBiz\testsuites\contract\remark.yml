testcases:
  -
    name: 合同备注场景
    testcase: testcases/contract/add-remark.yml
  -
    name: 合同类型串场景
    testcase: testcases/contract-categories/contract-category-scene.yml
  -
    name: 保存合同类型校验
    testcase: testcases/contract-categories/save-contract-category.yml
  -
    name: 归档条件串场景
    testcase: testcases/auto-archive/auto-archive-scene.yml
  -
    name: 台账串场景-合同类型AI智能提取
    testcase: testcases/contract-ledger/contract-ledger-scene1.yml
  -
    name: 台账串场景-模板填写控件提取
    testcase: testcases/contract-ledger/contract-ledger-scene2.yml
  -
    name: 台账串场景-直接发起合同AI智能提取
    testcase: testcases/contract-ledger/contract-ledger-scene3.yml
  -
    name: 台账串场景-我收到的合同AI智能提取
    testcase: testcases/contract-ledger/contract-ledger-scene4.yml
  -
    name: 台账串场景-纸质文件AI智能提取
    testcase: testcases/contract-ledger/contract-ledger-scene5.yml
  -
    name: 查询&修改AI提取值
    testcase: testcases/contract-ledger/form-data-action.yml
  -
    name: 根据租户id查询租户信息,包含已注销的
    testcase: testcases/saas-common/tenant-list.yml
  -
    name: 台账异常case
    testcase: testcases/contract-ledger/contract-ledger-scene6.yml
  -
    name: 归档条件自动绑定指定分类的台账
    testcase: testcases/contract-ledger/rule-auto-bind.yml
  -
    name: 归档条件异常case
    testcase: testcases/auto-archive/auto-archive-scene2.yml
  -
    name: 查询提取限制
    testcase: testcases/contract-ledger/get-extract-config.yml
  -
    name: 合同比对
    testcase: testcases/contract/contract-analysis.yml