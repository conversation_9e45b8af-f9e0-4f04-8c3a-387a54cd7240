- config:
    name: 查询现在有效 + 以前有效的授权列表
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 传入正确的子企业列表，可以正常查询出来
    api: api/auth_relation/historyOrNowEffectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGidList": [
            "39a5a137052447d5934867bf5cbd7834"
          ],
          "bizScene": "contractManage"
        }
    validate:
      - eq: [content.mixAuthRelationList.0.childTenantGid, 39a5a137052447d5934867bf5cbd7834]


- test:
    name: childTenantGidList为空
    api: api/auth_relation/historyOrNowEffectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGidList": [],
          "bizScene": "contractManage"
        }
    validate:
      - eq: [content.success, false]

- test:
    name: bizScene为空
    api: api/auth_relation/historyOrNowEffectiveAuthRelationByChildTenantGid.yml
    variables:
      json:
        {
          "childTenantGidList": ["39a5a137052447d5934867bf5cbd7834"],
          "bizScene": ""
        }
    validate:
      - eq: [content.success, false]
