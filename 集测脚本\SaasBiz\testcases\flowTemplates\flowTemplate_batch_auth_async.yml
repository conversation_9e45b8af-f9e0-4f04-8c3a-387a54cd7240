- config:
    name: 流程模板批量授权-异步
    variables:
        - code: 0
        - message: 成功

- test:
      name: 流程模板批量授权-异步
      variables:
        flowTemplateIds: "05fc50e4ed5047e59c1cb64e26e964d4"
        accountOid: 'a690089d48a14707a1cb78c453a0d99'
        authId: null
        roleId: "3d382d084c774a2fbcecda422c457d55"
        roleKey: "TEMP_UPDATE"
        type: 2
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        operatorId : "a690089d48a14707a1cb78c453a0d991"

      api: api/flowTemplates/flowTemplate_batch_auth_async.yml
      validate:
          - eq: ["content.code", 0]
          - eq: ["content.message", "成功"]