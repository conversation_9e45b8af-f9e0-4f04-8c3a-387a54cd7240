- config:
    name: 删除流程模板分类
    variables:
      orgId1: ${ENV(orgId2)}                   #基础版以上的企业
      accountId1: ${ENV(accountId1)}           #orgId1和orgId2的管理员
      accountId2: ${ENV(accountId2)}           #orgId1下的普通成员
      accountId3: ${ENV(accountId3)}           #不是orgId1下的成员
      roleId1: ${ENV(roleId1)}                 #orgId1企业下编辑模板权限的角色id
      orgId2: ${ENV(orgId3)}                   #基础版的企业
      categoryId1: ${ENV(categoryId2)}         #orgId2下的模板分类
      categoryName1: 测试分类5                 #orgId1的分类
      categoryName2: 测试分类4                 #orgId1的分类
      db_name1: contract_manager
      sql1: "SELECT contract_category_id FROM doc_cooperation.contract_category where oid='$orgId1' and contract_category_name='$categoryName1';"
      sql2: "SELECT contract_category_id FROM doc_cooperation.contract_category where oid='$orgId1' and contract_category_name='$categoryName2';"


- test:
    name: 创建流程模板分类-造数据1
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryName: $categoryName1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 创建流程模板分类-造数据2
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryName: $categoryName2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除流程模板分类-categoryId为空
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", categoryId不能为空]

- test:
    name: 删除流程模板分类-categoryId不存在
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: 123
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类不存在]

- test:
    name: 删除流程模板分类-categoryId不属于当前企业
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类不存在]

- test:
    name: 删除流程模板分类-操作人不是企业成员
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId3
      categoryId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", "企业成员不存在"]

- test:
    name: 删除流程模板分类-操作人无全局模板编辑权限
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      categoryId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", "您没有模板编辑权限"]

- test:
    name: 更新成员所有信息-给accountId2设置全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除流程模板分类-操作人有全局模板编辑权限
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      categoryId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-取消accountId2的全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: []
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除流程模板分类-管理员默认有权限
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ${select_sql($sql2, $db_name1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除流程模板分类-版本不支持
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 版本功能不支持]
