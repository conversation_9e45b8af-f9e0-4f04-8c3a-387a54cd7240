- config:
    name: 获取活动详情
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 活动不存在
    api: api/activities/activitiesdetail.yml
    variables:
      activityCode: '111'
    validate:
      - eq: ["content.code", 70000405]
      - eq: ["content.message", '该活动不存在']


- test:
    name: 活动存在
    api: api/activities/activitiesdetail.yml
    variables:
      activityCode: '测试测试'
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", '成功']
