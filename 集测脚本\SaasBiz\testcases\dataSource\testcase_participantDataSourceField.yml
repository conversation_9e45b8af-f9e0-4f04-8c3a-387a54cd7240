- config:
    name: 获取参与方数据源字段和关联规则
    variables:
      appId: ${ENV(appid)}
      tenantId: 08486d61823d4c2086122b26fb3e615a
      operatorId: 0bc26b27ede64cc48e7a800f4c5cbac7

- test:
    name: 获取企业下的参与方和数据源的关联信息
    api: api/dataSource/participantDataSourceField.yml
    variables:
      appId: $appId
      tenantId: $tenantId
      operatorId: $operatorId
      json:
        {
          "dataSourceIds": [ "form66eb9c67e4b0941661d2757a" ]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.dataSourceFields.0.dataSourceId", "form66eb9c67e4b0941661d2757a"]