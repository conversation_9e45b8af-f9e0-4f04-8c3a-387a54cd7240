- config:
    name: 查询当前已经授权的数量
    variables:
      orgId: "0001095aa4bb4f178dafd418ac274a62"  #企业oid 阮小五测试企业
      adminId: "a46d29697c50498eb9ff75655ba9bad1"  #管理员oid   阮小五
      data: ${ENV(data)}
      refuse_fileId: "66f9b050-588f-4d23-bbee-7751127716c5"




- test:
    name: 查询当前一级授权的数量
    variables:
      sealId: "8cd946dd-d171-43db-9bf9-a9b364888ce6"
      orgId: "25306ce4efbb40c4b373c51a3a6ceeb2"
      sealGrantBizId: ""
      grantLevel: 1
    api: api/seal/grant-num.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

- test:
    name: 查询当前二级授权的数量
    variables:
      sealId: ""
      orgId: ""
      sealGrantBizId: "f5492ee9-2f07-424d-9aad-e7f95f9592f9"
      grantLevel: 2
    api: api/seal/grant-num.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]
