name: banner管理列表查询
variables:
  pageNum: 1
  pageSize: 10
  bannerName: ""
  descFlag: false
  orderField: 1
  status: ""
request:
  url: ${ENV(saas_common_manage_url)}/v1/saas-common/banners/page
  method: POST
  headers:
    x-timevale-jwtcontent: eyJhbGlhcyI6Iuemu-atjCIsImlkIjoibGlnZSJ9
    Content-Type: application/json
  json:
    {
      "areaCode": $areaCode,
      "bannerName": $bannerName,
      "descFlag": $descFlag,
      "orderField": $orderField,
      "pageNum": $pageNum,
      "pageSize": $pageSize,
      "productCode": $productCode,
      "status": $status
    }
