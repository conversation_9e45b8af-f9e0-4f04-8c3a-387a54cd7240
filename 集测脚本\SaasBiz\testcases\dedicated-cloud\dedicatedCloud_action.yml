- config:
    name: 专属云项目的增删改查
    variables:
      tenantId: ${ENV(mx_orgId1)}                       # 当前空间主体
      operatorId: ${ENV(mx_accountId1)}                 # 操作人oid
      dedicatedCloud_name: 专属云-${today_getTimeStamp_ms()}
      app_id: ${ENV(app_id)}
      db_host: ${ENV(db_host1)}
      db_user: saas_integration
      db_psw: Saas_Integration#123456#
      db_name: saas_integration


- test:
    name: 查看专属云的配置
    api: api/dedicated-cloud/dedicatedCloud_config.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]


- test:
    name: 查看授权企业下的关联企业
    api: api/dedicated-cloud/org_authRelation_list.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#      - eq: ["content.data.list.0.subjectOid", "08486d61823d4c2086122b26fb3e615a"]
      
- test:
    name: 查看该企业下的appId
    api: api/dedicated-cloud/subject_open_app.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      subjectOid: $tenantId
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#      - eq: ["content.data.list.0.appId", "7876705782"]

- test:
    name: 创建专属云项目
    api: api/dedicated-cloud/create_dedicatedCloud.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "projectName": $dedicatedCloud_name,
          "serverUrl": "https://lp-zsyyqxm-projectk8s.tsign.cn/dedicated-sign",
          "appId": "7876705782",
          "authAppIds": []
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - dedicatedCloudId_1: content.data.dedicatedCloudId

- test:
    name: 查看企业下的专属云项目列表
    api: api/dedicated-cloud/dedicatedCloud_list.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "pageNum": 1,
          "pageSize": 1
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.list.0.dedicatedCloudId", $dedicatedCloudId_1]

- test:
    name: 查询专属云项目的详情
    api: api/dedicated-cloud/dedicatedCloud_detail.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      dedicatedCloudId: $dedicatedCloudId_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.dedicatedCloudId", $dedicatedCloudId_1]

- test:
    name: 修改专属云项目
    api: api/dedicated-cloud/dedicatedCloud_update.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      json:
        {
          "projectName": $dedicatedCloud_name,
          "serverUrl": "https://lp-zsyyqxm-projectk8s.tsign.cn/dedicated-sign",
          "authAppIds": [{
                           "subjectGid": "4d1759c54c6c4f949903c7ae231877d7",
                           "appId": "7876705795"
                         }],
          "dedicatedCloudId": $dedicatedCloudId_1
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 下载专属云的配置文件
    api: api/dedicated-cloud/downloadConfig.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      dedicatedCloudId: $dedicatedCloudId_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新专属云的启用状态
    api: api/dedicated-cloud/dedicatedCloud_updateStatus.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId
      app_id: $app_id
      sql_1: "SELECT dedicated_project_id FROM saas_integration.dedicated_project_config where app_id='7876705782' and deleted =0 limit 1;"
      dedicated_project_id1: ${select_sql($sql_1, $db_name)}
      sql_2: "delete from dedicated_project_config where dedicated_project_id='$dedicated_project_id1';"
      sql_3: "delete from dedicated_project_auth where dedicated_project_id='$dedicated_project_id1';"
      json:
        {
          "dedicatedCloudId": $dedicated_project_id1,
          "status": 1
        }
    validate:
      - eq: ["content.code", 38000008]
#      - eq: ["content.message", "专属云服务访问地址无法连通，请确认地址是否准确后重试"]
    teardown_hooks:
      - ${delete_sql($db_host, $db_user, $db_psw, $db_name, $sql_2)}
      - ${delete_sql($db_host, $db_user, $db_psw, $db_name, $sql_3)}

#- test:
#    name: 创建专属云项目
#    api: api/dedicated-cloud/create_dedicatedCloud.yml
#    variables:
#      tenantId: $tenantId
#      operatorId: $operatorId
#      app_id: $app_id
#      json:
#        {
#          "projectName": $dedicatedCloud_name,
#          "serverUrl": "https://lp-zsyyqxm-projectk8s.tsign.cn/dedicated-sign",
#          "appId": "7876705782",
#          "authAppIds": []
#        }
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#    extract:
#      - dedicatedCloudId_1: content.data.dedicatedCloudId
#
#- test:
#    name: 查看企业下的专属云项目列表
#    api: api/dedicated-cloud/dedicatedCloud_list.yml
#    variables:
#      tenantId: $tenantId
#      operatorId: $operatorId
#      app_id: $app_id
#      json:
#        {
#          "pageNum": 1,
#          "pageSize": 1
#        }
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#      - eq: ["content.data.list.0.dedicatedCloudId", $dedicatedCloudId_1]
#
#- test:
#    name: 查询专属云项目的详情
#    api: api/dedicated-cloud/dedicatedCloud_detail.yml
#    variables:
#      tenantId: $tenantId
#      operatorId: $operatorId
#      app_id: $app_id
#      dedicatedCloudId: $dedicatedCloudId_1
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#      - eq: ["content.data.dedicatedCloudId", $dedicatedCloudId_1]
#
#- test:
#    name: 修改专属云项目
#    api: api/dedicated-cloud/dedicatedCloud_update.yml
#    variables:
#      tenantId: $tenantId
#      operatorId: $operatorId
#      app_id: $app_id
#      json:
#        {
#          "projectName": $dedicatedCloud_name,
#          "serverUrl": "https://lp-zsyyqxm-projectk8s.tsign.cn/dedicated-sign",
#          "authAppIds": [{
#                           "subjectGid": "4d1759c54c6c4f949903c7ae231877d7",
#                           "appId": "7876705795"
#                         }],
#          "dedicatedCloudId": $dedicatedCloudId_1
#        }
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#
#- test:
#    name: 下载专属云的配置文件
#    api: api/dedicated-cloud/downloadConfig.yml
#    variables:
#      tenantId: $tenantId
#      operatorId: $operatorId
#      app_id: $app_id
#      dedicatedCloudId: $dedicatedCloudId_1
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#
#- test:
#    name: 更新专属云的启用状态
#    api: api/dedicated-cloud/dedicatedCloud_updateStatus.yml
#    variables:
#      tenantId: $tenantId
#      operatorId: $operatorId
#      app_id: $app_id
#      sql_1: "delete from dedicated_project_config where dedicated_project_id=$dedicatedCloudId_1"
#      sql_2: "delete from dedicated_project_auth where dedicated_project_id=$dedicatedCloudId_1"
#      json:
#        {
#          "dedicatedCloudId": $dedicatedCloudId_1,
#          "status": 1
#        }
#    validate:
#      - eq: ["content.code", 38000008]
#      - contains: ["content.message", "请确认地址是否准确后重试"]
#    teardown_hooks:
#      - ${delete_sql($db_host, $db_user, $db_psw, $db_name, $sql_1)}
#      - ${delete_sql($db_host, $db_user, $db_psw, $db_name, $sql_2)}

