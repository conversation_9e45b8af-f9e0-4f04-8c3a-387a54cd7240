- config:
    name: 用户领章授权记录
#    base_url: ${ENV(saas_common_manage_url)}



- test:
    name: 传oid、appID、redirectUrl
    api: api/getseal/add-record.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293
      url: https://test-seal.tsign.cn/realNameSealBase/authToSeal

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: url为空
    api: api/getseal/add-record.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293
      url:
    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message", 领章页面地址不能为空]

- test:
    name: appId为空
    api: api/getseal/add-record.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId:
      url: https://test-seal.tsign.cn/realNameSealBase/authToSeal
    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message", appId不能为空]

- test:
    name: oid为空
    api: api/getseal/add-record.yml
    variables:
      oid: ""
      appId: 3876547293
      url: https://test-seal.tsign.cn/realNameSealBase/authToSeal
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]



    #teardown_hooks:
      #- ${hook_sleep_n_secs(2)}


