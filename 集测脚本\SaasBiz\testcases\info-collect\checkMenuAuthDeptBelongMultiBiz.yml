- config:
    name: 获取某些部门是否为隔离部门（已完成）
    variables:
        - code: 0
        - message: 成功

- test:
    name: 获取某些部门是否为隔离部门
    api: api/info-collect/checkMenuAuthDeptBelongMultiBiz.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        operatorId: "a690089d48a14707a1cb78c453a0d991"
        deptIds: ["d6fefb2763df4888aaf5b6406adfb7de"]
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]
        - ne: ["content.data", None]


- test:
    name: 获取某些部门是否为隔离部门
    api: api/info-collect/checkMenuAuthDeptBelongMultiBiz.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        operatorId: "b6366e3a555048f79d12c5fead854124"
        deptIds: ""
    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", '请求数据参数校验不通过，请重新检查后重试']
        - ne: ["content.data", None]