- config:
    name: 审批拒绝（还没调通）


#- test:
#    name: 审批拒绝
#    api: api/approval/approval_reject.yml
#    variables:
#      operatorId: 565a742760cc485185bbd3cfc1e47e80
#      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
#      approvalType: 1
#      approvalId: AF-269e154f42080731
#      taskId: c82c5499-d2c2-11ed-922b-5aecfee89704
#      remark: 1
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.message", 审批流程已结束]
#      - eq: ["content.code", 14650208]

- test:
    name: 发起一个审批流
    api: api/common/startapproval_input.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      skipCheck: true
      approvalName: "辰南-直接发起流程"
      approvalTemplateCode: "ATe066e1c4d88a4f329c3400c1b77357bc"
      subjectOid: "d9ec3169a6e04d148e5a8cc08ab3c13d"
      subjectGid: "37bdebc911bf495ca13c7410fd314a9b"
      subjectName: "杭州正道文化传媒有限公司"
      initiatorOid: "565a742760cc485185bbd3cfc1e47e80"
      initiatorGid: "2e6211b50db54777b8510223a7723c41"
      initiatorName: "吴志强"
      initiatorDeptId:
      approvalTemplateConditionType: 0
      bizId: "1c9e547f06574a68a41738517271449c"
      bizGroupId: "1c9e547f06574a68a41738517271449c"
    validate:
      - eq: [ "status_code", 200 ]
    extract:
      approvalId: content.approvalCode
#
#- test:
#    name: 审批拒绝，成功
#    api: api/approval/approval_reject.yml
#    variables:
#      operatorId: 565a742760cc485185bbd3cfc1e47e80
#      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
#      approvalType: 2
#      datas: [ {
#        "approvalId": $approvalId
#      } ]
#      remark: 1
#    validate:
#      - eq: [ "status_code", 200 ]
#      - eq: [ "content.message", 成功 ]


- test:
    name: 发起一个审批流
    api: api/common/startapproval_input.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      skipCheck: true
      approvalName: "辰南-直接发起流程"
      approvalTemplateCode: "ATe066e1c4d88a4f329c3400c1b77357bc"
      subjectOid: "d9ec3169a6e04d148e5a8cc08ab3c13d"
      subjectGid: "37bdebc911bf495ca13c7410fd314a9b"
      subjectName: "杭州正道文化传媒有限公司"
      initiatorOid: "565a742760cc485185bbd3cfc1e47e80"
      initiatorGid: "2e6211b50db54777b8510223a7723c41"
      initiatorName: "吴志强"
      initiatorDeptId:
      approvalTemplateConditionType: 0
      bizId: "1c9e547f06574a68a41738517271449c"
      bizGroupId: "1c9e547f06574a68a41738517271449c"
    validate:
      - eq: [ "status_code", 200 ]
    extract:
      approvalId1: content.approvalCode


- test:
    name: 发起一个审批流
    api: api/common/startapproval_input.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      skipCheck: true
      approvalName: "辰南-直接发起流程"
      approvalTemplateCode: "ATe066e1c4d88a4f329c3400c1b77357bc"
      subjectOid: "d9ec3169a6e04d148e5a8cc08ab3c13d"
      subjectGid: "37bdebc911bf495ca13c7410fd314a9b"
      subjectName: "杭州正道文化传媒有限公司"
      initiatorOid: "565a742760cc485185bbd3cfc1e47e80"
      initiatorGid: "2e6211b50db54777b8510223a7723c41"
      initiatorName: "吴志强"
      initiatorDeptId:
      approvalTemplateConditionType: 0
      bizId: "1c9e547f06574a68a41738517271449c"
      bizGroupId: "1c9e547f06574a68a41738517271449c"
    validate:
      - eq: [ "status_code", 200 ]
    extract:
      approvalId2: content.approvalCode


- test:
    name: 审批拒绝，成功(批量)
    api: api/approval/approval_reject.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalType: 2
      datas: [ {
        "approvalId": $approvalId1 },{
        "approvalId": $approvalId2
      } ]
      remark: 1
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ "content.message", 成功 ]
