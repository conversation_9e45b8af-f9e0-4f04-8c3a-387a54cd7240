- config:
    name: 审批事件是否已存在（完成）


- test:
    name: 复制审批模版,合同审批
    api: api/approval/template_copy.yml
    variables:
      operatorId: 691e9d2a1aae49929cf9c2b446a1e157
      tenantId: 52b72c6d9ac941bebdd0431d97f2f8ab
      approvalTemplateId : AT39E102635B824301A81F0DB9C1A1776F
      approvalTemplateType: 2

    extract:
      approvalTemplateId1: content.data.approvalTemplateId
    validate:
      - eq: ["content.code", 0]
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]

- test:
    name: 审批事件是否已存在,合同审批
    api: api/approval/template_exist_condition.yml
    variables:
      operatorId: 691e9d2a1aae49929cf9c2b446a1e157
      tenantId: 52b72c6d9ac941bebdd0431d97f2f8ab
      approvalTemplateId : $approvalTemplateId1
      approvalTemplateType: 2
      conditionType : 0
      conditionValues :
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除审批模版,合同审批
    api: api/approval/template_del.yml
    variables:
      operatorId: 691e9d2a1aae49929cf9c2b446a1e157
      tenantId: 52b72c6d9ac941bebdd0431d97f2f8ab
      approvalTemplateId : $approvalTemplateId1
      approvalTemplateType: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]

#- test:
#    name: 审批事件是否已存在,用印审批
#    api: api/approval/template_exist_condition.yml
#    variables:
#      operatorId: 565a742760cc485185bbd3cfc1e47e80
#      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
#      approvalTemplateId : ATa19d880e382b4546b44e7c337a1e3e65
#      approvalTemplateType: 1
#      conditionType : 0
#      conditionValues :
#
#
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]


- test:
    name: 审批事件是否已存在
    api: api/approval/template_exist_condition.yml
    variables:
      operatorId: "e2368fdcd51a48c5bcc5b5da2277e8c0"
      tenantId: "554027011f28412f8b0ecd518dbaa008"
      approvalTemplateId : "c354e2b336d64c7e9c24733368157925"
      approvalTemplateType: 2
      conditionType : 1
      conditionValues : ["a6c165c4c225427c97b7541ddcb1ddfd"]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

