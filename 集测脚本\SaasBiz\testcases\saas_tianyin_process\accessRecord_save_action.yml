- config:
    name: 保存访问记录
    variables:
      - url_save: https://testfront.tsign.cn:8887/index



#- test:
#    name: 保存访问记录-accountId为空
#    api: api/saas_tianyin_process/accessRecord_save.yml
#    variables:
#      accountId: ""
#      url: 123
#    validate:
#      - eq: [ "content.code", ******** ]
#      - contains: [ "content.message", 账号ID不能为空 ]


- test:
    name: 保存访问记录-url为空
    api: api/saas_tianyin_process/accessRecord_save.yml
    variables:
      accountId: ${ENV(gray_user_oid)}
    validate:
      - eq: [ "content.code", ********* ]
      - contains: [ "content.message", url不能为空 ]


- test:
    name: 保存访问记录
    api: api/saas_tianyin_process/accessRecord_save.yml
    variables:
      accountId: ${ENV(gray_user_oid)}
      url: $url_save
    validate:
      - eq: [ "content.code", 0 ]
      - contains: [ "content.message", 成功 ]


- test:
    name: 获取上一个访问记录URL
    api: api/saas_tianyin_process/accessRecord_before.yml
    variables:
      accountId: ${ENV(gray_user_oid)}
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "content.data.redirectUrl", $url_save ]
