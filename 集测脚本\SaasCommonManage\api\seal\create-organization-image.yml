 #新增企业图片印章
    variables:
        appid: ${ENV(appid)}
    request:
        url: ${ENV(inner_open_url)}/v1/saas-common/organizations/seals/create-organization-image
        method: POST
        headers: ${gen_headers($appid, X-Tsign-Open-Tenant-Id=$tenantid)}
        json:
          {
            "alias": $alias,
            "auditFlag": $auditFlag,
            "data": $data,
            "downloadFlag": $downloadFlag,
            "handleFlag": $handleFlag,
            "height": $height,
            "notifyUrl": $notifyUrl,
            "operateType": $operateType,
            "sealBizType": $sealBizType,
            "sealType": $sealType,
            "transparentFlag": $transparentFlag,
            "type": $type,
            "uploadFileMd5": $uploadFileMd5,
            "width": $width
          }
