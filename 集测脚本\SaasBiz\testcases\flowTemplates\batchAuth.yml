- config:
    name: 流程模板批量授权（完成）
    variables:
        accountId1: 276b5a8f2a964142bb1ebcf0dd82bf72
        orgId1: 67e1edfc41be46dfa824c8af811ca844
        flowTemplateId1: 0c00b681ce7743b7b671bfb22b42d952

- test:
    name: 流程模板批量授权
    api: api/flowTemplates/batchAuth.yml
    variables:
        operatorId: $accountId1
        tenantId: $orgId1
        authList:
          [
          {
              "authId": "ddc5db94a0ef4c1db463ec4569546fb2",
              "accountOid": null,
              "roleId": "32f94074ea3d45039c74a2c3e25e0c79",
              "roleKey": "TEMP_AUTH",
              "type": 4
          },
          {
              "authId": "2d9125dc916345b59422e39cf439ecaf",
              "accountOid": null,
              "roleId": "32f94074ea3d45039c74a2c3e25e0c79",
              "roleKey": "TEMP_AUTH",
              "type": 4
          },
          {
              "authId": "fcc30002b63448089500b4cd7a2f2343",
              "accountOid": null,
              "roleId": "32f94074ea3d45039c74a2c3e25e0c79",
              "roleKey": "TEMP_AUTH",
              "type": 4
          },
          {
              "authId": "e6c5379b77284befbe51538831b7beed",
              "accountOid": null,
              "roleId": "f204ffa804ce4dba804d2c66705b2b89",
              "roleKey": "TEMP_USE",
              "type": 4
          }
          ]
        flowTemplateId: $flowTemplateId1
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]
