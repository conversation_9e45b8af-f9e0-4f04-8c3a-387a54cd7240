- config:
    name: saas弹窗相关操作
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      - name1: ${getTimeStamp_ms()}
      - productCode1: mx_test
      - areaCode1: test1
      - accountId1: ${ENV(accountId1)}


- test:
    name: 新增弹窗
    api: api/popups/add_popups.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      name: $name1
      conditionType: 0
      configMap: {
        "productKey":"BZQ",
        "functionKey":"fill_reform"
      }
      hasSatisfy: true
      type: 2
      noticeType: 1
      popupType: 1

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 弹窗分页查询
    api: api/popups/popups_list.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      name: $name1
      status: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - popId1: content.data.data.0.id


- test:
    name: 修改弹窗
    api: api/popups/update_popups.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      name: $name1
      id: $popId1
      conditionType: 0
      configMap: {
        "productKey": "BZQ",
        "functionKey": "fill_reform"
      }
      hasSatisfy: true
      type: 2
      noticeType: 1
      popupType: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]



- test:
    name: 启用弹窗
    api: api/popups/enable_popups.yml
    variables:
      popId: $popId1
      status: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取当前弹窗弹出内容-展示弹窗
    api: api/popups/get_popups.yml
    variables:
      proCode: $productCode1
      areaCode: $areaCode1
      userId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.id", $popId1]


- test:
    name: 弹窗展示回调
    api: api/popups/callback_popups.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      popId: $popId1
      userId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取当前弹窗弹出内容-无弹窗
    api: api/popups/get_popups.yml
    variables:
      proCode: $productCode1
      areaCode: $areaCode1
      userId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", null]


- test:
    name: 弹窗操作统计-点击链接
    api: api/popups/click_popups.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      popId: $popId1
      userId: $accountId1
      type: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 弹窗操作统计-点击关闭
    api: api/popups/click_popups.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      popId: $popId1
      userId: $accountId1
      type: 2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}


- test:
    name: 弹窗分页查询
    api: api/popups/popups_list.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      name: $name1
      status: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.data.0.clickNum", 1]
      - eq: ["content.data.data.0.closeNum", 1]


- test:
    name: 禁用弹窗
    api: api/popups/enable_popups.yml
    variables:
      popId: $popId1
      status: 0
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 删除弹窗
    api: api/popups/delete_popups.yml
    variables:
      popId: $popId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
