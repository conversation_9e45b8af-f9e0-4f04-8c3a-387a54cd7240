- config:
    name: 企业空间-部分解约、部分解约后再次解约
    variables:
      - accountId: ${ENV(mx_accountId1)}
        orgId: ${ENV(mx_orgId1)}
        taskName1: 直接发起-${getTimeStamp()}
        taskName2: 直接发起-${getTimeStamp()}-解除协议
        account1: ${ENV(mx_account1)}
        accountName1: ${ENV(mx_accountName1)}
        orgName1: ${ENV(mx_orgName1)}
        fileName1: "test.pdf"
        fileName2: "test2.pdf"
        contentMd5_1: 2FwUG2Zm3BtLVOuuYCPh7g==
        fileSize1: 99580
        filePath1: "data/test.pdf"
        filePath2: "data/test2.pdf"
        contentType1: application/pdf
        orgId2: ${ENV(mx_orgId2)}
        orgCode2: 9100000056418279X1
        orgName2: esigntest自动化测试企业11


- test:
    name: 文件直传创建文件-1
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $orgId
      accountId: $accountId
      contentType: $contentType1
      contentMd5: $contentMd5_1
      convert2Pdf: false
      fileName: $fileName1
      fileSize: $fileSize1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - fileId1: content.data.fileId
      - uploadUrl1: content.data.uploadUrl


- test:
    name: 上传文件到oss-1
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl1
      filePath: $filePath1
      contentType: $contentType1
      contentMd5: $contentMd5_1
    validate:
      - eq: [ "content.errCode", 0 ]
      - eq: [ "content.msg", 成功 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}


- test:
    name: 文件直传创建文件-2
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $orgId
      accountId: $accountId
      contentType: $contentType1
      contentMd5: $contentMd5_1
      convert2Pdf: false
      fileName: $fileName2
      fileSize: $fileSize1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - fileId2: content.data.fileId
      - uploadUrl2: content.data.uploadUrl


- test:
    name: 上传文件到oss-2
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl2
      filePath: $filePath2
      contentType: $contentType1
      contentMd5: $contentMd5_1
    validate:
      - eq: [ "content.errCode", 0 ]
      - eq: [ "content.msg", 成功 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}



- test:
    name: 直接发起
    api: api/contract_manage/startProcess.yml
    variables:
      tenantId: $orgId
      operatorId: $accountId
      businessType: 0
      ccs: [ {
        "account": $account1,
        "accountOid": $accountId,
        "accountName": $accountName1,
        "accountNick": "",
        "accountRealName": true,
        "comment": "",
        "subjectId": $accountId,
        "subjectName": $accountName1,
        "subjectRealName": true,
        "subjectType": 0
      } ]
      files:
        [
          {
            "fileId": $fileId1,
            "fileType": 1,             #文件类型，1-合并文件 2-附件
            "fileName": $fileName1,
            "from": 2,                 #文件来自 1-模板文件 2-合同文件
            "fileSecret": false,       #文件是否保密
            "contractNo": "testNo1",
            "contractNoType": 2
          },
          {
            "fileId": $fileId2,
            "fileType": 1,             #文件类型，1-合并文件 2-附件
            "fileName": $fileName2,
            "from": 2,                 #文件来自 1-模板文件 2-合同文件
            "fileSecret": false,       #文件是否保密
            "contractNo": "testNo2",
            "contractNoType": 2
          }
        ]
      flowTemplateId: ""
      initiatorAccountId: $accountId
      participants:
        [
          {
            "participantSubjectType": 1,
            "role": "3",
            "sealType": null,
            "signRequirements": "1,3",
            "roleSet": 1,
            "fillOrder": 0,
            "signOrder": 1,
            "participantLabel": "签署方1",
            "participantId": null,
            "instances": [
              {
                "account": $account1,
                "accountOid": $accountId,
                "accountName": $accountName1,
                "accountRealName": true,
                "comment": "",
                "subjectId": $orgId,
                "subjectName": $orgName1,
                "subjectRealName": true,
                "subjectType": 1,
                "preFillValues": null,
                "subTaskName": ""
              }
            ],
            "willTypes": [ "FACE", "CODE_SMS", "EMAIL", "SIGN_PWD" ]
          },
          {
            "participantSubjectType": 0,
            "role": "3",
            "sealType": "1",
            "signRequirements": null,
            "roleSet": 1,
            "fillOrder": 0,
            "signOrder": 1,
            "participantLabel": "签署方2",
            "participantId": null,
            "instances": [
              {
                "account": $account1,
                "accountOid": $accountId,
                "accountName": $accountName1,
                "accountRealName": true,
                "comment": "",
                "subjectId": $accountId,
                "subjectName": $accountName1,
                "subjectRealName": true,
                "subjectType": 0,
                "preFillValues": null,
                "subTaskName": ""
              }
            ],
            "willTypes": [ "FACE", "CODE_SMS", "EMAIL", "SIGN_PWD" ]
          }
        ]
      scene: 1
      taskName: $taskName1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - flowId1: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}




- test:
    name: 查询签署流程详情
    api: api/footstone/flowDetail.yml
    variables:
      flowId: $flowId1
      authorizerIds: $orgId
      queryAccountId: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.flowDesc", "签署中" ]
    extract:
      - signfieldId1: content.data.signDocs.0.signfields.0.signfieldId
      - signfieldId2: content.data.signDocs.1.signfields.0.signfieldId
      - posBean1: content.data.signDocs.0.signfields.0.posBean
      - posBean2: content.data.signDocs.1.signfields.0.posBean
      - processId1: content.data.processId


- test:
    name: 查询签署可用印章列表
    api: api/footstone/getSignSeals.yml
    variables:
      flowId: $flowId1
      accountId: $accountId
      batchSign: false
      signerAccountId: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - sealId1: content.data.officialSeals.0.organSeals.0.sealId
      - sealId2: content.data.officialSeals.0.legalSeals.0.sealId


- test:
    name: 获取意愿url地址
    api: api/footstone/getIdentifyUrl.yml
    variables:
      flowId: $flowId1
      accountId: $accountId
      payerAccountGid: "4d1759c54c6c4f949903c7ae231877d7"
      saleSchemaId: 18
      scope: GENERAL
      bizCtxIds: [ $flowId1 ]
      bizType: 1
      clientType: PC
      scene: 1
      signSerialId: null
      space: $accountId
      willTypeHided: null
      wukongOid: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - bizId1: content.data.bizId


- test:
    name: 发起验证码
    api: api/footstone/sendSms.yml
    variables:
      accountId: $accountId
      flowIds: [ $flowId1 ]
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - bizId1: content.data.bizId


- test:
    name: 校验验证码认证
    api: api/footstone/checkSms.yml
    variables:
      accountId: $accountId
      authCode: 123456
      bizId: $bizId1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "content.data.passed", true ]


- test:
    name: 提交签署
    api: api/footstone/add-Update-Execute.yml
    variables:
      flowId: $flowId1
      accountId: $accountId
      addSignfields: [
        {
          "sealId": $sealId1,
          "signerOperatorAuthorizerId": $orgId,
          "sealType": "",
          "signWillingType": 0,
          "crossEnterpriseSeal": false,
          "signerAccountId": $accountId,
          "assignedPosbean": true,
          "posBean": {
            "posX": 234.**************,
            "posY": 738.*************,
            "addSignTime": false,
            "width": 159,
            "signDateBeanType": 2,
            "posPage": 1,
            "height": 159
          },
          "authorizedAccountId": $orgId,
          "signType": 1,
          "signerOperatorId": $accountId,
          "flowId": $flowId1,
          "fieldType": 0,
          "actorIndentityType": 2,
          "fileId": $fileId1,
        },
        {
          "sealId": $sealId2,
          "signerOperatorAuthorizerId": $orgId,
          "sealType": "",
          "signWillingType": 0,
          "crossEnterpriseSeal": false,
          "signerAccountId": $accountId,
          "assignedPosbean": true,
          "posBean": {
            "posX": 466.*************,
            "posY": 605.*************,
            "addSignTime": false,
            "width": 76,
            "signDateBeanType": 2,
            "posPage": 1,
            "height": 38
          },
          "authorizedAccountId": $orgId,
          "signType": 1,
          "signerOperatorId": $accountId,
          "flowId": $flowId1,
          "fieldType": 0,
          "actorIndentityType": 3,
          "fileId": $fileId1,
          "order": 1
        },
        {
          "sealId": $sealId1,
          "signerOperatorAuthorizerId": $orgId,
          "sealType": "",
          "signWillingType": 0,
          "crossEnterpriseSeal": false,
          "signerAccountId": $accountId,
          "assignedPosbean": true,
          "posBean": {
            "posX": 244.**************,
            "posY": 719.*************,
            "addSignTime": false,
            "width": 159,
            "signDateBeanType": 2,
            "posPage": 1,
            "height": 159
          },
          "authorizedAccountId": $orgId,
          "signType": 1,
          "signerOperatorId": $accountId,
          "flowId": $flowId1,
          "fieldType": 0,
          "actorIndentityType": 2,
          "fileId": $fileId2,
          "order": 1
        },
        {
          "sealId": $sealId2,
          "signerOperatorAuthorizerId": $orgId,
          "sealType": "",
          "signWillingType": 0,
          "crossEnterpriseSeal": false,
          "signerAccountId": $accountId,
          "assignedPosbean": true,
          "posBean": {
            "posX": 395.**************,
            "posY": 562.*************,
            "addSignTime": false,
            "width": 76,
            "signDateBeanType": 2,
            "posPage": 1,
            "height": 38
          },
          "authorizedAccountId": $orgId,
          "signType": 1,
          "signerOperatorId": $accountId,
          "flowId": $flowId1,
          "fieldType": 0,
          "actorIndentityType": 3,
          "fileId": $fileId2,
          "order": 1
        }
      ]
      async: true
      signfieldIds: [ ]
      updateSignfields: [ ]
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}


- test:
    name: 查询签署流程详情
    api: api/footstone/flowDetail.yml
    variables:
      flowId: $flowId1
      authorizerIds: $orgId
      queryAccountId: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.flowDesc", "完成" ]


- test:
    name: 流程待解约合同查询接口-流程状态为已完成
    api: api/contract_manage/process_rescind/rescindFileList.yml
    variables:
      - accountId: $accountId
      - tenantId: $orgId
      - processId: $processId1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - fileId3: content.data.files.0.fileId
      - fileId4: content.data.files.1.fileId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}


- test:
    name: 生成解约流程合同文件
    api: api/contract_manage/process_rescind/generateRescindFile.yml
    variables:
      - processId: $processId1
      - accountId: $accountId
      - tenantId: $orgId
      - rescindFileIds: [ $fileId3 ]
      - rescindRemark: "条款内容有误"
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - fileId5: content.data.files.0.fileId
      - fileName5: content.data.files.0.fileName
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}


- test:
    name: 直接发起--部分解约
    api: api/contract_manage/startProcess.yml
    variables:
      tenantId: $orgId
      operatorId: $accountId
      businessType: 1
      approveTemplateId: null
      refFlowTemplateId: null
      originProcessId: $processId1
      ccs: [ {
        "account": $account1,
        "accountOid": $accountId,
        "accountName": $accountName1,
        "accountNick": "",
        "accountRealName": true,
        "comment": "",
        "subjectId": $accountId,
        "subjectName": $accountName1,
        "subjectRealName": true,
        "subjectType": 0
      } ]
      files:
        [
          {
            "fileId": $fileId5,
            "fileType": 1,             #文件类型，1-合并文件 2-附件
            "fileName": $fileName5,
            "from": 2,                 #文件来自 1-模板文件 2-合同文件
            "fileSecret": false,       #文件是否保密
          }
        ]
      flowTemplateId: ""
      initiatorAccountId: $accountId
      participants:
        [
          {
            "participantSubjectType": 1,
            "role": "3",
            "sealType": null,
            "signRequirements": "1,3",
            "roleSet": 1,
            "fillOrder": 0,
            "signOrder": 1,
            "participantLabel": "签署方1",
            "participantId": null,
            "instances": [
              {
                "account": $account1,
                "accountOid": $accountId,
                "accountName": $accountName1,
                "accountRealName": true,
                "comment": "",
                "subjectId": $orgId,
                "subjectName": $orgName1,
                "subjectRealName": true,
                "subjectType": 1,
                "preFillValues": null,
                "subTaskName": ""
              }
            ],
            "willTypes": [ "FACE", "CODE_SMS", "EMAIL", "SIGN_PWD" ]
          }
        ]
      scene: 1
      taskName: $taskName2
      rescindFileIds: [ $fileId3 ]
      businessRemark: "条款内容有误"
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - processId2: content.data.processId
      - flowId2: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}


- test:
    name: 查询签署流程详情
    api: api/footstone/flowDetail.yml
    variables:
      flowId: $flowId2
      authorizerIds: $orgId
      queryAccountId: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.flowDesc", "签署中" ]
    extract:
      - signfieldId1: content.data.signDocs.0.signfields.0.signfieldId
      - signfieldId2: content.data.signDocs.0.signfields.1.signfieldId
      - posBean3: content.data.signDocs.0.signfields.0.posBean
      - posBean4: content.data.signDocs.0.signfields.1.posBean
      - fileId6: content.data.signDocs.0.fileId


- test:
    name: 查询签署可用印章列表
    api: api/footstone/getSignSeals.yml
    variables:
      flowId: $flowId2
      accountId: $accountId
      batchSign: false
      signerAccountId: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - sealId1: content.data.officialSeals.0.organSeals.0.sealId
      - sealId2: content.data.officialSeals.0.legalSeals.0.sealId


- test:
    name: 获取意愿url地址
    api: api/footstone/getIdentifyUrl.yml
    variables:
      flowId: $flowId2
      accountId: $accountId
      payerAccountGid: "4d1759c54c6c4f949903c7ae231877d7"
      saleSchemaId: 18
      scope: GENERAL
      bizCtxIds: [ $flowId2 ]
      bizType: 1
      clientType: PC
      scene: 1
      signSerialId: null
      space: $accountId
      willTypeHided: null
      wukongOid: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - bizId1: content.data.bizId


- test:
    name: 发起验证码
    api: api/footstone/sendSms.yml
    variables:
      accountId: $accountId
      flowIds: [ $flowId2 ]
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - bizId1: content.data.bizId


- test:
    name: 校验验证码认证
    api: api/footstone/checkSms.yml
    variables:
      accountId: $accountId
      authCode: 123456
      bizId: $bizId1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "content.data.passed", true ]


- test:
    name: 提交签署--部分解约
    api: api/footstone/add-Update-Execute.yml
    variables:
      flowId: $flowId2
      accountId: $accountId
      addSignfields: [ ]
      async: true
      signfieldIds: [ ]
      updateSignfields: [
        {
          "sealId": $sealId1,
          "posBean": $posBean3,
          "signfieldId": $signfieldId1,
          "authorizedAccountId": $orgId,
          "signerOperatorAuthorizerId": $orgId,
          "signerOperatorId": $accountId,
          "signWillingType": 0,
          "fieldType": 0,
          "crossEnterpriseSeal": false,
          "fileId": $fileId6
        },
        {
          "sealId": $sealId2,
          "posBean": $posBean4,
          "signfieldId": $signfieldId2,
          "authorizedAccountId": $orgId,
          "signerOperatorAuthorizerId": $orgId,
          "signerOperatorId": $accountId,
          "signWillingType": 0,
          "fieldType": 0,
          "crossEnterpriseSeal": false,
          "fileId": $fileId6
        }
      ]
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}



- test:
    name: 查询签署流程详情
    api: api/footstone/flowDetail.yml
    variables:
      flowId: $flowId1
      authorizerIds: $orgId
      queryAccountId: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.flowDesc", "部分解约" ]

- test:
    name: 流程待解约合同查询接口-流程状态为已完成
    api: api/contract_manage/process_rescind/rescindFileList.yml
    variables:
      - accountId: $accountId
      - tenantId: $orgId
      - processId: $processId1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - fileId3: content.data.files.0.fileId
      - fileId4: content.data.files.1.fileId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}


- test:
    name: 生成解约流程合同文件
    api: api/contract_manage/process_rescind/generateRescindFile.yml
    variables:
      - processId: $processId1
      - accountId: $accountId
      - tenantId: $orgId
      - rescindFileIds: [ $fileId4 ]
      - rescindRemark: "条款内容有误"
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - fileId5: content.data.files.0.fileId
      - fileName5: content.data.files.0.fileName
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 直接发起--部分解约--再次发起解约
    api: api/contract_manage/startProcess.yml
    variables:
      tenantId: $orgId
      operatorId: $accountId
      businessType: 1
      approveTemplateId: null
      refFlowTemplateId: null
      originProcessId: $processId1
      ccs: [ {
        "account": $account1,
        "accountOid": $accountId,
        "accountName": $accountName1,
        "accountNick": "",
        "accountRealName": true,
        "comment": "",
        "subjectId": $accountId,
        "subjectName": $accountName1,
        "subjectRealName": true,
        "subjectType": 0
      } ]
      files:
        [
          {
            "fileId": $fileId5,
            "fileType": 1,             #文件类型，1-合并文件 2-附件
            "fileName": $fileName5,
            "from": 2,                 #文件来自 1-模板文件 2-合同文件
            "fileSecret": false,       #文件是否保密
          }
        ]
      flowTemplateId: ""
      initiatorAccountId: $accountId
      participants:
        [
          {
            "participantSubjectType": 1,
            "role": "3",
            "sealType": null,
            "signRequirements": "1,3",
            "roleSet": 1,
            "fillOrder": 0,
            "signOrder": 1,
            "participantLabel": "签署方1",
            "participantId": null,
            "instances": [
              {
                "account": $account1,
                "accountOid": $accountId,
                "accountName": $accountName1,
                "accountRealName": true,
                "comment": "",
                "subjectId": $orgId,
                "subjectName": $orgName1,
                "subjectRealName": true,
                "subjectType": 1,
                "preFillValues": null,
                "subTaskName": ""
              }
            ],
            "willTypes": [ "FACE", "CODE_SMS", "EMAIL", "SIGN_PWD" ]
          }
        ]
      scene: 1
      taskName: $taskName2
      rescindFileIds: [ $fileId3 ]
      businessRemark: "条款内容有误"
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - processId2: content.data.processId
      - flowId2: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 查询签署流程详情
    api: api/footstone/flowDetail.yml
    variables:
      flowId: $flowId2
      authorizerIds: $orgId
      queryAccountId: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.flowDesc", "签署中" ]
    extract:
      - signfieldId1: content.data.signDocs.0.signfields.0.signfieldId
      - signfieldId2: content.data.signDocs.0.signfields.1.signfieldId
      - posBean3: content.data.signDocs.0.signfields.0.posBean
      - posBean4: content.data.signDocs.0.signfields.1.posBean
      - fileId6: content.data.signDocs.0.fileId


- test:
    name: 查询签署可用印章列表
    api: api/footstone/getSignSeals.yml
    variables:
      flowId: $flowId2
      accountId: $accountId
      batchSign: false
      signerAccountId: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - sealId1: content.data.officialSeals.0.organSeals.0.sealId
      - sealId2: content.data.officialSeals.0.legalSeals.0.sealId


- test:
    name: 获取意愿url地址
    api: api/footstone/getIdentifyUrl.yml
    variables:
      flowId: $flowId2
      accountId: $accountId
      payerAccountGid: "4d1759c54c6c4f949903c7ae231877d7"
      saleSchemaId: 18
      scope: GENERAL
      bizCtxIds: [ $flowId2 ]
      bizType: 1
      clientType: PC
      scene: 1
      signSerialId: null
      space: $accountId
      willTypeHided: null
      wukongOid: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - bizId1: content.data.bizId


- test:
    name: 发起验证码
    api: api/footstone/sendSms.yml
    variables:
      accountId: $accountId
      flowIds: [ $flowId2 ]
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      - bizId1: content.data.bizId


- test:
    name: 校验验证码认证
    api: api/footstone/checkSms.yml
    variables:
      accountId: $accountId
      authCode: 123456
      bizId: $bizId1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "content.data.passed", true ]


- test:
    name: 提交签署--部分解约--再次解约
    api: api/footstone/add-Update-Execute.yml
    variables:
      flowId: $flowId2
      accountId: $accountId
      addSignfields: [ ]
      async: true
      signfieldIds: [ ]
      updateSignfields: [
        {
          "sealId": $sealId1,
          "posBean": $posBean3,
          "signfieldId": $signfieldId1,
          "authorizedAccountId": $orgId,
          "signerOperatorAuthorizerId": $orgId,
          "signerOperatorId": $accountId,
          "signWillingType": 0,
          "fieldType": 0,
          "crossEnterpriseSeal": false,
          "fileId": $fileId6
        },
        {
          "sealId": $sealId2,
          "posBean": $posBean4,
          "signfieldId": $signfieldId2,
          "authorizedAccountId": $orgId,
          "signerOperatorAuthorizerId": $orgId,
          "signerOperatorId": $accountId,
          "signWillingType": 0,
          "fieldType": 0,
          "crossEnterpriseSeal": false,
          "fileId": $fileId6
        }
      ]
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 查询签署流程详情
    api: api/footstone/flowDetail.yml
    variables:
      flowId: $flowId1
      authorizerIds: $orgId
      queryAccountId: $accountId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.flowDesc", "已解约" ]
