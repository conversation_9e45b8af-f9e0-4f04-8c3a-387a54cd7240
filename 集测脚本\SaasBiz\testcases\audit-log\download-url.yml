- config:
    name: 查询下载审计日志结果

- test:
    name: 下载审计日志
    api: api/audit-log/audit-log-download.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        "accountIds": []
        "appid": "**********"
        "endTime": *************
        "event": "直接发起"
        "firstModule": "saas_process_start"
        "innerCall": false
        "resourceId": ""
        "startTime": *************
        "tagName": ""
        "tenantId": ""
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]

    extract:
        - taskId1: content.data.taskId
- test:
    name: 查询下载审计日志结果
    api: api/audit-log/download-url.yml
    variables:
        taskId: $taskId1
        firstModule: ""
        orgId_lige: "752cbb97b722461d891f4682042a15c4 "
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]

- test:
    name: 查询下载审计日志结果
    api: api/audit-log/download-url.yml
    variables:
        taskId: $taskId1
        firstModule: "saas_contract_template"
        orgId_lige: "752cbb97b722461d891f4682042a15c4 "
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]

- test:
    name: 查询下载审计日志结果-非企业人员
    api: api/audit-log/download-url.yml
    variables:
        taskId: "59f57c3e860e4fe3993e2573ecb2e04c"
        firstModule: "saas_contract_template"
        orgId_lige: "00ccae92ea86444ca5fbadb42026c943"
    validate:
        - eq: ["content.code", 10000015]
        - contains: ["content.message", "您不是该企业成员，请联系企业管理员加入企业。"]
