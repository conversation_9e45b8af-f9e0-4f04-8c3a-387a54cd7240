- config:
    name: 查询&修改AI提取值
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      menuId1: 9e774972a1e54a88b35457254281f297
      processId1: 5da867d68ce640499c2d63ff059023e4

- test:
    name: 查询提取值
    api: api/contract-ledger/get-form-data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
      processId: $processId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fieldName1: content.data.fieldDataList.0.fieldName
      - columnName1: content.data.fieldDataList.0.columnName
      - fieldId1: content.data.fieldDataList.0.fieldId
      - fieldType1: content.data.fieldDataList.0.fieldType

- test:
    name: 修改提取值
    api: api/contract-ledger/update-form-data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fieldDataList:
        [
        {
          "fieldName":"$fieldName1",
          "data":"个人借款合同2",
          "columnName":"$columnName1",
          "fieldId":"$fieldId1",
          "fieldType":$fieldType1
        }
        ]
      menuId: $menuId1
      processId: $processId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询提取值
    api: api/contract-ledger/get-form-data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: $menuId1
      processId: $processId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.fieldDataList.0.data", 个人借款合同2]

- test:
    name: 查询企业合同列表
    api: api/offline_contract/grouping_lists.yml
    variables:
      tenantId: $orgId1
      OperatorId: $accountId1
      pageSize: 10
      pageNum: 1
      menuId: $menuId1
      matching: '[{"key":"processId","value":["$processId1"],"sort":"","isPublic":false},{"key":"processCreateTime","value":null,"sort":"","isPublic":false},{"key":"personName","value":[""],"sort":"","isPublic":false}]'
      withApproving: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.groupingProcessList.0.dynamicOutParamList.0.value", 个人借款合同2]
