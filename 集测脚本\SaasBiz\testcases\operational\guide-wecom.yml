- config:
    name: 查询引导企微账号

- test:
    name: 法人/管理员可以正常引流
    api: api/operational/guide-wecom.yml
    variables:
      tenantId: c76c2f6f136d4997aaccf30f1f767f59
      operatorid: 565a742760cc485185bbd3cfc1e47e80
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      
      
- test:
    name: 普通员工无法引流
    api: api/operational/guide-wecom.yml
    variables:
      tenantId: c76c2f6f136d4997aaccf30f1f767f59
      operatorid: b27913606bfd4f74925c36367e2cc985
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]