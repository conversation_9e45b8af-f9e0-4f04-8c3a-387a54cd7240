- config:
    name: 下载导入模版(系统异常)
    variables:
        - code: 0
        - message: 成功

- test:
    name: 下载导入模版
    api: api/info-collect/collect_data_batch_import.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d "
        operatorId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        formId : "form65011b2be4b0aae76a7432f0"
        fileKey : ${ENV(fileKey)}

    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]