- config:
    name: 流程模板分类串场景
    variables:
      orgId1: ${ENV(orgId1)}
      accountId1: ${ENV(accountId1)}
      categoryName1: 测试分类d
      orgId2: ${ENV(orgId2)}
      db_name1: contract_manager
#      sql1: "SELECT flow_template_id FROM doc_cooperation.flow_template where oid='$orgId1' and type in(1,4) and status!=2 order by update_time desc limit 1;"
#      flowTemplateId1: ${select_sql($sql1, $db_name1)}  #orgId1的流程模板
      flowTemplateId1: c46b9dd8920249cfb47c64802975d595


- test:
    name: 创建流程模板分类1
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryName: 测试分类a
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 创建流程模板分类-categoryName已存在
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryName: 测试分类a
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - eq: ["content.message", 分类名称重复]

- test:
    name: 创建流程模板分类2
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryName: 测试分类b
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 创建流程模板分类3
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryName: 测试分类c
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询流程模板分类列表
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - categoryId1: content.data.0.categoryId
      - categoryId2: content.data.1.categoryId
      - categoryId3: content.data.2.categoryId

- test:
    name: 修改流程模板分类-categoryName已存在
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
      categoryName: 测试分类b
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - eq: ["content.message", 分类名称重复]

- test:
    name: 修改流程模板分类
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
      categoryName: $categoryName1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询流程模板分类列表-验证流程模板分类名称修改成功
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.categoryName", $categoryName1]

- test:
    name: 导入流程模板-模板2
    api: api/template-manage/import_flowTemplate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
      newFlowTemplateName: ""
      replaceDocTemplates: []
      shareCode: ""
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - flowTemplateId2: content.data.flowTemplateId

- test:
    name: 批量添加流程模板到分类下-模板1和模板2加到分类1下
    api: api/template-category/batch-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
      flowTemplateIds: [$flowTemplateId1, $flowTemplateId2]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 流程模板列表-查询分类1下的模板
    api: api/template-manage/flowTemplateList.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      status: ""
      flowTemplateName: ""
      queryLabel: true
      label: ""
      containShared: true
      excludeLabels: ""
      queryUse: false
      containsDynamic: true
      categoryId: $categoryId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 2]
      - eq: ["content.data.list.0.categories.0.categoryId", $categoryId1]
      - eq: ["content.data.list.1.categories.0.categoryId", $categoryId1]

- test:
    name: 查询流程模板分类列表
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.number", 2]
      - eq: ["content.data.1.number", 0]
      - eq: ["content.data.2.number", 0]

- test:
    name: 添加流程模板到分类下-模板1添加分类2
    api: api/template-category/add-to-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId2
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    setup_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 流程模板列表-查询分类2下的模板
    api: api/template-manage/flowTemplateList.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      status: ""
      flowTemplateName: ""
      queryLabel: true
      label: ""
      containShared: true
      excludeLabels: ""
      queryUse: false
      containsDynamic: true
      categoryId: $categoryId2
    extract:
      - categories: content.data.list.0.categories
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["${getLength($categories)}", 2]

- test:
    name: 查询流程模板分类列表
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.number", 2]
      - eq: ["content.data.1.number", 1]
      - eq: ["content.data.2.number", 0]

- test:
    name: 模板编辑分类-模板2设置分类3
    api: api/template-category/add-to-categories.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId2
      categoryIds: [$categoryId3]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    setup_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 流程模板列表-查询分类3下的模板
    api: api/template-manage/flowTemplateList.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      status: ""
      flowTemplateName: ""
      queryLabel: true
      label: ""
      containShared: true
      excludeLabels: ""
      queryUse: false
      containsDynamic: true
      categoryId: $categoryId3
    extract:
      - categories: content.data.list.0.categories
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["${getLength($categories)}", 1]
      - eq: ["content.data.list.0.categories.0.categoryId", $categoryId3]

- test:
    name: 查询流程模板分类列表
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.number", 1]
      - eq: ["content.data.1.number", 1]
      - eq: ["content.data.2.number", 1]

- test:
    name: 移除分类下的模板-模板1移除分类2
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId2
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    setup_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 流程模板列表-查询分类2下的模板
    api: api/template-manage/flowTemplateList.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      status: ""
      flowTemplateName: ""
      queryLabel: true
      label: ""
      containShared: true
      excludeLabels: ""
      queryUse: false
      containsDynamic: true
      categoryId: $categoryId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.list", []]

- test:
    name: 查询流程模板分类列表
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.number", 1]
      - eq: ["content.data.1.number", 0]
      - eq: ["content.data.2.number", 1]

- test:
    name: 删除流程模板-删除模板2
    api: api/template-manage/delete_flowTemplate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    setup_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 查询流程模板分类列表
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.number", 1]
      - eq: ["content.data.1.number", 0]
      - eq: ["content.data.2.number", 0]

- test:
    name: 删除流程模板分类1
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    setup_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 流程模板列表-查询模板1
    api: api/template-manage/flowTemplateList.yml
    variables:
      sql2: "SELECT flow_template_name FROM doc_cooperation.flow_template where flow_template_id='$flowTemplateId1';"
      tenantId: $orgId1
      operatorId: $accountId1
      pageNum: 1
      pageSize: 10
      status: ""
      flowTemplateName: ${select_sql($sql2, $db_name1)}
      queryLabel: true
      label: ""
      containShared: true
      excludeLabels: ""
      queryUse: false
      containsDynamic: true
      categoryId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.categories", []]

- test:
    name: 删除流程模板分类2
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除流程模板分类3
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId3
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询流程模板分类列表
    api: api/template-category/category-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", []]
