- config:
    name: 停用审批模版（完成）


- test:
    name: 停用审批模版
    api: api/approval/template_close.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalTemplateId : ATd43aa6ad96ee4f499049b0d896812c4a
      approvalTemplateType: 2
      approvalTemplateName : 勿删-集测专用
      approvalTemplateDesc : 测试

    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


#- test:
#    name: 启用审批模版，启用成功
#    api: api/approval/template_open.yml
#    variables:
#      operatorId: 565a742760cc485185bbd3cfc1e47e80
#      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
#      approvalTemplateType: 2
#      approvalTemplateId: ATa19d880e382b4546b44e7c337a1e3e65
#
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["status_code", 200]
#      - eq: ["content.message", 成功]

