- config:
    name: 获取合同偏好设置


- test:
    name: 获取合同偏好设置
    api: api/Contract-preferences/query-preferences.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        orgId: "752cbb97b722461d891f4682042a15c4"
        preferenceKeys: "seal_use_apply_way"
#    validate:
#        - eq: ["content.code", 0]
#        - contains: ["content.message", 成功]

- test:
    name: 获取合同偏好设置-oid为空
    api: api/Contract-preferences/query-preferences.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        orgId: ""
        preferenceKeys: "seal_use_apply_way"
#    validate:
#        - eq: ["content.code", 120000004]
#        - contains: ["content.message", "参数错误: OID不能为空"]

- test:
    name: 获取合同偏好设置-非企业成员
    api: api/Contract-preferences/query-preferences.yml
    variables:
        orgId_lige: "752cbb97b722461d891f4682042a15c4"
        orgId: "752cbb97b722461d891f468042a15c4"
        preferenceKeys: "seal_use_apply_way"
#    validate:
#        - eq: ["content.code", 10000015]
#        - contains: ["content.message", "您不是该企业成员，请联系企业管理员加入企业。"]