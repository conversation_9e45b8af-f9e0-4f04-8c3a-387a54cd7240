- config:
    name: 查询审批流模版详情（完成）


- test:
    name: 查询审批流模版详情，合同审批
    api: api/approval/template_detail.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalTemplateId : AT120b2e0ec5d24a319e159f44809c9bc1
      approvalTemplateType: 2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.approvalTemplateCondition.viewRangeValues.3.0.name",贺桂金]


- test:
    name: 查询审批流模版详情，用印审批
    api: api/approval/template_detail.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      approvalTemplateId : AFT-1704426986001011904
      approvalTemplateType: 1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
