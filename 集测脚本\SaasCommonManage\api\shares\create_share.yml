name: 创建分享
variables:
  needShareCode: true
  needShareQrCode: true
  platform: ""
  appid: ${ENV(appid)}
request:
  url: ${ENV(inner_open_url)}/v1/saas-common/shares/create
  method: POST
  headers: ${gen_headers($appid, X-Tsign-Open-Operator-Id=$accountId)}
  json:
    {
      "accountId": $accountId,
      "callSorce": $callSorce,
      "menuId": $menuId,
      "needShareCode": $needShareCode,
      "needShareQrCode": $needShareQrCode,
      "platform": $platform,
      "resourceId": $resourceId,
      "resourceType": $resourceType,
      "router": $router,
      "shareEndTime": $shareEndTime,
      "shareOperateType": $shareOperateType,
      "shareTargets": $shareTargets,
      "shareType": $shareType,
      "subjectId": $subjectId
    }
