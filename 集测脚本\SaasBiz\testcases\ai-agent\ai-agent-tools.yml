- config:
    name: ai-agent工具模块
    variables:
      processId: "7f622bba0fa14fde863f3a4ff9d24e02"
      operatorId: "d7f439aee37740149318874d0f0fa82f"
      tenantId: "849b1e1fce2b4568b0aa2b709daabea4"
      appId: "7876701657"

      fileId1: "4e7853c44e1a42bebca921a30130fb6c"
      processId1: "3e2be5a907a847dcb0a0dd5161faeeed"
- test:
    name: rag初始化
    api: api/ai-agent/agent-init.yml
    variables:
      processId: $processId
      operatorId: $operatorId
      tenantId: $tenantId
      appiId: $appId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
- test:
    name: 是否能使用ai-agent的校验
    api: api/ai-agent/check-ai-agent-use.yml
    variables:
      processId: $processId
      operatorId: $operatorId
      tenantId: $tenantId
      appiId: $appId
    validate:
        - eq: [ "content.code", 0 ]
        - eq: ["content.message", 成功]
- test:
    name: 关键词搜索
    api: api/ai-agent/keyword-search.yml
    variables:
      processId: $processId1
      fileId: $fileId1
      operatorId: $operatorId
      tenantId: $tenantId
      appiId: $appId
- test:
    name: 合同审批校验
    api: api/ai-agent/check-approval.yml
    variables:
      processId: $processId
      operatorId: $operatorId
      tenantId: $tenantId
      appiId: $appId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: ["content.message", 成功]
- test:
    name: 合同拒签校验
    api: api/ai-agent/check-refuse.yml
    variables:
      processId: $processId
      operatorId: $operatorId
      tenantId: $tenantId
      appiId: $appId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: ["content.message", 成功]
- test:
    name: 获取合同信息
    api: api/ai-agent/get-process-info.yml
    variables:
      processId: $processId
      operatorId: $operatorId
      tenantId: $tenantId
      appiId: $appId
#    validate:
#      - eq: [ "content.code", 0 ]
#      - eq: ["content.message", 成功]
- test:
    name: 获取解约地址
    api: api/ai-agent/get-rescind-url.yml
    variables:
      processId: $processId
      operatorId: $operatorId
      tenantId: $tenantId
      appiId: $appId
      menuId: ""
    validate:
      - eq: [ "content.code", 0 ]
      - eq: ["content.message", 成功]
- test:
    name: 获取文件信息
    api: api/ai-agent/get-file-info.yml
    variables:
      processId: $processId
      operatorId: $operatorId
      tenantId: $tenantId
      appiId: $appId
      flowType: 1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: ["content.message", 成功]
- test:
    name: 获取重新发起地址
    api: api/ai-agent/get-restart-url.yml
    variables:
      processId: $processId
      operatorId: $operatorId
      tenantId: $tenantId
      appiId: $appId
    validate:
      - eq: [ "content.code", 0 ]
      - eq: ["content.message", 成功]


