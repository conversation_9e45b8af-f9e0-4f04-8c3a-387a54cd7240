- config:
    name: 查询最后一次体验完成台账
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      orgId2: 913ed9f6ac5742a999ca8c002775f490

- test:
    name: 查询最后一次体验完成台账-台账id不存在
    api: api/contract-ledger/toBeConfirmed-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询最后一次体验完成台账-操作人不是企业成员
    api: api/contract-ledger/toBeConfirmed-trial.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 企业成员不存在]