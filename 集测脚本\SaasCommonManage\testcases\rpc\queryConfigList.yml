- config:
    name: 查询全量配置
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 查询开的配置
    api: api/rpc/queryConfigList.yml
    variables:
      json:
        {
          "event": "",
          "firstModule": "",
          "pageNo": 1,
          "pageSize": 10,
          "riskLevel": "",
          "subscribeStatus": "open",
          "tenantGid": "707a9ad074f44d90ba0c925adbca32eb",
          "tenantOid": "349f80c577df44c7b6bb1c535b57b5a0"
        }
    validate:
      - eq: ["status_code", 200]

- test:
    name: 查询关的配置
    api: api/rpc/queryConfigList.yml
    variables:
      json:
        {
          "event": "",
          "firstModule": "",
          "pageNo": 1,
          "pageSize": 10,
          "riskLevel": "",
          "subscribeStatus": "close",
          "tenantGid": "707a9ad074f44d90ba0c925adbca32eb",
          "tenantOid": "349f80c577df44c7b6bb1c535b57b5a0"
        }
    validate:
      - eq: ["status_code", 200]

- test:
    name: 查询全量配置
    api: api/rpc/queryConfigList.yml
    variables:
      json:
        {
          "event": "",
          "firstModule": "",
          "pageNo": 1,
          "pageSize": 10,
          "riskLevel": "",
          "subscribeStatus": "close",
          "tenantGid": "707a9ad074f44d90ba0c925adbca32eb",
          "tenantOid": "349f80c577df44c7b6bb1c535b57b5a0"
        }
    validate:
      - eq: ["status_code", 200]


- test:
    name: 查询单个配置
    api: api/rpc/queryConfigList.yml
    variables:
      json:
        {
          "event": "成员退出企业",
          "firstModule": "ser_organizational_change",
          "pageNo": 1,
          "pageSize": 10,
          "riskLevel": "",
          "subscribeStatus": "",
          "tenantGid": "707a9ad074f44d90ba0c925adbca32eb",
          "tenantOid": "349f80c577df44c7b6bb1c535b57b5a0"
        }
    validate:
      - eq: ["status_code", 200]