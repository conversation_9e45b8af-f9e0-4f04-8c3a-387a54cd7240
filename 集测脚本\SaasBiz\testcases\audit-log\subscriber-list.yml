- config:
    name: 查询风险订阅配置

- test:
    name: 查询风险订阅配置
    api: api/audit-log/config-list.yml
    variables:
        json:
          {
              "riskLevel": "",
              "pageNo": 1,
              "pageSize": 10,
              "subscribeStatus": "close",
              "event": "",
              "firstModule": ""
          }
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]

- test:
    name: 查询风险订阅配置
    api: api/audit-log/config-list.yml
    variables:
        json:
          {
              "riskLevel": "",
              "pageNo": 1,
              "pageSize": 10,
              "subscribeStatus": "open",
              "event": "",
              "firstModule": ""
          }
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]

- test:
    name: 查询风险订阅配置
    api: api/audit-log/config-list.yml
    variables:
        json:
          {
              "riskLevel": "",
              "pageNo": 1,
              "pageSize": 10,
              "subscribeStatus": "",
              "event": "",
              "firstModule": ""
          }
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]


- test:
    name: 查询推送记录
    api: api/audit-log/record-detail.yml
    variables:
        recordId: 5547787442641982013
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]

- test:
    name: 查询推送记录
    api: api/audit-log/record-list.yml
    variables:
        json:
          {
              "exportType": "subscribe",
              "startTime": *************,
              "endTime": *************,
              "event": "",
              "firstModule": "",
              "pageNo": 1,
              "pageSize": 10
          }
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]

- test:
    name: 保存订阅人
    api: api/audit-log/save-subscriber.yml
    variables:
        json:
          {
            "configId": "dff7c65a02764e13ae2164236ad77157",
            "accountList": [
              {
                "accountId": "edf234774b5145ac9b438853d51a8425",
                "accountName": "测试小号鹱",
                "accountType": "person",
                "enable": null
              }
            ]
          }
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]



- test:
    name: 查询订阅人
    api: api/audit-log/subscriber-list.yml
    variables:
        configId: 13b072f5b0b34331be6ae88bc66a570f
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]