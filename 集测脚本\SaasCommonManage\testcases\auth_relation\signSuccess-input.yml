- config:
    name: AuthRelationBuildServiceImpl远程协议调用
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: flowId为空
    api: api/auth_relation/signSuccess-input.yml
    variables:
      flowId: null
    validate:
      - eq: ["content.success", false]

- test:
    name: flowId 关联已经签署的授权书
    api: api/auth_relation/signSuccess-input.yml
    variables:
      flowId: "668c3ae749134604a73b9fd241d0d293"
    validate:
      - eq: ["content.authRelationId", 305]
      - eq: ["content.authRelationLogId", 873]
