- config:
    name: 保存合同类型
    variables:
      orgId1: d667e01dfe35437cbfd753bc824a5d75
      accountId1: 74ada1674a4d441eb0120c789f1b9300

- test:
    name: 保存合同类型-categoryName为空
    api: api/contract-categories/save-contract-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryName: ""
      extractFields:
        [
        {
          "fieldDesc": "",
          "fieldName": "手机号",
          "fieldType": "NUMBER"
        }
        ]
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", 合同类型名称不能为空]

- test:
    name: 保存合同类型-fieldName为空
    api: api/contract-categories/save-contract-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryName: 合同类型1
      extractFields:
        [
        {
          "fieldDesc": "",
          "fieldName": null,
          "fieldType": "NUMBER"
        }
        ]
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 合同类型名称已存在]
