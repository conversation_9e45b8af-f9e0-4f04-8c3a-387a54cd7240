- config:
    name: 创建/修改流程模板分类
    variables:
      orgId1: ${ENV(orgId2)}                   #基础版以上的企业
      accountId1: ${ENV(accountId1)}           #orgId1和orgId2的管理员
      categoryId1: ${ENV(categoryId1)}         #orgId1下的模板分类
      accountId2: ${ENV(accountId2)}           #orgId1下的普通成员
      accountId3: ${ENV(accountId3)}           #不是orgId1下的成员
      roleId1: ${ENV(roleId1)}                 #orgId1企业下编辑模板权限的角色id
      orgId2: ${ENV(orgId3)}                   #基础版的企业
      categoryId2: ${ENV(categoryId2)}         #orgId2下的模板分类
      db_name1: contract_manager
      categoryName1: 测试分类1
      categoryName2: 测试分类2


- test:
    name: 创建流程模板分类-categoryName为空
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryName: ""
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类名称不能为空]

- test:
    name: 创建流程模板分类-categoryName包含非法字符
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryName: "@"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", "分类名称支持汉字、数字、英文字母"]

- test:
    name: 创建流程模板分类-categoryName超出最大长度5个中文
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryName: "测试下ab123"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", "每个分类最多5个字/ 10个英文字母（字母算半个字符）"]

- test:
    name: 创建流程模板分类-操作人不是企业成员
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId3
      categoryId: ""
      categoryName: "测试abc123"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", "企业成员不存在"]

- test:
    name: 创建流程模板分类-操作人无全局模板编辑权限
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      categoryId: ""
      categoryName: "测试abc123"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", "您没有模板编辑权限"]

- test:
    name: 更新成员所有信息-给accountId2设置全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 创建流程模板分类-操作人有全局模板编辑权限
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      categoryId: ""
      categoryName: $categoryName1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-取消accountId2的全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: []
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 创建流程模板分类-管理员默认有权限
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryName: $categoryName2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 创建流程模板分类-版本不支持
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      categoryId: ""
      categoryName: "测试abc123"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 版本功能不支持]

- test:
    name: 修改流程模板分类-categoryId不存在
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: 123
      categoryName: "测试abc123"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
 #     - contains: ["content.message", 分类不存在]

- test:
    name: 修改流程模板分类-categoryId不属于当前企业
    api: api/template-category/save-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId2
      categoryName: "测试abc123"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
 #     - contains: ["content.message", 分类不存在]

#清理测试数据
- test:
    name: 删除流程模板分类-删除分类1
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      sql1: "SELECT contract_category_id FROM doc_cooperation.contract_category where oid='$orgId1' and contract_category_name='$categoryName1';"
      categoryId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除流程模板分类-删除分类2
    api: api/template-category/delete-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      sql1: "SELECT contract_category_id FROM doc_cooperation.contract_category where oid='$orgId1' and contract_category_name='$categoryName2';"
      categoryId: ${select_sql($sql1, $db_name1)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
