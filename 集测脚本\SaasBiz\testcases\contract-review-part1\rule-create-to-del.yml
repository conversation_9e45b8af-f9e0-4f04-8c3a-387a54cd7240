- config:
    name: 审查规则从创建到删除
    variables:
        appId: "7876701657"
        groupId: "b807dd041732494e937c4af0965836a4"
        fileKey: ${ENV(filekey_contract_review)}

- test:
    name: 规则列表
    api: api/contract-review-part1/rule/rule-list.yml
    variables:
        appId: $appId
        groupId: $groupId
#        inventoryId: ""
    extract:
        groupList: content.data.list
        ruleId1: content.data.list.0.ruleId
    validate:
        - eq: ["content.code", 0]
        - ne: ["${getLength($groupList)}", 0]

- test:
    name: 规则示例
    api: api/contract-review-part1/rule/rule-example.yml
    variables:
        appId: $appId
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]
        - ne: ["content.data.list", null]
- test:
    name: 规则解析
    api: api/contract-review-part1/rule/rule-analysis.yml
    variables:
        groupId: $groupId
        ruleRemark: "合同中约定的产品价格有效期条款需要包含价格有效期， 且有效期不能超过一年"
        ruleId: $ruleId1
    extract:
        ruleId2: content.data.ruleId
    validate:
        - eq: [ "content.code", 0 ]
        - contains: [ "content.message", 成功 ]
        - ne: ["content.data.ruleId", null]

- test:
    name: 规则详情
    api: api/contract-review-part1/rule/rule-detail.yml
    variables:
        appId: $appId
        ruleId: $ruleId1
    validate:
        - eq: [ "content.code", 0 ]
        - contains: [ "content.message", 成功 ]
    extract:
        reviewRuleRisks: content.data.dataList.0.reviewRuleRisks
        groupId: content.data.dataList.0.groupId
#        ruleName: content.data.ruleName
        ruleRemark: content.data.dataList.0.ruleRemark
        riskLevel: content.data.dataList.0.riskLevel
    setup_hooks:
        - ${hook_sleep_n_secs(5)}
#- test:
#    name: 规则效果验证
#    api: api/contract-review-part1/rule/rule-verify.yml
#    variables:
#        appId: $appId
#        fileKey: "$06d0b30d-6582-40d4-a7f7-61a03d924fbd$1074831495"
#        groupId: $groupId
#        reviewRuleRisks: $reviewRuleRisks
#        riskLevel: $riskLevel
#        ruleId: $ruleId1
#        ruleName: "集测名称"
#        ruleRemark: $ruleRemark
#    extract:
#        ruleId3: content.data.ruleId
#- test:
#    name: 规则效果验证结果
#    api: api/contract-review-part1/rule/rule-verify-result.yml
#    variables:
#        appId: $appId
#        ruleId: $ruleId3
#    validate:
#        - eq: [ "content.code", 0 ]
#        - ne: [ "content.message", null ]
- test:
    name: 规则保存
    api: api/contract-review-part1/rule/rule-save.yml
    variables:
        appId: $appId
        groupId: $groupId
        reviewRuleRisks: $reviewRuleRisks
        riskLevel: $riskLevel
        ruleId: $ruleId2
        ruleName: "集测创建1"
        ruleRemark: $ruleRemark
        convertedStatus: "done"
    validate:
        - eq: [ "content.code", 0 ]
        - contains: [ "content.message", 成功 ]
- test:
    name: 规则列表
    api: api/contract-review-part1/rule/rule-list.yml
    variables:
        appId: $appId
        groupId: $groupId
        inventoryId: ""
    extract:
        groupList: content.data.list
    validate:
        - eq: ["content.code", 0]
        - eq: ["${getLength($groupList)}", 2]
- test:
    name: 规则复制
    api: api/contract-review-part1/rule/rule-copy.yml
    variables:
        appId: $appId
        ruleId: $ruleId2
        ruleName: "规则创建副本"
    validate:
        - eq: ["content.code", 0 ]
        - contains: ["content.message", 成功]
    extract:
        ruleId4: content.data.ruleId
- test:
    name: 规则删除确认
    api: api/contract-review-part1/rule/rule-del-confirm.yml
    variables:
        appId: $appId
        ruleId: [$ruleId2]
    validate:
        - eq: [ "content.code", 0 ]
        - contains: [ "content.message", 成功]
- test:
    name: 规则删除
    api: api/contract-review-part1/rule/rule-del.yml
    variables:
        appId: $appId
        ruleIds: [$ruleId2,$ruleId4]
    validate:
        - eq: [ "content.code", 0 ]
        - contains: [ "content.message", 成功 ]
- test:
    name: 规则列表
    api: api/contract-review-part1/rule/rule-list.yml
    variables:
        appId: $appId
        groupId: $groupId
        inventoryId: ""
    extract:
        groupList: content.data.list
    validate:
        - eq: ["content.code", 0]
        - eq: ["${getLength($groupList)}", 1]



