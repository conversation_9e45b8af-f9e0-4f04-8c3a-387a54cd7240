- config:
    name: 下载导入模版（完成）
    variables:
        - code: 0
        - message: 成功

- test:
    name: 下载导入模版
    api: api/info-collect/collect_data_dowload_import_template.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        operatorId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        formId: "form64f849d4e4b0937d65ec9b98"

    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]
        - ne: ["content.data", None]

- test:
    name: 下载导入模版,无formid
    api: api/info-collect/collect_data_dowload_import_template.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        operatorId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        formId: ""

    validate:
        - eq: ["content.code", 31300001]
        - eq: ["content.message", "formId"]
        - ne: ["content.data", None]