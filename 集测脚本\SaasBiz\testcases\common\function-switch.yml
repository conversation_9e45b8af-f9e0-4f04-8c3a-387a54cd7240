- config:
    name: 功能开关


- test:
    name: 查询流程模板分类列表-functionKey为空
    api: api/common/function-switch.yml
    variables:
      functionKey: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", false]

- test:
    name: 查询流程模板分类列表-functionKey不存在
    api: api/common/function-switch.yml
    variables:
      functionKey: 123
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", false]

- test:
    name: 查询流程模板分类列表
    api: api/common/function-switch.yml
    variables:
      functionKey: ios_purchase
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data", true]
