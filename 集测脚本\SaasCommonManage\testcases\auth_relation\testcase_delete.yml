- config:
    name: 删除授权
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      - sql: "update auth_relation set deleted=0 where id = 302"
    setup_hooks:
      - ${hook_db_data($sql, saas_base_manage)}


- test:
    name: 删除授权
    api: api/auth_relation/delete.yml
    variables:
      parentTenantGid: bf6e1096684c452e9821b40430229965
      parentTenantOid: 1380ec9f50a0425faed96360f2e95418
      childTenantGid: afb6324d4d7748148db36d0f75a9a8e1
      childTenantOid: 856c153005614d8c94470f95881d316e
    validate:
      - eq: ["content.result", true]
