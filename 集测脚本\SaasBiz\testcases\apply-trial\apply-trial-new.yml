- config:
    name: 申请试用相关场景
    variables:
      sql1: "delete from saas_addition_function where gid='cca491659aac4214877c2179c3915e7d' and func_code='set_process_secret'"
      sql2: "update apply_trial_log set `status`=0 where subject_gid='cca491659aac4214877c2179c3915e7d' and func_code='ai_hand_draw'"
      db_name: saas_base_manage

- test:
    name: 管理员申请合同保密的试用
    api: api/apply-trial/apply-trial-new.yml
    setup_hooks:
      - ${hook_db_data($sql1, $db_name)}
    variables:
      tenantId: bdc78b6acf45466289d42dd5cf3a90ec
      operatorId: 0bc26b27ede64cc48e7a800f4c5cbac7
      clientId: WEB
      json:
        {
          "functionCode": "set_process_secret"
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.tips", "开通成功"]

- test:
    name: 再次开通合同保密会报错
    api: api/apply-trial/apply-trial-new.yml
    variables:
      tenantId: bdc78b6acf45466289d42dd5cf3a90ec
      operatorId: 0bc26b27ede64cc48e7a800f4c5cbac7
      clientId: WEB
      json:
        {
          "functionCode": "set_process_secret"
        }
    validate:
      - eq: ["content.code", 70001112]
#      - eq: ["content.message", "该会员功能已在试用中"]

- test:
    name: 非管理员的角色，申请试用时会给管理员发消息
    api: api/apply-trial/apply-trial-new.yml
    setup_hooks:
      - ${hook_db_data($sql2, $db_name)}
    variables:
      tenantId: bdc78b6acf45466289d42dd5cf3a90ec
      operatorId: a25720cea1a64970889278bf7af5ef8d
      clientId: WEB
      json:
        {
          "functionCode": "ai_hand_draw"
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.tips", "发送成功"]

- test:
    name: 已经通知过管理员，再次通知会报错
    api: api/apply-trial/apply-trial-new.yml
    variables:
      tenantId: bdc78b6acf45466289d42dd5cf3a90ec
      operatorId: a25720cea1a64970889278bf7af5ef8d
      clientId: WEB
      json:
        {
          "functionCode": "ai_hand_draw"
        }
    validate:
      - eq: ["content.code", 70001113]
      - eq: ["content.message", "会员功能试用已发送,请勿重复提交"]

- test:
    name: 查看开通的落地页回显
    api: api/apply-trial/query-trial-function-apply.yml
    variables:
      tenantId: bdc78b6acf45466289d42dd5cf3a90ec
      operatorId: 0bc26b27ede64cc48e7a800f4c5cbac7
      clientId: WEB
      json:
        {
          "applicantOid": "a25720cea1a64970889278bf7af5ef8d",
          "applicantSubjectOid": "bdc78b6acf45466289d42dd5cf3a90ec",
          "functionCode": "ai_hand_draw"
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.applicationFunctionCode", "ai_hand_draw"]