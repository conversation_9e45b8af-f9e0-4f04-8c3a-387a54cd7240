- config:
    name: 法人授权相关操作
    variables:
      orgId: "23367e90691c44cf81e60a361dafbde9"  # esigntest法人授权测试企业7
      adminId: "a46d29697c50498eb9ff75655ba9bad1"  #管理员oid   阮小五
      legalId: "219461ffede84f629235751ca4967b8e "  #法人  阮小七
      effectiveTime0: "${getTimeStamp()}" #生效开始时间
      expireTime0: "${getTimeStamp()}"  #有效期截止时间
      data: ${ENV(data)}
      sql: "update seal.legal_seal_auth set auth_status=3 where org_id = '23367e90691c44cf81e60a361dafbde9' and status =1 limit 1;"
      dbname: "seal-manager"

#发起线下法人授权申请
- test:
    name: 发起线下法人授权申请
    variables:
        tenantid: $orgId
        operatorid: $adminId
        authDocFileKey: $data
        authIDFrontFileKey: $data
        authIDReverseFileKey: $data
        legalNoType: CRED_PSN_CH_IDCARD
        legalNumber: ******************
        orgId: $orgId
    api: api/seal/offline-apply-legal-auth.yml
    extract:
        - legalAuthId1: content.data.legalAuthId
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#撤销线下法人授权
- test:
    name: 撤销线下法人授权
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        legalAuthId: $legalAuthId1
    api: api/seal/revoke-offline-legal-auth.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#发起线上法人授权签署流程
- test:
    name: 发起线上法人授权签署流程
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        developerAppId: ""
        developerCallbackUrl: ""
        principal: ""
    api: api/seal/online-sign-legal-auth.yml
    extract:
        - legalAuthId2: content.data.legalAuthId
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#线上法人授权申请催签
- test:
    name: 线上法人授权申请催签
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
    api: api/seal/urge-legal-auth.yml
    validate:
        - contained_by: ["content.code",[0,1437137]]
        - contained_by: ["content.message", ["成功","您刚刚催办过了，发起后半小时内不能催办"]]

#取消线上法人授权申请
- test:
    name: 取消线上法人授权申请
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        legalAuthId: $legalAuthId2
    api: api/seal/cancel-online-legal-auth.yml
    validate:
        - contained_by: ["content.code",[0,1437137]]
        - contained_by: ["content.message", ["成功","您刚刚催办过了，发起后半小时内不能催办"]]

#发起线上法人授权签署流程造数
- test:
    name: 发起线上法人授权签署流程2
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        developerAppId: ""
        developerCallbackUrl: ""
        principal: ""
    api: api/seal/online-sign-legal-auth.yml
    extract:
        - legalAuthId2: content.data.legalAuthId
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#重新申请法人授权
- test:
    name: 重新申请法人授权
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
    api: api/seal/recover-legal-auth.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]
    setup_hooks:
        - ${update_db_data($sql, $dbname)}