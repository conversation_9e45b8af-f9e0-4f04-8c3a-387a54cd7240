- config:
    name: 台账串场景-模板填写控件提取
    variables:
      orgId1: 67e1edfc41be46dfa824c8af811ca844
      accountId1: ${ENV(mx_accountId)}
      templateId1: 0c00b681ce7743b7b671bfb22b42d952
      templateName1: 集测脚本专用模板
      fieldId1: 8f8cb5c240ae45858b74d29ce58ee97e
      fieldName1: 合同名称
      fieldName2: 单行文本-${getTimeStamp_ms()}
      formName1: 模板-${getTimeStamp_ms()}
      db_name: contract_analysis
      orgGid1: 693110cc535a4212a99c1ef79f0b6e1a

- test:
    name: 拉取填写控件自动匹配
    api: api/contract-ledger/match-template-struct.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      templateIds: [$templateId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId1: content.data.0.fileStructMappingList.0.fileId
      - structId1: content.data.0.fileStructMappingList.0.fieldStructMapping.0.struct.structId
      - structName1: content.data.0.fileStructMappingList.0.fieldStructMapping.0.struct.structName

- test:
    name: 保存台账-提取方式选择模板填写控件提取
    api: api/contract-ledger/save-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: []
      conditions: []
      extractType: 1
      fieldIds: []
      formName: $formName1
      ledgerTemplateMapping:
        [
        {
          "templateId":"$templateId1",
          "templateName":"$templateName1",
          "templateType":1,
          "fileStructMappingList":[
          {
            "fileId":"$fileId1",
            "fieldStructMapping":[
            {
              "field":"$fieldId1",
              "fieldName":"$fieldName1",
              "struct":{
                "structId":"$structId1",
                "structName":"$structName1",
                "templateStructType":1
              }
            }
            ]
          }
          ]
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - formId1: content.data

- test:
    name: 台账列表
    api: api/contract-ledger/form-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryIds: []
      extractType: 1
      fomName: ""
      matching: $formId1
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.templates.0.templateId", $templateId1]

- test:
    name: 运行台账
    api: api/contract-ledger/rerun-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取台账进度
    api: api/contract-ledger/get-form-progress.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ge: ["content.data.completed", 0]

- test:
    name: 停止台账运行
    api: api/contract-ledger/stop-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - contained_by: ["content.code", [0,*********]]
      - contained_by: ["content.message", ["成功","参数错误: 已经运行完毕，无法停止，您可以修改设置重新运行"]]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 查询台账
    api: api/contract-ledger/get-form-detail.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.templates.0.templateId", $templateId1]
      - eq: ["content.data.fields.0.fieldId", $fieldId1]

- test:
    name: 修改台账-修改控件对应的提取字段
    api: api/contract-ledger/update-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: []
      conditions: []
      extractType: 1
      fieldIds: []
      formId: $formId1
      formName: $formName1
      ledgerTemplateMapping:
        [
        {
          "templateId":"$templateId1",
          "templateName":"$templateName1",
          "templateType":1,
          "fileStructMappingList":[
          {
            "fileId":"$fileId1",
            "fieldStructMapping":[
            {
              "field":"",
              "fieldName":"$fieldName2",
              "struct":{
                "structId":"$structId1",
                "structName":"$structName1",
                "templateStructType":1
              }
            }
            ]
          }
          ]
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询台账
    api: api/contract-ledger/get-form-detail.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.fields.0.fieldId", $fieldId1]
    extract:
      - fieldId2: content.data.fields.0.fieldId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 关闭台账
    api: api/contract-ledger/close-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除台账
    api: api/contract-ledger/delete-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      sql1: "SELECT form_id FROM contract_analysis.form where tenant_gid='$orgGid1' and form_name='$formName1' limit 1;"
      formId: ${select_sql($sql1, $db_name)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除字段
    api: api/contract-ledger/delete-field.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      sql1: "SELECT field_id FROM contract_analysis.field where tenant_gid='$orgGid1' and field_name='$fieldName2' limit 1;"
      fieldId: ${select_sql($sql1, $db_name)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["status_code", 200]
