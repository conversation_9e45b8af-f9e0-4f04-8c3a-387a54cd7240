config:
  name: 信息采集-主流程外的相关功能
  variables:
    orgId1: 08486d61823d4c2086122b26fb3e615a
    accountId1: 0bc26b27ede64cc48e7a800f4c5cbac7
    formId_1: "form658c212ce4b0b7a4d8a8898a"
    infoCollectRecordOuterId_1: "1740214241116557312"
teststeps:
  - name: 获取复制登记表凭证
    api: api/info_collect/getFormCopyKey.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
      json: {
        "formId": $formId
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "status_code", 200 ]
    extract:
      - copyKey_1: content.data.copyKey

  - name: 导入登记表
    api: api/info_collect/form_copy.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      copyKey: $copyKey_1
      json: {
        "copyKey": $copyKey
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "status_code", 200 ]
    extract:
      - formId_2: content.data.formId


  - name: 删除登记表
    api: api/info_collect/form_delete.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_2
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "content.data", null ]


  - name: 更新登记表
    api: api/info_collect/form_update.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_2
      json: {
        "formId": $formId,
        "formJson": "{\"config\":{\"customClass\":\"\",\"hideErrorMessage\":false,\"hideLabel\":false,\"labelPosition\":\"top\",\"labelWidth\":100,\"layout\":\"horizontal\",\"size\":\"default\",\"ui\":\"element\",\"width\":\"100%\",\"version\":\"2.1\",\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]},\"list\":[{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"33.33%\",\"widthType\":\"1/3\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"企业名称\",\"key\":\"p1ufgvhl\",\"model\":\"input_p1ufgvhl\",\"alias\":\"input_p1ufgvhl\",\"rules\":[],\"row\":0,\"col\":0},{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"33.33%\",\"widthType\":\"1/3\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"委托人姓名\",\"key\":\"l7slmses\",\"model\":\"input_l7slmses\",\"alias\":\"input_l7slmses\",\"rules\":[],\"row\":0,\"col\":1},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"33.33%\",\"widthType\":\"1/3\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"委托人手机号\",\"key\":\"zpeeo51s\",\"model\":\"cellphone_zpeeo51s\",\"alias\":\"cellphone_zpeeo51s\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":0,\"col\":2},{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"50%\",\"widthType\":\"1/2\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"被委托人姓名\",\"key\":\"9m2owx6o\",\"model\":\"input_9m2owx6o\",\"alias\":\"input_9m2owx6o\",\"rules\":[],\"row\":1,\"col\":0},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"50%\",\"widthType\":\"1/2\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"被委托人手机号\",\"key\":\"50pcr3os\",\"model\":\"cellphone_50pcr3os\",\"alias\":\"cellphone_50pcr3os\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":1,\"col\":1},{\"type\":\"date\",\"showNameText\":\"日期\",\"availableInTable\":true,\"options\":{\"customClass\":\"\",\"dataBind\":true,\"dateType\":\"date\",\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"editable\":true,\"eventConfigEnabled\":true,\"format\":\"yyyy-MM-dd\",\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"placeholder\":\"请选择\",\"placeholderText\":\"输入提示\",\"readonly\":false,\"required\":false,\"requiredMessage\":\"\",\"supportFileName\":true,\"timestamp\":true,\"tips\":\"\",\"type\":\"date\",\"validator\":\"\",\"validatorCheck\":false,\"validateFormatValue\":\"\",\"valueFormat\":\"yyyy-MM-dd HH:mm:ss\",\"width\":\"100%\",\"widthType\":\"1\",\"validateFormat\":[{\"value\":\"yyyy\",\"label\":\"年\"},{\"value\":\"yyyy-Q\",\"label\":\"年-季度\"},{\"value\":\"yyyy-MM\",\"label\":\"年-月\"},{\"value\":\"yyyy-MM-dd\",\"label\":\"年月日\"},{\"value\":\"yyyy-MM-dd HH:mm\",\"label\":\"年月日-时分\"},{\"value\":\"yyyy-MM-dd HH:mm:ss\",\"label\":\"年月日-时分秒\"}],\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"编辑了日期\",\"key\":\"i0crkwqn\",\"model\":\"date_i0crkwqn\",\"alias\":\"date_i0crkwqn\",\"rules\":[],\"row\":2,\"col\":0}],\"remoteComponents\":{\"matter-form_input\":\"https://asset.esign.cn/lowcode/test/components/matter-form/input/1.0.58/input.umd.js\",\"matter-form_cellphone\":\"https://asset.esign.cn/lowcode/test/components/matter-form/cellphone/1.0.59/cellphone.umd.js\",\"matter-form_date\":\"https://asset.esign.cn/lowcode/test/components/matter-form/date/1.0.58/date.umd.js\"}}",
        "name": "测试信息采集",
        "operations": [ {
          "code": "lc_eform_collection_list",
          "label": "登记信息表",
          "type": "SYSTEM",
          "key": "collection_list"
        }, {
          "code": "lc_eform_collection_add",
          "label": "新建登记表",
          "type": "SYSTEM",
          "key": "collection_add"
        }, {
          "code": "lc_eform_collection_task_add",
          "label": "新建采集任务",
          "type": "SYSTEM",
          "key": "collection_task_add"
        }, {
          "code": "lc_eform_collection_template_relation",
          "label": "关联业务模版",
          "type": "CUSTOM",
          "key": "collection_template_relation"
        }, {
          "code": "lc_eform_collection_edit",
          "label": "编辑登记表",
          "type": "SYSTEM",
          "key": "collection_edit"
        }, {
          "code": "lc_eform_collection_view",
          "label": "查看登记表",
          "type": "SYSTEM",
          "key": "collection_view"
        }, {
          "code": "lc_eform_collection_delete",
          "label": "删除登记表",
          "type": "SYSTEM",
          "key": "collection_delete"
        }, {
          "code": "lc_eform_collection_task_start_list",
          "label": "我发起的",
          "type": "SYSTEM",
          "key": "collection_task_start_list"
        }, {
          "code": "lc_eform_collection_task_manage_list",
          "label": "我管理的",
          "type": "SYSTEM",
          "key": "collection_task_manage_list"
        }, {
          "code": "lc_eform_collection_task_view",
          "label": "任务详情",
          "type": "SYSTEM",
          "key": "collection_task_view"
        }, {
          "code": "lc_eform_collection_task_enable",
          "label": "启用",
          "type": "SYSTEM",
          "key": "collection_task_enable"
        }, {
          "code": "lc_eform_collection_task_disable",
          "label": "停用",
          "type": "SYSTEM",
          "key": "collection_task_disable"
        }, {
          "code": "lc_eform_collection_task_delete",
          "label": "删除",
          "type": "SYSTEM",
          "key": "collection_task_delete"
        }, {
          "code": "lc_eform_collection_task_url",
          "label": "复制链接",
          "type": "SYSTEM",
          "key": "collection_task_url"
        }, {
          "code": "lc_eform_collection_data_start_list",
          "label": "我发起的",
          "type": "SYSTEM",
          "key": "collection_data_start_list"
        }, {
          "code": "lc_eform_collection_data_manage_list",
          "label": "我管理的",
          "type": "SYSTEM",
          "key": "collection_data_manage_list"
        }, {
          "code": "lc_eform_collection_data_sign",
          "label": "选择数据发起签署",
          "type": "SYSTEM",
          "key": "collection_data_sign"
        }, {
          "code": "lc_eform_collection_data_export",
          "label": "导出数据",
          "type": "SYSTEM",
          "key": "collection_data_export"
        }, {
          "code": "lc_eform_collection_data_download",
          "label": "下载",
          "type": "SYSTEM",
          "key": "collection_data_download"
        }, {
          "code": "lc_eform_collection_data_view",
          "label": "查看",
          "type": "SYSTEM",
          "key": "collection_data_view"
        }, {
          "code": "lc_eform_collection_data_approve",
          "label": "审核",
          "type": "SYSTEM",
          "key": "collection_data_approve"
        }, {
          "code": "lc_eform_collection_data_delete",
          "label": "删除",
          "type": "SYSTEM",
          "key": "collection_data_delete"
        }, {
          "code": "lc_eform_collection_data_edit",
          "label": "编辑",
          "type": "SYSTEM",
          "key": "collection_data_edit"
        } ],
        "pageConfig": {
          "fields": [ {
            "fieldName": "id",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "data_id_",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": false,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": true,
            "fieldType": "input",
            "order": 0
          }, {
            "fieldName": "企业名称",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "input_p1ufgvhl",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": true,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": true,
            "fieldType": "input",
            "order": 1
          }, {
            "fieldName": "委托人姓名",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "input_l7slmses",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": true,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": true,
            "fieldType": "input",
            "order": 2
          }, {
            "fieldName": "委托人手机号",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "cellphone_zpeeo51s",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": true,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": true,
            "fieldType": "cellphone",
            "order": 3
          }, {
            "fieldName": "被委托人姓名",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "input_9m2owx6o",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": true,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": true,
            "fieldType": "input",
            "order": 4
          }, {
            "fieldName": "被委托人手机号",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "cellphone_50pcr3os",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": true,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": true,
            "fieldType": "cellphone",
            "order": 5
          }, {
            "fieldName": "编辑了日期",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "date_i0crkwqn",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": true,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": true,
            "fieldType": "date",
            "order": 6
          }, {
            "fieldName": "状态",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "status_",
            "multiFuzzySelect": false,
            "isSelect": true,
            "isShow": true,
            "detailPageShow": false,
            "multiFuzzySelectEnable": false,
            "options": [ {
              "label": "待审核",
              "value": "WAIT_AUDIT"
            }, {
              "label": "审核通过",
              "value": "AUDIT_PASS"
            }, {
              "label": "审核驳回",
              "value": "AUDIT_REJECT"
            }, {
              "label": "无需审核",
              "value": "UN_AUDIT"
            } ],
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": true,
            "fieldType": "select",
            "order": 7
          }, {
            "fieldName": "创建人",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "creator_",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": false,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": false,
            "fieldType": "user",
            "order": 8
          }, {
            "fieldName": "修改人",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "modifier_",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": false,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": false,
            "fieldType": "user",
            "order": 9
          }, {
            "fieldName": "创建时间",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "create_time_",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": false,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": true,
            "fieldType": "date",
            "order": 10
          }, {
            "fieldName": "更新时间",
            "childFields": [ ],
            "editPageStatus": 1,
            "fieldKey": "modify_time_",
            "multiFuzzySelect": false,
            "isSelect": false,
            "isShow": true,
            "detailPageShow": false,
            "multiFuzzySelectEnable": false,
            "detailPageStatus": 2,
            "listPageShow": true,
            "selectEnable": true,
            "fieldType": "date",
            "order": 11
          } ]
        }
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "content.data", null ]

  - name: 导出采集数据
    api: api/info_collect/data_export.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
      infoCollectRecordOuterId: $infoCollectRecordOuterId_1
      json: {
        "formId": $formId,
        "infoCollectRecordOuterIds": [$infoCollectRecordOuterId]
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "status_code", 200 ]

  - name: 设置采集信息表头
    api: api/info_collect/table_header_update.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
      json: {
        "formId": $formId,
        "infoCollectTaskId": "0",
        "displayList": [ {
          "fieldKey": "data_id_",
          "fieldName": "id",
          "fieldType": "input",
          "fieldWidth": 150,
          "lock": false
        }, {
          "fieldKey": "input_p1ufgvhl",
          "fieldName": "企业名称",
          "fieldType": "input",
          "fieldWidth": 150,
          "lock": false
        }, {
          "fieldKey": "input_l7slmses",
          "fieldName": "委托人姓名",
          "fieldType": "input",
          "fieldWidth": 150,
          "lock": false
        }, {
          "fieldKey": "cellphone_zpeeo51s",
          "fieldName": "委托人手机号",
          "fieldType": "cellphone",
          "fieldWidth": 150,
          "lock": false
        }, {
          "fieldKey": "input_9m2owx6o",
          "fieldName": "被委托人姓名",
          "fieldType": "input",
          "fieldWidth": 150,
          "lock": false
        }, {
          "fieldKey": "cellphone_50pcr3os",
          "fieldName": "被委托人手机号",
          "fieldType": "cellphone",
          "fieldWidth": 150,
          "lock": false
        } ]
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "content.data", null ]

  - name: 更新采集数据
    api: api/info_collect/data_update.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId_1
      infoCollectRecordOuterId: $infoCollectRecordOuterId_1
      json: {
        "infoCollectTaskId": 925,
        "detail": {
          "input_p1ufgvhl": "esigntest自动化测试企业11测试吴四",
          "input_l7slmses": "测试吴四",
          "cellphone_zpeeo51s": "***********",
          "input_9m2owx6o": "测试吴四",
          "cellphone_50pcr3os": "***********",
          "date_i0crkwqn": *************
        },
        "infoCollectRecordOuterId": $infoCollectRecordOuterId,
        "formId": $formId
      }
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", null ]
      - eq: [ "content.data", null ]

