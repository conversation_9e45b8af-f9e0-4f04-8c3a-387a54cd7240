- config:
    name: 批量开通会员版本
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      orgGid1: ${ENV(orgGid4)}
      orgId1: ${ENV(orgId4)}
      accountId1: ${ENV(accountId1)}
      db_name1: saas-common-manage


- test:
    name: 批量开通会员版本失败-开始时间为空
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr:
      expireDateEndStr: ${todayEnd_getTimeStamp_ms()}
      gidList: [$orgGid1]
      vipLevel: 2
      vipCode: SENIOR
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 版本开始日期不能为空]

- test:
    name: 批量开通会员版本失败-结束时间为空
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr: ${today_getTimeStamp_ms()}
      expireDateEndStr:
      gidList: [$orgGid1]
      vipLevel: 2
      vipCode: SENIOR
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 版本结束日期不能为空]

- test:
    name: 批量开通会员版本失败-gid为空
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr: ${today_getTimeStamp_ms()}
      expireDateEndStr: ${todayEnd_getTimeStamp_ms()}
      gidList: []
      vipLevel: 2
      vipCode: SENIOR
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", GID列表不能为空]

- test:
    name: 批量开通会员版本失败-版本错误
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr: ${today_getTimeStamp_ms()}
      expireDateEndStr: ${todayEnd_getTimeStamp_ms()}
      gidList: [$orgGid1]
      vipLevel: 0
      vipCode: TEST
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 版本错误]

- test:
    name: 批量开通会员版本失败-开始时间早于结束时间
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr: ${todayEnd_getTimeStamp_ms()}
      expireDateEndStr: ${today_getTimeStamp_ms()}
      gidList: [$orgGid1]
      vipLevel: 2
      vipCode: SENIOR
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 版本开始日期必须早于结束时间]

- test:
    name: 批量开通会员版本失败-结束时间小于当前时间
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr: ${today_getTimeStamp_ms()}
      expireDateEndStr: ${today_getTimeStamp_ms()}
      gidList: [$orgGid1]
      vipLevel: 2
      vipCode: SENIOR
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 版本结束日期必须晚于当前时间]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 获取用户会员及会员功能列表-基础版
    api: api/vipmanage/queryVip.yml
    variables:
      operatorId: $accountId1
      userId: $orgId1
      withFuncs: false
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.vipCode", TRIAL]
      - eq: ["content.data.level", 0]
    extract:
      - useVipCode: content.data.useVipCode

- test:
    skipUnless: $useVipCode   #根据useVipCode判断走新版还是老版
    name: 批量开通会员版本成功-走新版-专业版
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr: ${today_getTimeStamp_ms()}
      expireDateEndStr: ${todayEnd_getTimeStamp_ms()}
      gidList: [$orgGid1]
      vipLevel:
      vipCode: PROFESSIONAL
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
      - eq: ["content.data", true]

- test:
    skipIf: $useVipCode
    name: 批量开通会员版本成功-走老版-专业版
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr: ${today_getTimeStamp_ms()}
      expireDateEndStr: ${todayEnd_getTimeStamp_ms()}
      gidList: [$orgGid1]
      vipLevel: 6
      vipCode:
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
      - eq: ["content.data", true]

- test:
    name: 查询会员当前版本-专业版
    api: api/vipmanage/queryCurrentVip.yml
    variables:
      userId: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.vipCode", PROFESSIONAL]
      - eq: ["content.data.level",6]

- test:
    name: 获取用户会员及会员功能列表-专业版
    api: api/vipmanage/queryVip.yml
    variables:
      operatorId: $accountId1
      userId: $orgId1
      withFuncs: false
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.vipCode", PROFESSIONAL]
      - eq: ["content.data.level",6]
    extract:
      - useVipCode: content.data.useVipCode

- test:
    skipUnless: $useVipCode   #根据useVipCode判断走新版还是老版
    name: 批量开通会员版本成功-走新版-高级版
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr: ${today_getTimeStamp_ms()}
      expireDateEndStr: ${todayEnd_getTimeStamp_ms()}
      gidList: [$orgGid1]
      vipLevel:
      vipCode: SENIOR
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
      - eq: ["content.data", true]

- test:
    skipIf: $useVipCode
    name: 批量开通会员版本成功-走老版-高级版
    api: api/vipmanage/batchOpenVip.yml
    variables:
      expireDateStartStr: ${today_getTimeStamp_ms()}
      expireDateEndStr: ${todayEnd_getTimeStamp_ms()}
      gidList: [$orgGid1]
      vipLevel: 2
      vipCode:
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
      - eq: ["content.data", true]

- test:
    name: 查询会员当前版本-高级版
    api: api/vipmanage/queryCurrentVip.yml
    variables:
      userId: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.vipCode", SENIOR]
      - contained_by: ["content.data.level", [2,3]]

- test:
    name: 获取用户会员及会员功能列表
    api: api/vipmanage/queryVip.yml
    variables:
      operatorId: $accountId1
      userId: $orgId1
      withFuncs: false
      sql1: "delete from saas_base_manage.saas_vip_account where gid='$orgGid1';"
#      key_old: "saas-common-manage:saasAccountVipCache:870ad4491b044d3b8f531e4edbd1f3ea"
      key_new: "saas-common-manage:saasAccountVipLevelCache_new:870ad4491b044d3b8f531e4edbd1f3ea"
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.vipCode", SENIOR]
      - contained_by: ["content.data.level", [2,3]]
    teardown_hooks:
      - ${hook_db_data($sql1, $db_name1)}   #删除数据库数据
#      - ${redis_val($key_old)}              #清除老版会员缓存
      - ${redis_val($key_new)}              #清除新版会员缓存
