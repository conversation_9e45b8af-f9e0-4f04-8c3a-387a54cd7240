- config:
    name: 可选合同状态
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      accountId2: 475955db49aa4289a8cb9422e200988c

- test:
    name: 可选合同状态-非企业成员
    api: api/auto-archive/get-optional-status.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 企业成员不存在]

- test:
    name: 保存用户合同偏好设置-归档合同类型设为已完成合同
    api: api/contract-manager/savePreferences.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      preferences:
        [
        {
          "preferenceKey": "process_archive_range",
          "preferenceValue": "FINISH"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 可选合同状态-企业合同偏好设置的归档合同类型为已完成合同
    api: api/auto-archive/get-optional-status.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    extract:
      processStatusList1: content.data.processStatusList
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["${getValue($processStatusList1, status, 4, flag)}", false]  #已撤回
      - eq: ["${getValue($processStatusList1, status, 8, flag)}", true]   #已完成

- test:
    name: 保存用户合同偏好设置-归档合同类型设为全部合同
    api: api/contract-manager/savePreferences.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      preferences:
        [
        {
          "preferenceKey": "process_archive_range",
          "preferenceValue": "ALL"
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 可选合同状态-企业合同偏好设置的归档合同类型为全部合同
    api: api/auto-archive/get-optional-status.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    extract:
      processStatusList2: content.data.processStatusList
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["${getValue($processStatusList2, status, 4, flag)}", true]  #已撤回
      - eq: ["${getValue($processStatusList2, status, 8, flag)}", true]   #已完成