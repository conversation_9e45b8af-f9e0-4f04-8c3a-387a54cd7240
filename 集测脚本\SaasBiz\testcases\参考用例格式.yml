- config:
    name: 搜索关键字
    variables:
      appId: ${ENV(appid1)}
      fileId1: 1eb4f0c926a240b781d862dd839bac6b


- test:
    name: 搜索关键字-fileId不存在
    api: api/files/keyword-positions.yml
    variables:
    
      json:
        { 
          "fileId": "123",  
          "keywords": ["盖章"]
          "appId": $appId
        }
    validate:
      - eq: ["content.code", 1451001]
      - eq: ["content.message", "查询文档信息失败,合同不存在"]

