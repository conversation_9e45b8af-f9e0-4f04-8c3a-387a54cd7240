name: 保存资源归属
request:
  url: ${ENV(saas_common_manage_url)}/batchSaveResourceBelong/input
  method: POST
  headers:
    Content-Type: application/json
  json:
    {
      "resourceType": $resourceType,
      "dataList": [
      {
        "deptPath": [$deptPath],
        "directDeptId": $directDeptId,
        "reason": $reason,
#        "type": $type,
        "resourceId" :$resourceId,
        "subjectGid": $subjectGid,
        "subjectOid":$subjectOid,
      }
      ]
    }
