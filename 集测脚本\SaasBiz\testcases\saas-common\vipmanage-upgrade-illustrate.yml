- config:
    name: 获取升级引导信息
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}

- test:
    name: 企业下获取升级引导信息
    api: api/saas-common/vipmanage-upgrade-illustrate.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.qrCodeFileUrl", null]
      - ne: ["content.data.qrCodeType", null]

- test:
    name: 个人下获取升级引导信息
    api: api/saas-common/vipmanage-upgrade-illustrate.yml
    variables:
      tenantId: $accountId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.qrCodeFileUrl", null]
      - ne: ["content.data.qrCodeType", null]