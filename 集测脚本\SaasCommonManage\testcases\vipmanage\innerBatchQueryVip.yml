- config:
    name: 批量查询会员版本列表-运营支撑平台使用
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      orgGid1: ${ENV(orgGid1)}
      orgGid2: ${ENV(orgGid4)}


- test:
    name: 批量查询会员版本列表-gidList为空
    api: api/vipmanage/innerBatchQueryVip.yml
    variables:
      gidList: []
    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message", GID列表不能为空]

- test:
    name: 批量查询会员版本列表-gid不存在
    api: api/vipmanage/innerBatchQueryVip.yml
    variables:
      gidList: ["123"]
    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message",  gid不存在]

- test:
    name: 批量查询会员版本列表-gid有重复
    api: api/vipmanage/innerBatchQueryVip.yml
    variables:
      gidList: [$orgGid1,$orgGid1]
    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message", gid重复]

- test:
    name: 批量查询会员版本列表-成功
    api: api/vipmanage/innerBatchQueryVip.yml
    variables:
      gidList: [$orgGid1,$orgGid2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.gid", $orgGid1]
      - eq: ["content.data.1.gid", $orgGid2]
