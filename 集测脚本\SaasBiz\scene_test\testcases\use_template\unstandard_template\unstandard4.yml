- config:
    name: 非标模板+参与人二要素不匹配
    variables:
      tenantId_1: ${ENV(mx_orgId1)}                       # 当前空间主体
      operatorId_1: ${ENV(ls_oid)}                        # 操作人oid
      operatorId_2: ${ENV(mx_accountId1)}
      initiatorAccountId: ${ENV(ls_oid)}                  # 发起人的oid
      account_1: ${ENV(ls_account)}                       # 参与人手机号
      account_2: ${ENV(mx_account2)}
      accountOid_1: ${ENV(ls_oid)}                        # 参与人oid
      accountOid_2: ${ENV(mx_accountId2)}
      accountName_1: 戚继光                               # 参与人姓名
      accountName_2: ${ENV(mx_accountName2)}
      subject_1: ${ENV(mx_orgId1)}                        # 参与人主体oid
      subject_2: ${ENV(mx_accountId2)}
      subjectName_1: ${ENV(mx_orgName1)}                  # 参与人的主体名称
      subjectName_2: ${ENV(mx_accountName2)}
      css_account_1: ${ENV(mx_account1)}                    # 抄送人的手机号
      css_accountOid_1: ${ENV(mx_accountId1)}               # 抄送人oid
      css_account_name_1: ${ENV(mx_accountName1)}          # 抄送人姓名
      css_subject_1: ${ENV(mx_orgId1)}                      # 抄送人主体oid
      css_subjectName_1: ${ENV(mx_orgName1)}                # 抄送人主体名称
      originalFileId1: 5db38690a35a4eca868002c7e9484b22      # 模板初始的文件id
      taskName: 非标+参与方二要素不匹配${generate_random_str(3)}
      use_flowTemplateId: ${ENV(ls_flowTemplateId_11)}                   # 流程模板ID
      approveTemplateId: ""                                         # 合同审批ID
      contentType_1: application/octet-stream
      contentMd5_1: GOA9yFhqM41rLDQCBR8LEQ==
      fileName_1: "测试的文件.docx"
      fileSize_1: 12614
      filePath_1: "data/测试的文件.docx"
      contentMd5_2: ZHO/tU7EMKVyzRIViMF+eQ==
      fileName_2: "带有下划线的文本.docx"
      fileSize_2: 13301
      filePath_2: "data/带有下划线的文本.docx"


- test:
    name: 获取流程发起的详情信息
    api: api/contract_manage/processDetail.yml
    variables:
      tenantId: $tenantId_1
      operatorId: $operatorId_1
      flowTemplateId: $use_flowTemplateId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - participantId1: content.data.participants.0.participantId
      - participantId2: content.data.participants.1.participantId

- test:
    name: 文件直传创建文件
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $tenantId_1
      accountId: $operatorId_1
      contentType: $contentType_1
      contentMd5: $contentMd5_1
      convert2Pdf: true
      fileName: $fileName_1
      fileSize: $fileSize_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId_1: content.data.fileId
      - uploadUrl_1: content.data.uploadUrl


- test:
    name: 上传文件到oss
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl_1
      filePath: $filePath_1
      contentType: $contentType_1
      contentMd5: $contentMd5_1
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 文件直传创建文件_02
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $tenantId_1
      accountId: $operatorId_1
      contentType: $contentType_1
      contentMd5: $contentMd5_2
      convert2Pdf: true
      fileName: $fileName_2
      fileSize: $fileSize_2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId_2: content.data.fileId
      - uploadUrl_2: content.data.uploadUrl


- test:
    name: 上传文件到oss_02
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl_2
      filePath: $filePath_2
      contentType: $contentType_1
      contentMd5: $contentMd5_2
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 构建非标临时模板
    api: api/template_manage/build_unstandard_temp.yml
    variables:
      tenantId: $tenantId_1
      operatorId: $operatorId_1
      flowTemplateId: $use_flowTemplateId
      json:
        {
          "approveTemplateId": $approveTemplateId,
          "refFlowTemplateId": null,
          "flowTemplateId": $use_flowTemplateId,
          "scene": 5,
          "ccs": [{
                    "account": $css_account_1,
                    "accountOid": $css_accountOid_1,
                    "accountName": $css_account_name_1,
                    "accountNick": null,
                    "accountRealName": true,
                    "comment": null,
                    "subjectId": $css_subject_1,
                    "subjectName": $css_subjectName_1,
                    "subjectRealName": true,
                    "subjectType": 1
                  }],
          "files": [         {
                               "fileId": $fileId_1,
                               "fileType": 1,
                               "fileName": $fileName_1,
                               "from": 2,
                               "fileSecret": false,
                               "supportReplace": true,
                               "originalFileId": $originalFileId1,
                               "order": 1,
                               "contractNoType": 1
                             }, {
                               "fileId": $fileId_2,
                               "fileType": 1,
                               "fileName": $$fileName_2,
                               "from": 2,
                               "fileSecret": false,
                               "supportReplace": false,
                               "originalFileId": "",
                               "order": 2,
                               "contractNoType": 2
                             }],
          "participants": [               {
                                            "participantSubjectType": 1,
                                            "role": "1,3",
                                            "sealType": null,
                                            "signRequirements": "1",
                                            "roleSet": 1,
                                            "fillOrder": 1,
                                            "signOrder": 1,
                                            "participantLabel": "签署方1",
                                            "participantId": $participantId1,
                                            "instances": [{
                                                            "account": $account_1,
                                                            "accountOid": $accountOid_1,
                                                            "accountNick": "",
                                                            "accountName": $accountName_1,
                                                            "accountRealName": true,
                                                            "comment": "",
                                                            "subjectId": $subject_1,
                                                            "subjectName": $subjectName_1,
                                                            "subjectRealName": true,
                                                            "subjectType": 1,
                                                            "preFillValues": null,
                                                            "fileValues": null,
                                                            "subTaskName": "",
                                                            "subTaskBizId": "",
                                                            "subTaskBizType": ""
                                                          }],
                                            "willTypes": [],
                                            "type": 1,
                                            "signSealType": 1,
                                            "signSeal": "",
                                            "forceReadEnd": false,
                                            "forceReadTime": ""
                                          }, {
                                            "participantSubjectType": 0,
                                            "role": "1,3",
                                            "sealType": "0,1,2",
                                            "signRequirements": null,
                                            "roleSet": 1,
                                            "fillOrder": 2,
                                            "signOrder": 1,
                                            "participantLabel": "签署方2",
                                            "participantId": $participantId2,
                                            "instances": [{
                                                            "account": $account_2,
                                                            "accountOid": $accountOid_2,
                                                            "accountNick": "",
                                                            "accountName": $accountName_2,
                                                            "accountRealName": true,
                                                            "comment": null,
                                                            "subjectId": $subject_2,
                                                            "subjectName": $subjectName_2,
                                                            "subjectRealName": true,
                                                            "subjectType": 0,
                                                            "preFillValues": null,
                                                            "fileValues": null,
                                                            "subTaskName": "",
                                                            "subTaskBizId": "",
                                                            "subTaskBizType": ""
                                                          }],
                                            "willTypes": [],
                                            "type": 1,
                                            "signSealType": 1,
                                            "signSeal": "",
                                            "forceReadEnd": false,
                                            "forceReadTime": ""
                                          }],
          "initiatorAccountId": $operatorId_1,
          "taskName": $taskName,
          "signEndTime": null,
          "fileEndTime": null,
          "signValidityConfig": {
            "validityType": 3,
            "durationYear": null,
            "durationMonth": null,
            "durationDay": null
          },
          "fileValidityConfig": {
            "validityType": 3,
            "durationYear": null,
            "durationMonth": null,
            "durationDay": null
          },
          "platform": 5,
          "skipFill": false,
          "previewType": 0,
          "visibleAccounts": null,
          "supportAddDoc": true,
          "secretType": 1,
          "repeatSign": false,
          "initiatorDeptId": "",
          "useWatermarkTemplateId": ""
        }
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 参与方(戚继光)对应的经办人账号信息不匹配]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

#- test:
#    name: 获取流程模板信息
#    api: api/footstone-doc/getFlowTemplate.yml
#    variables:
#      flowTemplateId: $temp_flowTemplateId1
#      tenantId: $tenantId_1
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#    extract:
#      - templateId1: content.data.docTemplates.0.templateId
#      - templateId2: content.data.docTemplates.1.templateId
#    #      - temp_flowTemplateId1: content.data.flowTemplateId
#    teardown_hooks:
#      - ${hook_sleep_n_secs(1)}
#
#
#- test:
#    name: 获取流程模板结构化组件
#    api: api/footstone-doc/getComponents.yml
#    variables:
#      flowTemplateId: $temp_flowTemplateId1
#      tenantId: $tenantId_1
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#    extract:
#      - cooperationerId1: content.data.cooperationerStructs.0.cooperationerId
#      - cooperationerId2: content.data.cooperationerStructs.1.cooperationerId
#      - structs1: content.data.cooperationerStructs.0.structs
#      - structs2: content.data.cooperationerStructs.1.structs
#    teardown_hooks:
#      - ${hook_sleep_n_secs(1)}
#
#
#- test:
#    name: 保存(添加/更新)模板结构化组件_第一个文件模板
#    api: api/footstone-doc/save_components.yml
#    variables:
#      templateId: $templateId1
#      tenantId: $tenantId_1
#      extract_id: "id"
#      str_structs1: ${json_to_string($structs1)}
#      str_structs2: ${json_to_string($structs2)}
#      id_1: ${extract_json_value($str_structs1, $templateId1, $extract_id, 0)}
#      id_2: ${extract_json_value($str_structs1, $templateId1, $extract_id, 1)}
#      id_3: ${extract_json_value($str_structs1, $templateId1, $extract_id, 2)}
#      id_4: ${extract_json_value($str_structs2, $templateId1, $extract_id, 0)}
#      id_5: ${extract_json_value($str_structs2, $templateId1, $extract_id, 1)}
#      id_6: ${extract_json_value($str_structs2, $templateId1, $extract_id, 2)}
#      json:
#        {
#          "structComponents":[
#          {
#            "aiKey":null,
#            "allowEdit":true,
#            "context":{
#              "defaultValue":"",
#              "ext":"{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"\",\"imgType\":1,\"fillLengthLimit\":20,\"desc\":\"\",\"usePageType\":null,\"usePageTypeGroupId\":null,\"appointPage\":null,\"order\":1}",
#              "label":"单行文本",
#              "limit":null,
#              "options":null,
#              "pos":{
#                "x":100.62,
#                "y":792.82,
#                "page":1
#              },
#              "required":true,
#              "subType":"",
#              "style":{
#                "bold":false,
#                "font":1,
#                "fontSize":12,
#                "height":15,
#                "horizontalAlignment":"LEFT",
#                "italic":false,
#                "textColor":"#000000",
#                "underLine":false,
#                "verticalAlignment":"TOP",
#                "width":160
#              },
#              "version":2
#            },
#            "groupKey":"",
#            "id": $id_1,
#            "key":null,
#            "refId": $cooperationerId1,
#            "type":1
#          },
#          {
#            "aiKey":null,
#            "allowEdit":true,
#            "context":{
#              "defaultValue":"",
#              "ext":"{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"\",\"imgType\":1,\"fillLengthLimit\":24,\"desc\":\"\",\"usePageType\":null,\"usePageTypeGroupId\":null,\"appointPage\":null,\"order\":2}",
#              "label":"多行文本",
#              "limit":null,
#              "options":null,
#              "pos":{
#                "x":112.7,
#                "y":719.57,
#                "page":1
#              },
#              "required":true,
#              "subType":"",
#              "style":{
#                "bold":false,
#                "font":1,
#                "fontSize":12,
#                "height":40,
#                "horizontalAlignment":"LEFT",
#                "italic":false,
#                "textColor":"#000000",
#                "underLine":false,
#                "verticalAlignment":"TOP",
#                "width":148,
#                "leadingRate":1
#              },
#              "version":2
#            },
#            "groupKey":"",
#            "id": $id_2,
#            "key":null,
#            "refId": $cooperationerId1,
#            "type":8
#          },
#          {
#            "aiKey":null,
#            "allowEdit":true,
#            "context":{
#              "defaultValue":"",
#              "ext":"{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"1\",\"imgType\":1,\"fillLengthLimit\":0,\"desc\":\"\",\"usePageType\":\"current\",\"usePageTypeGroupId\":\"\",\"appointPage\":null,\"order\":5}",
#              "label":"签署区",
#              "limit":null,
#              "options":null,
#              "pos":{
#                "x":88.28,
#                "y":217.69,
#                "page":1
#              },
#              "required":false,
#              "subType":"",
#              "style":{
#                "bold":false,
#                "font":1,
#                "fontSize":12,
#                "height":100,
#                "horizontalAlignment":"LEFT",
#                "italic":false,
#                "textColor":"#000000",
#                "underLine":false,
#                "verticalAlignment":"TOP",
#                "width":100
#              },
#              "version":2
#            },
#            "groupKey":"",
#            "id": $id_3,
#            "key":null,
#            "refId": $cooperationerId1,
#            "type":6
#          },
#          {
#            "aiKey":null,
#            "allowEdit":true,
#            "context":{
#              "defaultValue":"",
#              "ext":"{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"\",\"imgType\":1,\"fillLengthLimit\":18,\"desc\":\"\",\"usePageType\":null,\"usePageTypeGroupId\":null,\"appointPage\":null,\"order\":3}",
#              "label":"身份证号",
#              "limit":null,
#              "options":null,
#              "pos":{
#                "x":120.56,
#                "y":601.44,
#                "page":1
#              },
#              "required":true,
#              "subType":"",
#              "style":{
#                "bold":false,
#                "font":1,
#                "fontSize":12,
#                "height":15,
#                "horizontalAlignment":"LEFT",
#                "italic":false,
#                "textColor":"#000000",
#                "underLine":false,
#                "verticalAlignment":"TOP",
#                "width":125
#              },
#              "version":2
#            },
#            "groupKey":"",
#            "id": $id_4,
#            "key":null,
#            "refId": $cooperationerId2,
#            "type":16
#          },
#          {
#            "aiKey":null,
#            "allowEdit":true,
#            "context":{
#              "defaultValue":"",
#              "ext":"{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"\",\"imgType\":1,\"fillLengthLimit\":26,\"desc\":\"\",\"usePageType\":null,\"usePageTypeGroupId\":null,\"appointPage\":null,\"order\":4}",
#              "label":"数字",
#              "limit":null,
#              "options":null,
#              "pos":{
#                "x":106.7,
#                "y":538.87,
#                "page":1
#              },
#              "required":true,
#              "subType":"",
#              "style":{
#                "bold":false,
#                "font":1,
#                "fontSize":12,
#                "height":15,
#                "horizontalAlignment":"LEFT",
#                "italic":false,
#                "textColor":"#000000",
#                "underLine":false,
#                "verticalAlignment":"TOP",
#                "width":160
#              },
#              "version":2
#            },
#            "groupKey":"",
#            "id": $id_5,
#            "key":null,
#            "refId": $cooperationerId2,
#            "type":2
#          },
#          {
#            "aiKey":null,
#            "allowEdit":true,
#            "context":{
#              "defaultValue":"",
#              "ext":"{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"\",\"imgType\":1,\"fillLengthLimit\":0,\"desc\":\"\",\"usePageType\":\"current\",\"usePageTypeGroupId\":\"\",\"appointPage\":null,\"order\":6}",
#              "label":"签署区",
#              "limit":null,
#              "options":null,
#              "pos":{
#                "x":501.41,
#                "y":170.3,
#                "page":1
#              },
#              "required":false,
#              "subType":"",
#              "style":{
#                "bold":false,
#                "font":1,
#                "fontSize":12,
#                "height":100,
#                "horizontalAlignment":"LEFT",
#                "italic":false,
#                "textColor":"#000000",
#                "underLine":false,
#                "verticalAlignment":"TOP",
#                "width":100
#              },
#              "version":2
#            },
#            "groupKey":"",
#            "id": $id_6,
#            "key":null,
#            "refId": $cooperationerId2,
#            "type":6
#          }
#          ],
#          "supportDel":true
#        }
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#    extract:
#      - cooperationerId1_structId_1: content.data.0
#      - cooperationerId1_structId_2: content.data.1
#      - cooperationerId1_structId_3: content.data.2
#      - cooperationerId2_structId_1: content.data.3
#      - cooperationerId2_structId_2: content.data.4
#      - cooperationerId2_structId_3: content.data.5
#    teardown_hooks:
#      - ${hook_sleep_n_secs(1)}
#
#- test:
#    name: 保存(添加/更新)模板结构化组件_第二个文件模板
#    api: api/footstone-doc/save_components.yml
#    variables:
#      templateId: $templateId2
#      tenantId: $tenantId_1
#      json:
#        {
#          "structComponents": [      {
#                                       "aiKey": null,
#                                       "allowEdit": true,
#                                       "context": {
#                                         "defaultValue": "",
#                                         "ext": "{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"\",\"imgType\":1,\"fillLengthLimit\":20,\"order\":1,\"desc\":\"\"}",
#                                         "label": "单行文本",
#                                         "limit": null,
#                                         "options": null,
#                                         "pos": {
#                                           "x": 219.7,
#                                           "y": 803.15,
#                                           "page": 1
#                                         },
#                                         "required": true,
#                                         "subType": "",
#                                         "style": {
#                                           "bold": false,
#                                           "font": 1,
#                                           "fontSize": 12,
#                                           "height": 15,
#                                           "horizontalAlignment": "LEFT",
#                                           "italic": false,
#                                           "textColor": "#000000",
#                                           "underLine": false,
#                                           "verticalAlignment": "TOP",
#                                           "width": 160
#                                         },
#                                         "version": 2
#                                       },
#                                       "groupKey": "",
#                                       "id": null,
#                                       "key": null,
#                                       "refId": $cooperationerId1,
#                                       "type": 1
#                                     }, {
#                                       "aiKey": null,
#                                       "allowEdit": true,
#                                       "context": {
#                                         "defaultValue": "",
#                                         "ext": "{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"\",\"imgType\":1,\"fillLengthLimit\":24,\"order\":2,\"desc\":\"\"}",
#                                         "label": "多行文本",
#                                         "limit": null,
#                                         "options": null,
#                                         "pos": {
#                                           "x": 198.97,
#                                           "y": 711.67,
#                                           "page": 1
#                                         },
#                                         "required": true,
#                                         "subType": "",
#                                         "style": {
#                                           "bold": false,
#                                           "font": 1,
#                                           "fontSize": 12,
#                                           "height": 40,
#                                           "horizontalAlignment": "LEFT",
#                                           "italic": false,
#                                           "textColor": "#000000",
#                                           "underLine": false,
#                                           "verticalAlignment": "TOP",
#                                           "width": 148,
#                                           "leadingRate": 1
#                                         },
#                                         "version": 2
#                                       },
#                                       "groupKey": "",
#                                       "id": null,
#                                       "key": null,
#                                       "refId": $cooperationerId1,
#                                       "type": 8
#                                     }, {
#                                       "aiKey": null,
#                                       "allowEdit": true,
#                                       "context": {
#                                         "defaultValue": "",
#                                         "ext": "{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"1\",\"imgType\":1,\"fillLengthLimit\":0,\"order\":3,\"desc\":\"\",\"usePageType\":\"current\"}",
#                                         "label": "签署区",
#                                         "limit": null,
#                                         "options": null,
#                                         "pos": {
#                                           "x": 105.89,
#                                           "y": 339.61,
#                                           "page": 1
#                                         },
#                                         "required": false,
#                                         "subType": "",
#                                         "style": {
#                                           "bold": false,
#                                           "font": 1,
#                                           "fontSize": 12,
#                                           "height": 100,
#                                           "horizontalAlignment": "LEFT",
#                                           "italic": false,
#                                           "textColor": "#000000",
#                                           "underLine": false,
#                                           "verticalAlignment": "TOP",
#                                           "width": 100
#                                         },
#                                         "version": 2
#                                       },
#                                       "groupKey": "",
#                                       "id": null,
#                                       "key": null,
#                                       "refId": $cooperationerId1,
#                                       "type": 6
#                                     }, {
#                                       "aiKey": null,
#                                       "allowEdit": true,
#                                       "context": {
#                                         "defaultValue": "",
#                                         "ext": "{\"units\":\"px\",\"qiFeng\":false,\"signDatePos\":null,\"signRequirements\":\"\",\"imgType\":1,\"fillLengthLimit\":0,\"order\":4,\"desc\":\"\",\"usePageType\":\"current\"}",
#                                         "label": "签署区",
#                                         "limit": null,
#                                         "options": null,
#                                         "pos": {
#                                           "x": 512.34,
#                                           "y": 315.92,
#                                           "page": 1
#                                         },
#                                         "required": false,
#                                         "subType": "",
#                                         "style": {
#                                           "bold": false,
#                                           "font": 1,
#                                           "fontSize": 12,
#                                           "height": 100,
#                                           "horizontalAlignment": "LEFT",
#                                           "italic": false,
#                                           "textColor": "#000000",
#                                           "underLine": false,
#                                           "verticalAlignment": "TOP",
#                                           "width": 100
#                                         },
#                                         "version": 2
#                                       },
#                                       "groupKey": "",
#                                       "id": null,
#                                       "key": null,
#                                       "refId": $cooperationerId2,
#                                       "type": 6
#                                     }],
#          "supportDel": true
#        }
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#    extract:
#      - cooperationerId1_structId_4: content.data.0
#      - cooperationerId1_structId_5: content.data.1
#      - cooperationerId1_structId_6: content.data.2
#      - cooperationerId2_structId_4: content.data.3
#    teardown_hooks:
#      - ${hook_sleep_n_secs(1)}
#
#- test:
#    name: 关联拟定方与输入项组件
#    api: api/footstone-doc/bind_components.yml
#    variables:
#      flowTemplateId: $temp_flowTemplateId1
#      tenantId: $tenantId_1
#      json:
#        {
#          "clearReBind": true,
#          "cooperationerStructs": [   {
#                                        "cooperationerId": $cooperationerId1,
#                                        "structIds": [$cooperationerId1_structId_1, $cooperationerId1_structId_2, $cooperationerId1_structId_3, $cooperationerId1_structId_4, $cooperationerId1_structId_5, $cooperationerId1_structId_6]
#                                      }, {
#                                        "cooperationerId": $cooperationerId2,
#                                        "structIds": [$cooperationerId2_structId_1, $cooperationerId2_structId_2, $cooperationerId2_structId_3, $cooperationerId2_structId_4]
#                                      }]
#        }
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#    teardown_hooks:
#      - ${hook_sleep_n_secs(1)}
#
#- test:
#    name: 通过流程模板id发起
#    api: api/template_manage/startByFlowTemplate.yml
#    variables:
#      tenantId: $tenantId_1
#      operatorId: $operatorId_1
#      clientId: WEB
#      flowTemplateId: $temp_flowTemplateId1
#      initiatorAccountOid: $operatorId_1
#      businessType: 0
#      scene: 2
#      skipFill: false
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#    extract:
#      - processId1: content.data.processId
#      - cooperationId1: content.data.cooperationId
#      - realProcessId1: content.data.realProcessId
#    teardown_hooks:
#      - ${hook_sleep_n_secs(1)}