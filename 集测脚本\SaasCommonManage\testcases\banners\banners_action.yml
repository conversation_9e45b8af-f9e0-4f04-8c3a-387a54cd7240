- config:
    name: banner相关操作
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      - productCode1: mx_test
      - areaCode1: test5
      - bannerId1: 3649
      - clientVersionCode: 500
      - db_name1: saas_base_manage
      - sql1: "select click_time from saas_base_manage.banner_info where id='$bannerId1';"
      - clickTimes: ${select_sql($sql1, $db_name1)}
      - accountId1: ${ENV(accountId1)}
      - orgId1: ${ENV(org_gid1)}


- test:
    name: 查询端某个位置的banner列表
    api: api/banners/get_banners.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      clientVersionCode: $clientVersionCode
      operatorId: $accountId1
      tenantId: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.bannerList.0.bannerId", $bannerId1]


- test:
    name: 点击banner
    api: api/banners/click_banner.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      bannerId: $bannerId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}


- test:
    name: banner管理列表查询
    api: api/banners/banners_list.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      clickTimes1: ${sum($clickTimes, 1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.list.0.clickTimes", $clickTimes1]


- test:
    name: 下架指定banner
    api: api/banners/active_banner.yml
    variables:
      bannerId: $bannerId1
      active: false
      redis_key: "saas-common-manage:bannerCacheKey:mx_test:test5:276b5a8f2a964142bb1ebcf0dd82bf72"
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${redis_val($redis_key)}


- test:
    name: banner新增/更新
    api: api/banners/save_banner.yml
    variables:
      "areaCode": $areaCode1
      "productCode": $productCode1
      "bannerId": $bannerId1
      "clientVersionCode": "500"
      "conditionType1": "ge"
      conditionType: 0
      configMap: {
        "productKey":"BZQ",
        "functionKey":"start_and_fill_refrom"
      }
      hasSatisfy: true
      type: 2

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 查询端某个位置的banner列表
    api: api/banners/get_banners.yml
    variables:
      productCode: $productCode1
      areaCode: $areaCode1
      operatorId: $accountId1
      tenantId: $orgId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.bannerList", []]
    setup_hooks:
      - ${hook_sleep_n_secs(1)}


- test:
    name: 上架指定banner
    api: api/banners/active_banner.yml
    variables:
      bannerId: $bannerId1
      active: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询PC端登录页的banner
    api: api/banners/get_banners.yml
    variables:
      operatorId: $accountId1
      tenantId: $orgId1
      productCode: pc
      areaCode: login_page_chinese
    validate:
      - eq: [ content.code, 0 ]
      - eq: [ content.message, 成功 ]
