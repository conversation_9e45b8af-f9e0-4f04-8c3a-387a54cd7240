- config:
    name: 生成并查询水印模板快照
    variables:
      bizType: TEMP_WATERMARK_SNAPSHOT
      oid: 554027011f28412f8b0ecd518dbaa008
      operatorOid: 374fbae8502d4e679d91ff83cf1fa85d

- test:
    name: 生成水印模板快照
    api: api/watermark-template/generateWatermarkSnapShootInput.yml
    variables:
      bizId: ${getTimeStamp_ms()}
      bizType: $bizType
      oid: $oid
      operatorOid: $operatorOid
      watermarkTemplateId: 0c69981eb1ae4b5498077a2eac1ec1aa
    validate:
      - eq: ["status_code", 200]
    extract:
      - watermarkTemplateId1: content.watermarkSnapShootId

- test:
    name: 修改后生成水印模板快照
    api: api/watermark-template/generateWatermarkSnapShootInput.yml
    variables:
      bizId: ${getTimeStamp_ms()}
      bizType: $bizType
      oid: $oid
      operatorOid: $operatorOid
      watermarkTemplateId: $watermarkTemplateId1
    validate:
      - eq: ["status_code", 200]