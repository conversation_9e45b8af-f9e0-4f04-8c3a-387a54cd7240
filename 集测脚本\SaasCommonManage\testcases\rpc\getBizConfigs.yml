- config:
    name: 获取saasbiz信息+获取计费商品隔离信息
#    base_url: ${ENV(saas_common_manage_url)}


- test:
    name: 获取saasbiz信息
    api: api/rpc/getBizConfigs.yml
    variables:
      oid: d9ec3169a6e04d148e5a8cc08ab3c13d
      key: [
        "process_transfer",
        "seal_approval_transfer",
        "contract_approval_transfer"
      ]
    validate:
      - eq: ["status_code", 200]

- test:
    name: 获取钉签的计费商品隔离信息
    api: api/rpc/getBillIsolateInfo.yml
    variables:
      json:
        {
          "clientId": "DING_TALK",
          "gid": "e0b9cf6f7b404a03885b9d30d002b34e"
        }
    validate:
      - eq: ["status_code", 200]
      - eq: [content.billIsolate.clientId, DING_TALK]