- config:
    name: 获取会员版本列表-运营支撑平台使用
#    base_url: ${ENV(saas_common_manage_url)}


- test:
    name: 获取会员版本列表-不查会员功能
    api: api/vipmanage/vipList.yml
    variables:
      withCommodity: false
      withFuncs: false
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.vips.0.functions", []]

- test:
    name: 获取会员版本列表-查会员功能
    api: api/vipmanage/vipList.yml
    variables:
      withCommodity: false
      withFuncs: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.vips.0.functions", []]
