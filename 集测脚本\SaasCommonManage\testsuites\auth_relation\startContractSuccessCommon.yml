config:
  name: 企业关联的相关操作

testcases:
  -
    name: 企业管理授权
    testcase: testcases/auth_relation/testcase_startContractSuccessCommon.yml

  -
    name: 子企业解除授权
    testcase: testcases/auth_relation/testcase_childTenantDirectRescindAuthRelation.yml

  -
    name: 删除授权
    testcase: testcases/auth_relation/testcase_delete.yml

#多组织管理项目去掉了钉签非正式授权的功能，所以注释该case
#  -
#    name: 钉签同步增量数据到标准签
#    testcase: testcases/auth_relation/newSync-input.yml

  -
    name: 栏目相关操作
    testcase: testcases/auth_relation/signSuccess-input.yml

  - name: 获取共享流量授权开关
    testcase: testcases/auth_relation/testcase_getAuthRelationShareConfig.yml

  - name: 清空缓存的接口
    testcase: testcases/auth_relation/testcase_clearCache.yml

  - name: 关联企业授权模板下载地址获取
    testcase: testcases/auth_relation/template_downloadUrl.yml

  - name: 获取授权证明下载地址
    testcase: testcases/auth_relation/certificate_downloadUrl.yml

  - name: 关联企业新增授权记录查询
    testcase: testcases/auth_relation/auth-relation-records.yml

  - name: 查关联企业列表
    testcase: testcases/auth_relation/rpc_pageBackendAuthRelation.yml

  - name: 线下添加关联企业记录
    testcase: testcases/auth_relation/rpc_createOperatorLog.yml

  - name: 更新关联企业的配置信息
    testcase: testcases/auth_relation/updateShareConfig.yml