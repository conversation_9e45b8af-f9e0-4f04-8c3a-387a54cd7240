- config:
    name: 审查规则组从创建到删除
    variables:
        appId: "7876701657"
        groupId1: "d1c22dbd271b4f5f953af94e9f9c3346"

- test:
    name: 规则组列表
    api: api/contract-review-part1/rule-group/rule-group-list.yml
    variables:
        appId: $appId
    extract:
        groupList: content.data.groupList
    validate:
        - eq: ["content.code", 0]
        - ne: ["${getLength($groupList)}", 0]
- test:
    name: 创建规则组
    api: api/contract-review-part1/rule-group/rule-group-save.yml
    variables:
        appId: $appId
        groupName: "集测规则组"
    extract:
        groupId2: content.data.groupId
    validate:
        - eq: [ "content.code", 0 ]
        - contains: [ "content.message", 成功 ]
- test:
    name: 创建规则组-重复创建
    api: api/contract-review-part1/rule-group/rule-group-save.yml
    variables:
        appId: $appId
        groupName: "集测规则组"
    validate:
        - eq: [ "content.code", 120001603 ]
        - contains: [ "content.message", 规则组保存失败:规则组名称重复 ]
- test:
    name: 规则组列表
    api: api/contract-review-part1/rule-group/rule-group-list.yml
    variables:
        appId: $appId
    extract:
        groupList: content.data.groupList
    validate:
        - eq: ["content.code", 0]
        - ne: ["${getLength($groupList)}", 1]
- test:
    name: 规则组删除确认
    api: api/contract-review-part1/rule-group/rule-group-del-confirm.yml
    variables:
        appId: $appId
        groupId: $groupId2
    validate:
        - eq: [ "content.code", 0 ]
        - contains: [ "content.message", 成功 ]
- test:
    name: 删除规则组
    api: api/contract-review-part1/rule-group/rule-group-del.yml
    variables:
        appId: $appId
        groupId: $groupId2
    validate:
        - eq: [ "content.code", 0 ]
        - contains: [ "content.message", 成功 ]

- test:
    name: 规则组列表
    api: api/contract-review-part1/rule-group/rule-group-list.yml
    variables:
        appId: $appId
    extract:
        groupList: content.data.groupList
    validate:
        - eq: [ "content.code", 0 ]
        - ne: [ "${getLength($groupList)}", 0 ]


