- config:
    name: 生成活动链接/海报
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 生成活动链接/海报，该活动不支持生成海报
    api: api/activities/activitiesgenQrCode.yml
    variables:
      activityCode: '测试测试'
      shareType: ''
      activityShareId : ''
      withoutPoster: 'false'
      userId: ''
      userName: ''
    validate:
      - eq: ["content.code", 70000000]
      - eq: ["content.message", '获取用户信息失败， 请重新登录']



- test:
    name: 生成活动链接/海报
    api: api/activities/activitiesgenQrCode.yml
    variables:
      activityCode: '测试测试'
      activityShareId : ''
      shareType: 1
      withoutPoster: true
      userId: 'chennan'
      userName: '辰南'
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", '成功']
