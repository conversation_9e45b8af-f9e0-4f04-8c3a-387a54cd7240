- config:
    name: 获取账户资产/已购商品卡片信息
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}

- test:
    name: 企业下获取账户资产/已购商品卡片信息
    api: api/saas-common/bills-assets-unified-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      json: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.assets.0.type", "WALLET"]  #第一个返回账户可用余额
      - ne: ["content.data.assets.0.assetName", null]
      - ne: ["content.data.assets.0.assetDesc", null]
      - ne: ["content.data.assets.0.actions", []]
      - eq: ["content.data.assets.1.productId", 64]   #第二个返回电子签名流量
      - ne: ["content.data.buyAssets", []]   #增值服务不为空

- test:
    name: 个人下获取账户资产/已购商品卡片信息
    api: api/saas-common/bills-assets-unified-list.yml
    variables:
      tenantId: $accountId1
      operatorId: $accountId1
      json: {}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.assets.0.type", "WALLET"]  #第一个返回账户可用余额
      - ne: ["content.data.assets.0.assetName", null]
      - ne: ["content.data.assets.0.assetDesc", null]
      - ne: ["content.data.assets.0.actions", []]
      - eq: ["content.data.assets.1.productId", 64]   #第二个返回电子签名流量
      - ne: ["content.data.buyAssets", []]   #增值服务不为空