 #发起线下法人授权申请
    variables:
        appid: ${ENV(appid)}
    request:
        url: ${ENV(inner_open_url)}/v1/saas-common/organizations/seals/offline-apply-legal-auth
        method: POST
        headers: ${gen_headers($appid, X-Tsign-Open-Tenant-Id=$tenantid, X-Tsign-Open-Operator-Id=$operatorid)}
        json:
          {
            "authDocFileKey": $authDocFileKey,
            "authIDFrontFileKey": $authIDFrontFileKey,
            "authIDReverseFileKey": $authIDReverseFileKey,
            "legalNoType": $legalNoType,
            "legalNumber": $legalNumber,
            "orgId": $orgId
          }
