name: 添加或更新签署区并执行签署
variables:
  appId: ${ENV(appid)}
  clientId: WEB
  agentAccountId: null
  approvalPolicy: null
  approvalSubPolicy: null
  batchSign: false
  cacheKey: null
  dingUser: null
  language: null
  needSignWilling: true
  orderNo: null
  securityCode: null
  serviceId: null

request:
  url: ${ENV(footstone_url)}/v3/signflows/$flowId/signfields/add-Update-Execute
  method: PUT
  headers:
    Content-Type: application/json
    X-Tsign-Open-App-Id: $appId
    X-Tsign-Client-Id: $clientId
  json:
    {
      "accountId": $accountId,
      "addSignfields": $addSignfields,
      "agentAccountId": $agentAccountId,
      "approvalPolicy": $approvalPolicy,              #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      "approvalSubPolicy": $approvalSubPolicy,        #审批子策略，1-标准 2-钉钉
      "async": $async,                                #是否异步执行
      "batchSign": $batchSign,                        #是否批量签署
      "cacheKey": $cacheKey,
      "clientId": $clientId,
      "dingUser": $dingUser,
      "language": $language,
      "needSignWilling": $needSignWilling,            #需要签署意愿认证
      "orderNo": $orderNo,
      "securityCode": $securityCode,
      "serviceId": $serviceId,
      "signfieldIds": $signfieldIds,
      "updateSignfields": $updateSignfields
    }
