- config:
    name: 保存授权code
#    base_url: ${ENV(saas_common_manage_url)}



- test:

    name: 传oid、code、subjectId、appID
    api: api/getseal/save-authorize-code.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293
      code: 884714c7c7c2828cd3d45b944c14bb47
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: code为空
    api: api/getseal/save-authorize-code.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293
      code: ""
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message", 授权code不能为空]

- test:
    name: oid不存在
    api: api/getseal/save-authorize-code.yml
    variables:
      oid: "1223"
      appId: 3876547293
      code: 884714c7c7c2828cd3d45b944c14bb47
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 10000009]
      - contains: ["content.message", open user不存在]

- test:
    name: oid和企业主体不匹配
    api: api/getseal/save-authorize-code.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293
      code: 884714c7c7c2828cd3d45b944c14bb47
      subjectId: 5add8dece1e941f999e589223117b799

    validate:
      - eq: ["content.code", 10000009]
      - contains: ["content.message", open user不存在]

- test:
    name: subjectid为空
    api: api/getseal/save-authorize-code.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293
      code: 884714c7c7c2828cd3d45b944c14bb47
      subjectId:

    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message", 主体id不能为空]

# - test:
#     name: oid为空
#     api: api/getseal/save-authorize-code.yml
#     variables:
#       oid:
#       appId: 3876547293
#       code: 884714c7c7c2828cd3d45b944c14bb47
#       subjectId: 5add8dece1e941f999e589223117b75b

#     validate:
#       - eq: ["content.code", 70000603]
#       - contains: ["content.message", 获取用户角色异常]
# 导致监控告警

- test:
    name: APPID为空
    api: api/getseal/save-authorize-code.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: ""
      code: 884714c7c7c2828cd3d45b944c14bb47
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 70000002]
      - contains: ["content.message", appId不能为空]

    #teardown_hooks:
     # - ${hook_sleep_n_secs(2)}








