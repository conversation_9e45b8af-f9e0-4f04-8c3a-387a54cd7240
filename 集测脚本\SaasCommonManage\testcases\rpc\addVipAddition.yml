- config:
    name: 增配功能启停
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 单个增配功能新增
    api: api/rpc/addVipAddition.yml
    variables:
      json:
        {
          "accountGid": "89562cc6dfd94914b2d343af143fd09f",
          "alias": "岸止",
          "effectiveFrom": *************,
          "effectiveTo": *************,
          "funcCode": "create_catalog",
          "funcLimit": {
            "max_create_count": 100
          }
        }
    validate:
      - eq: ["status_code", 200]

- test:
    name: 增配功能启停
    api: api/rpc/changeVipAdditionStatus.yml
    variables:
      accountGid: "89562cc6dfd94914b2d343af143fd09f"
      alias: "岸止"
      funcCode:  "create_catalog"
      status: "DISABLE"
    validate:
      - eq: [ "status_code", 200 ]

- test:
    name: 单个增配功能删除
    api: api/rpc/deleteVipAddition.yml
    variables:
      json:
        {
          "accountGid": "89562cc6dfd94914b2d343af143fd09f",
          "alias": "岸止",
          "funcCode": "create_catalog"
        }
    validate:
      - eq: [ "status_code", 200 ]

- test:
    name: 分页查询增配信息
    api: api/rpc/vipAdditionPageQuery.yml
    variables:
      pageNum: 1
      pageSize: 10
    validate:
      - eq: [ "status_code", 200 ]


- test:
    name: 增配功能批量导入
    api: api/rpc/vipAdditionBatchImport.yml
    variables:
      alias: "测试"
      fileKey: "$$871e8e3e-cf60-4056-ad43-87c40cd06440$$**********"
    validate:
      - eq: [ "status_code", 200 ]

- test:
    name: 增配到期更新--按照GID的维度
    api: api/rpc/updateVipAdditionExpire.yml
    variables:
      json:
        {
          "scene": "GID",
          "gid": "2cd302fb600d4892b0876ee23e24a62c",
          "status": 0
        }
    validate:
      - eq: [ "status_code", 200 ]
      - eq: ["content.success", True]
      - eq: ["content.scene", "GID"]

- test:
    name: 增配到期更新--按照增配功能ID的维度
    api: api/rpc/updateVipAdditionExpire.yml
    variables:
      json:
        {
          "scene": "IDS",
          "ids": [ 1869,27 ],
            "status": 1
        }
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [content.success, True]
      - eq: [content.scene, "IDS"]

- test:
    name: 增配到期更新--按照时间维度
    api: api/rpc/updateVipAdditionExpire.yml
    variables:
      json:
        {
          "scene": "TIME_RANGE",
          "startTime": "2025-07-01T00:00:00Z",
          "endTime": "2027-07-30T23:59:59Z",
            "status": 2
        }
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [content.success, True]
      - eq: [content.scene, "TIME_RANGE"]