- config:
    name: 更新关联企业的配置
    variables:
      authRelationId: 2152

- test:
    name: 关闭会员版本共享
    api: api/auth_relation/updateShareConfig.yml
    variables:
      json:
        {
          "authRelationId": "2152",
          "configKey": "shareVip",
          "open": false
        }
    validate:
      - eq: ["status_code", 200]

- test:
    name: 开启会员版本共享
    api: api/auth_relation/updateShareConfig.yml
    variables:
      json:
        {
          "authRelationId": "2152",
          "configKey": "shareVip",
          "open": true
        }
    validate:
      - eq: ["status_code", 200]
      
- test:
    name: 关闭签署流量共享
    api: api/auth_relation/updateShareConfig.yml
    variables:
      json:
        {
          "authRelationId": "2152",
          "configKey": "shareSign",
          "open": false
        }
    validate:
      - eq: ["status_code", 200]
      
- test:
    name: 开启签署流量共享
    api: api/auth_relation/updateShareConfig.yml
    variables:
      json:
        {
          "authRelationId": "2152",
          "configKey": "shareSign",
          "open": true
        }
    validate:
      - eq: ["status_code", 200]
      
- test:
    name: 关闭专属云共享
    api: api/auth_relation/updateShareConfig.yml
    variables:
      json:
        {
          "authRelationId": "2152",
          "configKey": "shareDedicatedCloud",
          "open": false
        }
    validate:
      - eq: ["status_code", 200]
      
- test:
    name: 开启专属云共享
    api: api/auth_relation/updateShareConfig.yml
    variables:
      json:
        {
          "authRelationId": "2152",
          "configKey": "shareDedicatedCloud",
          "open": true
        }
    validate:
      - eq: ["status_code", 200]