- config:
    name: 直接发起扫码签
    variables:
      orgId1: ${ENV(mx_orgId1)}
      accountId1: ${ENV(mx_accountId1)}
      fileName1: "test.pdf"
      fileSize1: 99580
      contentMd5_1: 2FwUG2Zm3BtLVOuuYCPh7g==
      filePath1: "data/test.pdf"
      taskName1: 直接发起扫码签-${getTimeStamp_ms()}

- test:
    name: 文件直传创建文件
    api: api/footstone-doc/getUploadUrl.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      contentMd5: $contentMd5_1
      contentType: application/pdf
      convert2Pdf: false
      fileName: $fileName1
      fileSize: $fileSize1
      filePath: $filePath1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId1: content.data.fileId
      - uploadUrl1: content.data.uploadUrl

- test:
    name: 上传文件到oss
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl1
      contentType: application/pdf
      contentMd5: $contentMd5_1
      filePath: $filePath1
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 企业下直接发起扫码签-扫码签方是个人
    api: api/sharesign/startTask.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      businessType: 0
      files:
        [
        {
          "fileId": $fileId1,
          "fileType":1,
          "fileName": $fileName1,
          "from":2,
          "fileSecret":false
        }
        ]
      flowTemplateId: null
      initiatorAccountId: $accountId1
      participants:
        [
        {
          "participantSubjectType":0,
          "role":"3",
          "sealType":"0,1",
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":0,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":null,
          "instances":[

          ],
          "sharable":true,
          "shareMax":5000,
          "repeatSign":false,
          "willTypes":[

          ],
          "signSealType":1,
          "signSeal":"",
          "forceReadEnd":false,
          "forceReadTime":"",
          "attachmentConfigs":[

          ]
        }
        ]
      scene: 1
      taskName: $taskName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - shareSignTaskId1: content.data.shareSignTaskId
      - participantId1: content.data.shareUrls.0.participantId

- test:
    name: 扫码参与流程
    api: api/sharesign/startProcess.yml
    variables:
      tenantId: $accountId1
      accountId: $accountId1
      orgName: null
      participantId: $participantId1
      shareSignTaskId: $shareSignTaskId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.processId", null]
      - ne: ["content.data.flowId", null]
