config:
  name: 分页查询印章列表(企业成员有权限)
  variables:
    - orgId1: ${ENV(mx_orgId)}
    - accountId1: 74ada1674a4d441eb0120c789f1b9300   #orgId1的普通员工
    - accountId2: 863da7aaebc441ab9487f1cd2eb00ebd   #非orgId1的企业成员
    - accountId3: ${ENV(mx_accountId)}   #orgId1的管理员
    - sealName1: "编辑后的印章名称${getTimeStamp_ms()}"

teststeps:
  # 参数校验-必填参数测试
  - name: 参数校验-pageNo为空
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageSize": 10
      }
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", "pageNo不能为空"]

  - name: 参数校验-pageSize为空
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1
      }
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", "pageSize不能为空"]

  - name: 参数校验-pageNo小于1
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 0,
        "pageSize": 10
      }
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", "pageNo最小值为1"]

  - name: 参数校验-pageSize小于1
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 0
      }
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", "pageSize最小值为1"]

  - name: 参数校验-pageSize大于100
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 101
      }
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", "pageSize最大值为100"]

  - name: 参数校验-pageSize等于100(边界值-不报错，正常返回)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10
      }
    extract:
      - total_count: content.data.total
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]

  - name: 参数校验-tenantId传个人oid(不报错，正常返回，但返回数据为空)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $accountId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10
      }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.total", 0]

  - name: 参数校验-pageNo超出最大页码(查不到数据但total正常返回)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": "${sum($total_count,1)}",
        "pageSize": 1
      }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - gt: ["content.data.total", 0]
      - eq: ["content.data.seals", []]

  - name: 参数校验-sealBizTypes不合法
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": false,
        "grantedSeal": false,
        "sealBizTypes": ["TEST"],
        "sealName": ""
      }
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", "不支持的印章业务类型"]

  - name: 参数校验-sealName超出最大长度50个字符
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": false,
        "grantedSeal": false,
        "sealBizTypes": [],
        "sealName": "这是一个超过五十个字符长度的印章名称测试用例这是一个超过五十个字符长度的印章名称测试用例哈哈哈哈哈哈哈"
      }
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", "印章名称不能超过50个字符"]

  # 鉴权测试
  - name: 鉴权-operatorId不是tenantId的企业成员
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId2
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": false,
        "grantedSeal": false,
        "sealBizTypes": [],
        "sealName": ""
      }
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "您不是该企业成员，请联系企业管理员加入企业。"]

  # 业务场景测试
  - name: 业务场景-仅传pageNo和pageSize(返回当前企业的印章列表，且印章url不为空)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10
      }
    extract:
      - first_seal_id: content.data.seals.0.sealId
      - first_seal_sealBizType: content.data.seals.0.sealBizType
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - gt: ["content.data.total", 0]
      - ne: ["content.data.seals.0.sealImageUrl", null]

  - name: 业务场景-grantedSeal传true，downloadFlag传false(返回其他企业授权的印章列表，且印章url为空)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": false,
        "grantedSeal": true,
        "sealBizTypes": [],
        "sealName": ""
      }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.seals.0.sealImageUrl", null]
      - ne: ["content.data.seals.0.sealId", $first_seal_id]

  - name: 业务场景-grantedSeal传false，downloadFlag传true(返回当前企业的印章列表，且印章url不为空)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": true,
        "grantedSeal": false,
        "sealBizTypes": [],
        "sealName": ""
      }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - ne: ["content.data.seals.0.sealImageUrl", null]
      - eq: ["content.data.seals.0.sealId", $first_seal_id]

  - name: 业务场景-查当前企业的法人章(当前企业有法人章)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": false,
        "grantedSeal": false,
        "sealBizTypes": ["LEGAL_PERSON"],
        "sealName": ""
      }
    extract:
      - seals: content.data.seals
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["${validate_all_seals_biz_type($seals, LEGAL_PERSON)}", true]

  - name: 业务场景-查其他企业授权的法人章(无其他企业授权的法人章)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": true,
        "grantedSeal": true,
        "sealBizTypes": ["LEGAL_PERSON"],
        "sealName": ""
      }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: [ "content.data.total", 0 ]
      - eq: ["content.data.seals", []]

  - name: 业务场景-带sealName条件查当前企业印章，sealName不存在(返回数据为空)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": false,
        "grantedSeal": false,
        "sealBizTypes": [],
        "sealName": "${getTimeStamp_ms()}"
      }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.total", 0]
      - eq: ["content.data.seals", []]

  - name: 编辑印章名称
    api: api/saas-common/organizations-seals-update-alias.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId3
      - json: {
        "sealId": $first_seal_id,
        "alias": $sealName1
      }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

  - name: 业务场景-带sealName条件查当前企业印章，sealName存在(返回印章名称匹配的当前企业印章)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": false,
        "grantedSeal": false,
        "sealBizTypes": [],
        "sealName": $sealName1
      }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: [ "content.data.total", 1]
      - eq: [ "content.data.seals.0.sealName", $sealName1]

  - name: 业务场景-带sealName条件查其他企业授权的印章，sealName不存在(返回数据为空)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": false,
        "grantedSeal": true,
        "sealBizTypes": [],
        "sealName": $sealName1
      }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: [ "content.data.total", 0 ]
      - eq: [ "content.data.seals", [] ]

  - name: 业务场景-多条件联合查询，sealBizType+sealName(返回符合查询条件的印章)
    api: api/saas-common/organizations-seals-simple-list.yml
    variables:
      - tenantId: $orgId1
      - operatorId: $accountId1
      - json: {
        "pageNo": 1,
        "pageSize": 10,
        "downloadFlag": false,
        "grantedSeal": false,
        "sealBizTypes": [$first_seal_sealBizType],
        "sealName": $sealName1
      }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.seals.0.sealName", $sealName1 ]
      - eq: ["content.data.seals.0.sealBizType", $first_seal_sealBizType]