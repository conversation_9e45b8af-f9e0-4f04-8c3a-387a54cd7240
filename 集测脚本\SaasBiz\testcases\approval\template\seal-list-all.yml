- config:
    name: 查询用印审批模板全部印章列表
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}  #企业管理员
      accountId2: 74ada1674a4d441eb0120c789f1b9300  #企业普通员工


- test:
    name: 分页查询用印审批模板印章列表-仅查企业自己的章
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        grantedSeal: false
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      total1: content.data.total

- test:
    name: 分页查询用印审批模板印章列表-指定要查询跨企业授权章
    api: api/approval/template/seal-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        grantedSeal: true
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
    extract:
      total2: content.data.total

- test:
    name: 查询用印审批模板全部印章列表-不带参数，默认不返回下载地址
    api: api/approval/template/seal-list-all.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params: null
      total: ${sum($total1, $total2)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", $total]
      - eq: ["content.data.seals.0.url", null]

- test:
    name: 查询用印审批模板全部印章列表-指定要返回印章的下载地址
    api: api/approval/template/seal-list-all.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        downloadFlag: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - ne: ["content.data.seals.0.url", null]

- test:
    name: 查询用印审批模板全部印章列表-指定不返回印章的下载地址
    api: api/approval/template/seal-list-all.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      params:
        downloadFlag: false
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - eq: ["content.data.seals.0.url", null]

- test:
    name: 查询用印审批模板全部印章列表-操作人无权限
    api: api/approval/template/seal-list-all.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      params: null
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 当前用户无操作权限]