账号A：19822832131     企业1的普通成员1（发起无需合同审批，模板需要合同审批）  姓名：测试吴一
账号B：19850433616     无企业的个人账号                      姓名：测试吴二
账号C：19884800774     企业2的普通成员                       姓名：测试吴三
账号D：19858301486    企业1的合同审批人                      姓名：测试吴四
账号E：19862458310     企业2的用印审批人
账号F：19821022857     企业1的普通员工2（发起需要合同审批）
账号G：     无企业的个人账号


企业1：esigntest自动化测试企业11  9100000056418279X1  管理员：19858301486
企业2：esigntest自动化测试企业22  910000005656252146  管理员：19858301486
企业3：esigntest自动化测试企业33    主企业    管理员：19860468145
企业4：esigntest自动化专用主企业             管理员：19860468145
企业4：esigntest自动化专用子企业             管理员：19858301486

重新发起换绑子流程id，sub_process表看一下流程id换掉了

催办，如果当前在合同审批中，会催办审批人

发起合同审批就会有数据，process_start_sign_param表基于processid查询（换绑，转交，修改抄送人）


明绣：智能台账 含企业合同列表
辰南：直接发起，模板设置
黎旭：企业控制台，经办合同
离歌：相对方，直接发起
兰生：模板发起
青莲：扫码签，经办合同



标题对应的场景更细致点
1.p0级别加个新用户，新企业（直接发起，模拟发起各一个） @兰生-吴志强(兰生)@辰南
2.直接发起 保密补一下case，要有对应是否保密的断言（无权查看的人查看详情会报错） @辰南
3.添加关联企业成功（各个流程节点关联企业次数的余额情况） @黎旭-吴旭辉(黎旭)
4.扫码签，重复扫码的场景补一下 @青莲-宁庶升(青莲-宁庶升)
5.重新发起 回填，预填的情况 @青莲-宁庶升(青莲-宁庶升)
6.解约：解约中的解约，部分解约的解约，已完成的解约 @青莲-宁庶升(青莲-宁庶升)
7.动态模板，仅签跟需填写 @兰生-吴志强(兰生)