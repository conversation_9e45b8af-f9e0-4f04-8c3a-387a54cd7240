- config:
    name: 重新生成


- test:
    name: 重新生成-关键信息重新生成成功
    api: api/contract-summary/refresh-summary-info.yml
    variables:
      "fileId": "49a76364896a4664a28e018528c63174"
      "dataType": "keyInfo"
      "processId": "53073a756cd9499bb7c9dc1ee1740236"
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]

- test:
    name: 重新生成-合同概要重新生成成功
    api: api/contract-summary/refresh-summary-info.yml
    variables:
      "fileId": "49a76364896a4664a28e018528c63174"
      "dataType": "summary"
      "processId": "53073a756cd9499bb7c9dc1ee1740236"
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]

- test:
    name: 重新生成-文件id为空
    api: api/contract-summary/refresh-summary-info.yml
    variables:
      "fileId": ""
      "dataType": "summary"
      "processId": "53073a756cd9499bb7c9dc1ee1740236"
    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: 文件ID不能为空"]

- test:
    name: 重新生成-文件id错误
    api: api/contract-summary/refresh-summary-info.yml
    variables:
      "fileId": "1111"
      "dataType": "summary"
      "processId": "53073a756cd9499bb7c9dc1ee1740236"
    validate:
        - eq: ["content.code", 10000001]
        - eq: ["content.message", "合同不存在"]

- test:
    name: 重新生成-合同id为空
    api: api/contract-summary/refresh-summary-info.yml
    variables:
      "fileId": "49a76364896a4664a28e018528c63174"
      "dataType": "keyInfo"
      "processId": ""
    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: 合同ID不能为空"]

- test:
    name: 重新生成-合同id错误
    api: api/contract-summary/refresh-summary-info.yml
    variables:
      "fileId": "49a76364896a4664a28e018528c63174"
      "dataType": "keyInfo"
      "processId": "111"
    validate:
        - eq: ["content.code", 31202001]
        - eq: ["content.message", "合同流程不存在"]
