- config:
    name: 主任务创建完成后，添加子任务
#    base_url: ${ENV(saas_common_manage_url)}


- test:
    name: 主任务创建完成后，添加子任务
    api: api/rpc/taskAddSubTaskbatchAddInput.yml
    variables:
      json: {
        "parentId": "c5cf30712a394e878a2b7f1f616a82ae",
        "subTaskList": [
          {
            "accountGid": "accountGid",
            "accountOid": "accountOid",
            "bizId": "d3686ee1bd214a30ae190a4312880da1",
            "bizInfo": {
              "exportInfos": [

              ],
              "organizationId": "accountOid"
            },
            "name": "单侧子任务",
            "status": 1,
            "type": 15
          }
        ]
      }

#    validate:
#      - eq: [ "status_code", 200 ]
#      - ne: [ "content.bizIdUUidMap", null ]
