- config:
    name: 获取授权页面企业/用户等信息
#    base_url: ${ENV(saas_common_manage_url)}



- test:
    name: 传oid appid subjectId
    api: api/getseal/authorize-aggregate-info.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: subjectId不存在
    api: api/getseal/authorize-aggregate-info.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293
      subjectId: 5add8dece1e941f999e589223117b999

    validate:
      - eq: ["content.code", 10000009]
      - contains: ["content.message", open user不存在]


- test:
    name: oid不存在
    api: api/getseal/authorize-aggregate-info.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb99
      appId: 3876547293
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 10000009]
      - contains: ["content.message", open user不存在]

- test:
    name: oid为空
    api: api/getseal/authorize-aggregate-info.yml
    variables:
      oid: ""
      appId: 3876547293
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 70000603]
      - contains: ["content.message", 获取用户角色异常]

- test:
    name: subjectId为空
    api: api/getseal/authorize-aggregate-info.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293
      subjectId: ""

    validate:
      - eq: ["content.code", 70000603]
      - contains: ["content.message", 获取用户角色异常]

- test:
    name: oid不是管理员or法人
    api: api/getseal/authorize-aggregate-info.yml
    variables:
      oid: 8db662558c30419885b125314f312aa3
      appId: 3876547293
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 70000602]
      - contains: ["content.message", 用户非法人或管理员]

- test:
    name: oid和subjectid不在同一空间下
    api: api/getseal/authorize-aggregate-info.yml
    variables:
      oid: 8db662558c30419885b125314f312aa3
      appId: 3876547293
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: ["content.code", 70000602]
      - contains: ["content.message", 用户非法人或管理员]

- test:
    name: appId为空
    api: api/getseal/authorize-aggregate-info.yml
    variables:
      oid: 9c27b69717534e68857f9fb09c9dbb0b
      appId: 3876547293
      subjectId: 5add8dece1e941f999e589223117b75b

    validate:
      - eq: [ "content.code", 0 ]
      - contains: [ "content.message", 成功 ]
    #teardown_hooks:
     # - ${hook_sleep_n_secs(2)}
