- config:
    name: 页数余额查询
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      orgId2: a7c30a23ba734c6292e9aad8f4b7b676   #企业未实名
      orgId3: 913ed9f6ac5742a999ca8c002775f490

- test:
    name: 页数余额查询
    api: api/contract-ledger/ai-page-balance.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ge: ["content.data.count", 0]

- test:
    name: 页数余额查询-企业未实名
    api: api/contract-ledger/ai-page-balance.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.count", 0]

- test:
    name: 页数余额查询-操作人不是企业成员
    api: api/contract-ledger/ai-page-balance.yml
    variables:
      tenantId: $orgId3
      operatorId: $accountId1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", "您不是该企业成员，请联系企业管理员加入企业。"]