- config:
    name: 会员功能相关操作
    variables:
      orgId: "0001095aa4bb4f178dafd418ac274a62"  #企业oid 阮小五测试企业
      adminId: "a46d29697c50498eb9ff75655ba9bad1"  #管理员oid   阮小五
      oid1: "219461ffede84f629235751ca4967b8e" #企业成员  阮小七
      effectiveTime0: "${getTimeStamp()}" #生效开始时间
      expireTime0: "${getTimeStamp()}"  #有效期截止时间
      grantedUserCode1: "912345098765432345"
      grantedUserName1: "esigntest你是真的秀"
      orgId2: "52b72c6d9ac941bebdd0431d97f2f8ab" # esigntest你是真的秀


#查询会员功能列表
- test:
    name: 查询会员功能列表
    variables:
        jwtcontent: eyJhbGlhcyI6Iua1heW_hiIsImlkIjoicWlhbnlpIn0=
        pageNo: 1
        pageSize: 10
        selectType: ''
        selectValue: ''

    api: api/functions/func_list.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#校验功能标识是否存在
- test:
    name: 校验功能标识是否存在
    variables:
        jwtcontent: eyJhbGlhcyI6Iua1heW_hiIsImlkIjoicWlhbnlpIn0=
        funcCode: affiliated_enterprises

    api: api/functions/check-code.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#查询指定会员功能详情
- test:
    name: 查询指定会员功能详情
    variables:
        jwtcontent: eyJhbGlhcyI6Iua1heW_hiIsImlkIjoicWlhbnlpIn0=
        funcCode: affiliated_enterprises

    api: api/functions/functions_info.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#- test:
#    name: 获取用户指定会员功能信息
#    variables:
#        appId: ${ENV(BZQ_appId)}
#        Tenantoid: c76c2f6f136d4997aaccf30f1f767f59
#        Operatoroid: 565a742760cc485185bbd3cfc1e47e80
#        json:
#          {
#            "functionCodes": [ "batch_initiate" ]
#          }
#    api: api/functions/vip-batch.yml
#    validate:
#        - eq: ["content.code", 0]
#        - gt: ["content.data.functions.0.limit.max_batch_count", 0]