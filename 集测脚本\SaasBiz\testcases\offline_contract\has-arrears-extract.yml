- config:
    name: 查询是否存在欠费的提取记录
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      orgId2: 913ed9f6ac5742a999ca8c002775f490

- test:
    name: 查询是否存在欠费的提取记录-recordId不存在
    api: api/offline_contract/has-arrears-extract.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      recordId: 123
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 导入记录不存在]

- test:
    name: 查询是否存在欠费的提取记录-操作人不是企业成员
    api: api/offline_contract/has-arrears-extract.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      recordId: 123
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 企业成员不存在]