- config:
    name: 验证手机验证码
    variables:
      accountId1: ${ENV(accountId1)}


- test:
    name: 验证手机验证码-authCodeId为空
    api: api/phone-code/authCode.yml
    variables:
      operatorId: $accountId1
      authCodeId: ""
      code: 123456
#    validate:
#      - eq: ["content.code", *********]
#      - contains: ["content.message", "验证码唯一id不能为空"]

- test:
    name: 验证手机验证码-code为空
    api: api/phone-code/authCode.yml
    variables:
      operatorId: $accountId1
      authCodeId: 123
      code: ""
#    validate:
#      - eq: ["content.code", *********]
#      - contains: ["content.message", "验证码不能为空"]
      
#- test:
#    name: 发送手机验证码-成功
#    api: api/phone-code/sendCode.yml
#    variables:
#      operatorId: $accountId1
#      bizTag: apply-trial
#      phone: ***********
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#    extract:
#      - authCodeId1: content.data.authCodeId
#
#- test:
#    name: 验证手机验证码-code正确校验通过
#    api: api/phone-code/authCode.yml
#    variables:
#      operatorId: $accountId1
#      authCodeId: $authCodeId1
#      code: 123456
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#      - eq: ["content.data.pass", true]
