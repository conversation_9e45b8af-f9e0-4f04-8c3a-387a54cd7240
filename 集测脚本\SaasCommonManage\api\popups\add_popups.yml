name: 新增弹窗
variables:
  fileKey: ${ENV(fileKey1)}
  linkUrl: "https://www.esign.cn"
  validityEndTime: ""
  validityStartTime: ""
  weight: 1
  notifyVersions: ""

request:
  url: ${ENV(saas_common_manage_url)}/v1/saas-common/popups/add
  method: POST
  headers:
    x-timevale-jwtcontent: eyJhbGlhcyI6Iuemu-atjCIsImlkIjoibGlnZSJ9
    Content-Type: application/json
  json:
    {
      "areaCode": $areaCode,
      "configType": 0,
      "fileKey": $fileKey,
      "linkUrl": $linkUrl,
      "name": $name,
      "popupType": 0,
      "productCode": $productCode,
      "title": "",
      "validityEndTime": $validityEndTime,
      "validityStartTime": $validityStartTime,
      "noticeType": 0,
      "notifyVersions": ,
      "scrollPictures": [
      {
        "fileKey": $fileKey,
        "linkUrl": $linkUrl
      }
      ],
      "validityUserConfig": [
      {
        "conditionType": $conditionType, #条件组合方式：0-and 1-or，默认为0
        "configMap": $configMap, #对应类型配置
        "hasSatisfy": $hasSatisfy, #是否满足推送：true-满足添加时推送 false-不满足添加时推送，默认为true
        "type": $type #配置类型：1-指定用户 2-功能灰度 null-组合
      }
      ],
      "weight": $weight
    }
