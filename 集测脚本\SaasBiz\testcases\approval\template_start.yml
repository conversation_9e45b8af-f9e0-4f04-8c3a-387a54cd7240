- config:
    name: 发起时获取符合条件的审批模版（还没上架）


- test:
    name: 发起时获取符合条件的审批模版
    api: api/approval/template_start.yml
    variables:
      operatorId: 565a742760cc485185bbd3cfc1e47e80
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      templateId: 7c415156fbc04ea993f271123ed73999
      approvalTemplateName: 测试
      approvalTemplateId: ""
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["status_code", 200]
#      - eq: ["content.message", 成功]
