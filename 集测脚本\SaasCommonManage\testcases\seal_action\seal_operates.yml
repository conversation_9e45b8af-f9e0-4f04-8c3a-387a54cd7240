- config:
    name: 印章相关操作
    variables:
      orgId: "0001095aa4bb4f178dafd418ac274a62"  #企业oid 阮小五测试企业
      adminId: "a46d29697c50498eb9ff75655ba9bad1"  #管理员oid   阮小五
      effectiveTime0: "${getTimeStamp()}" #生效开始时间
      expireTime0: "${getTimeStamp()}"  #有效期截止时间
      data: ${ENV(data)}
      refuse_fileId: "66f9b050-588f-4d23-bbee-7751127716c5"

#获取印章创建配置
- test:
    name: 获取印章创建配置
    variables:
        tenantid: $orgId
    api: api/seal/seal-create-config.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#查询印章列表
- test:
    name: 查询印章列表
    variables:
        tenantid: $orgId
        pageNo: 1
        pageSize: 10
        downloadFlag: true
        statusFlag: false
        sealGrantCountFlag: true
        platformFlag: true
        imageRefuseFlag: false
        sealBizType: LEGAL_PERSON
    api: api/seal/seal_list.yml
    extract:
        - Mo_sealId: content.data.seals.0.sealId
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#预览企业模板印章
- test:
    name: 预览企业模板印章-公章
    variables:
        tenantid: $orgId
        antiFakeNumber:
        bottomText: qi
        color: PURPLE
        horizontalText: ruan
        opacity: 80
        style: NONE
        surroundTextInner:
        taxNumber:
        templateType: PUBLIC_TWO_OVAL
        widthHeight: "45_30"
    api: api/seal/preview-official-template.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#创建企业模板印章
- test:
    name: 创建企业模板印章-财务章
    variables:
        tenantid: $orgId
        alias: "企业模板章${getTimeStamp()}"
        bottomText: qi
        color: PURPLE
        horizontalText: ruan
        opacity: 80
        style: NONE
        surroundTextInner:
        templateType: FINANCE_ROUND_STAR
        widthHeight: "38_38"
    api: api/seal/add_organizationstemplate.yml
    extract:
        - Cu_sealId: content.data.sealId
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#查询企业模板章详情
- test:
    name: 查询企业模板章详情
    variables:
        tenantid: $orgId
        sealId: $Cu_sealId
    api: api/seal/official-template-detail.yml
    validate:
        - eq: ["content.code",0]
        - eq: ["content.data.status", 1]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#编辑印章名称
- test:
    name: 编辑印章名称
    variables:
        operatorid: $adminId
        tenantid: $orgId
        sealId: $Cu_sealId
        alias: 修改印章名称q
        sealOwnerOid: ""
    api: api/seal/update-alias.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 删除印章接口
    variables:
        sealId: $Cu_sealId
        tenantid: $orgId
    api: api/seal/delete_seal.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","成功"]

- test:
    name: 压缩，透明化处理印章图片
    variables:
        tenantid: $orgId
        color: PURPLE
        cvalues:
        - 100
        data: $data
        dataType: FILEKEY
        downloadFlag: true
        opacity: 100

    api: api/seal/compress-seal.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","成功"]

#新增企业图片印章
- test:
    name: 新增企业图片印章-公章
    variables:
        tenantid: $orgId
        alias: 本地上传${getTimeStamp()}
        auditFlag: true
        data: $data
        downloadFlag: true
        handleFlag:
        height: 166
        notifyUrl: null
        operateType: submit
        sealBizType: CONTRACT
        sealType: 3
        transparentFlag: true
        type: FILEKEY
        uploadFileMd5: ""
        width: 166

    extract:
        - Bg_sealId: content.data.sealId
    api: api/seal/create-organization-image.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#查询企业模板图片章详情
- test:
    name: 查询企业图片章详情
    variables:
        tenantid: $orgId
        sealIds: $Bg_sealId
        orgId: $orgId
        downloadFlag: true
        operator:
    api: api/seal/get_bySealId.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.seals.0.status", 2]

- test:
    name: 删除印章rpc接口-清数据
    variables:
        Rpc_sealId: $Bg_sealId
    api: api/seal/deleteSeal_rpc.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","执行成功"]

#查看法人章授权详情
- test:
    name: 查看法人章授权详情
    variables:
        tenantid: $orgId
        orgId: $orgId
    api: api/seal/legal-auth-detail.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#查看法人章授权状态
- test:
    name: 查看法人章授权状态
    variables:
        tenantid: $orgId
        orgId: $orgId
    api: api/seal/legal-auth-status.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#新增法人模板章
- test:
    name: 新增法人模板章
    variables:
        operatorid: $adminId
        tenantid: $orgId
        alias: 法人模板章${getTimeStamp()}
        color: PURPLE
        opacity: 80
        stampRule: "1"
        style: NONE
        templateType: RECTANGLE_BORDER
        widthHeight: "20_10"
    extract:
        - Fa_sealId: content.data.sealId
    api: api/seal/create-legal-template.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#预览法人章
- test:
    name: 预览法人章
    variables:
        tenantid: $orgId
        color: BLACK
        opacity: 99
        stampRule: "0"
        style: NONE
        templateType: SQUARE_RIGHT_BORDER
        widthHeight: "18_18"
    api: api/seal/preview-legal-template.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

#查询法人模板章详情
- test:
    name: 查询法人模板章详情
    variables:
        tenantid: $orgId
        sealId: $Fa_sealId
    api: api/seal/legal-template-detail.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.status", 1]
        - contains: ["content.data.sealTemplate.text", "阮小五印"]

#设置企业默认章
- test:
    name: 设置企业默认章
    variables:
        tenantid: $orgId
        sealId: $Mo_sealId
    api: api/seal/set-default-seal.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 删除印章rpc接口-清数据
    variables:
        Rpc_sealId: $Fa_sealId
    api: api/seal/deleteSeal_rpc.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","执行成功"]

#创建企业法人图片印章
- test:
    name: 创建企业法人图片印章
    variables:
        operatorid: $adminId
        tenantid: $orgId
        alias: 本地上传${getTimeStamp()}
        data: $data
        downloadFlag: true
        height: 166
        notifyUrl: null
        operateType: submit
        sealType: 3
        transparentFlag: true
        type: FILEKEY
        uploadFileMd5: ""
        width: 166
    extract:
        - Bf_sealId: content.data.sealId
    api: api/seal/create-legal-image.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]

- test:
    name: 查询法人图片章详情
    variables:
        tenantid: $orgId
        sealIds: $Bf_sealId
        orgId: $orgId
        downloadFlag: true
        operator:
    api: api/seal/get_bySealId.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.seals.0.status", 2]

- test:
    name: 删除印章rpc接口-清数据
    variables:
        Rpc_sealId: $Bf_sealId
    api: api/seal/deleteSeal_rpc.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","执行成功"]

- test:
    name: 获取印章审核拒绝原因
    variables:
        tenantid: $orgId
        sealId: $refuse_fileId
    api: api/seal/audit-refuse-info.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","成功"]

#新增证明材料
- test:
    name: 新增企业证明材料
    variables:
        tenantid: $orgId
        alias: 本地上传${getTimeStamp()}
        auditFlag: true
        data: $data
        downloadFlag: true
        handleFlag:
        height: 166
        notifyUrl: null
        operateType: submit
        sealBizType: CONTRACT
        sealType: 3
        transparentFlag: true
        type: FILEKEY
        uploadFileMd5: ""
        width: 166
        materialFileKeyOfEng: $data
    extract:
        - Bg_sealId: content.data.sealId
    api: api/seal/create-organization-zmimage.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.message", "成功"]
