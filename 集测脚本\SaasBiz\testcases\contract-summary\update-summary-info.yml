- config:
    name: 修改合同提取信息


- test:
    name: 修改合同提取信息-成功
    api: api/contract-summary/update-summary-info.yml
    variables:
      "processId": "c3ac343204fd488f87346ff9b0533aa1"
      "fileId": "69acc03aca58483bb3e740ab34ca5a16"
      "name": "测试"
      "summary": "测试一下"
      "seq": 1
      "aiValue": "王明"
      "value": 张三

    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]

- test:
    name: 修改合同提取信息-摘要信息为空
    api: api/contract-summary/update-summary-info.yml
    variables:
      "processId": "c3ac343204fd488f87346ff9b0533aa1"
      "fileId": "69acc03aca58483bb3e740ab34ca5a16"
      "name": "测试"
      "summary": ""
      "seq": 1
      "aiValue": "王明"
      "value": "张三"

    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: 摘要不能为空"]

- test:
    name: 修改合同提取信息-合同ID为空
    api: api/contract-summary/update-summary-info.yml
    variables:
      "processId": ""
      "fileId": "69acc03aca58483bb3e740ab34ca5a16"
      "name": "测试"
      "summary": "测试一下"
      "seq": 1
      "aiValue": "王明"
      "value": "张三"

    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: 合同ID不能为空"]

- test:
    name: 修改合同提取信息-合同流程不存在
    api: api/contract-summary/update-summary-info.yml
    variables:
      "processId": "111"
      "fileId": "69acc03aca58483bb3e740ab34ca5a16"
      "name": "测试"
      "summary": "测试一下"
      "seq": 1
      "aiValue": "王明"
      "value": "张三"

    validate:
        - eq: ["content.code", 31202001]
        - eq: ["content.message", "合同流程不存在"]

- test:
    name: 修改合同提取信息-文件ID为空
    api: api/contract-summary/update-summary-info.yml
    variables:
      "processId": "c3ac343204fd488f87346ff9b0533aa1"
      "fileId": ""
      "name": "测试"
      "summary": "测试一下"
      "seq": 1
      "aiValue": "王明"
      "value": "张三"

    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", '参数错误: 文件ID不能为空']

- test:
    name: 修改合同提取信息-文件ID不存在
    api: api/contract-summary/update-summary-info.yml
    variables:
      "processId": "c3ac343204fd488f87346ff9b0533aa1"
      "fileId": "111"
      "name": "测试"
      "summary": "测试一下"
      "seq": 1
      "aiValue": "王明"
      "value": "张三"

    validate:
        - eq: ["content.code", 31202176]
        - eq: ["content.message", '合同摘要不存在']



