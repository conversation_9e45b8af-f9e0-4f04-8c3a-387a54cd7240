- config:
    name: 企业试用功能


- test:
    name: 查询企业试用的功能
    api: api/user_action/queryOrgTrailFunction.yml
    variables:
      funcCode: ding_attach_approval
      gid: 37bdebc911bf495ca13c7410fd314a9b
    validate:
      - eq: ["status_code", 200]
#      - contains: ["content.message", 账号不存在或已注销]

- test:
    name: 保存企业试用的功能
    api: api/user_action/saveOrgTrailFunction.yml
    variables:
      funcCode: ding_attach_approval
      gid: e9cd908${getTimeStamp()}
      oid: 09d83d8687b74df292862c0cd8cc2e98
      createOid: b27913606bfd4f74925c36367e2cc985
      trialType: 1
    validate:
      - eq: ["status_code", 200]
#      - contains: ["content.message", 账号不存在或已注销]
