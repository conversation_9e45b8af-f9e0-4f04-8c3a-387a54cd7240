- config:
    name: 审批加减审批人
    variables:
      approvalId1: AF-34e5f987bb0749a6819127740c67c627


- test:
    name: 查看审批流
    api: api/approval/mix_flow.yml
    variables:
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      operatorId: a690089d48a14707a1cb78c453a0d991
      approvalId: $approvalId1
      approvalType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      taskId1: content.data.list.0.approvalLogs.0.taskId

- test:
    name: 审批详情，追加审批人
    api: api/approval/add-approval.yml
    variables:
      operatorId: a690089d48a14707a1cb78c453a0d991
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      json:
        {
          "operateDatas": [
            {
              "approvalType": "1",
              "remark": "autotest",
              "datas": [
                {
                  "approvalId": $approvalId1,
                  "taskId": $taskId1,
                  "processId": "6e0e6a2efe464923803d0be2f1758ffe"
                }
              ]
            }
          ],
          "type": 1,
          "candidates": [
            {
              "candidate": "675c4018dd544f70a5aea3be8c26127f",
              "candidateType": "ASSIGN_PERSONNEL",
              "userName": "测试张三"
            }
          ]
        }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 查看审批流
    api: api/approval/mix_flow.yml
    variables:
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      operatorId: a690089d48a14707a1cb78c453a0d991
      approvalId: $approvalId1
      approvalType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      taskId2: content.data.list.0.approvalLogs.1.taskId
    teardown_hooks:
        - ${hook_sleep_n_secs(5)}

- test:
    name: 审批详情，删除追加审批人
    api: api/approval/reduce-approval.yml
    variables:
      operatorId: a690089d48a14707a1cb78c453a0d991
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      json:
        {
          "operateDatas": [
            {
              "approvalType": "1",
              "remark": "",
              "datas": [
                {
                  "approvalId": $approvalId1,
                  "processId": "6e0e6a2efe464923803d0be2f1758ffe",
                  "taskId": $taskId2
                }
              ]
            }
          ]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]