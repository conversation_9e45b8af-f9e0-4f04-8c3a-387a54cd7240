- config:
    name:  管理员敏感权限隔离-经办合同&企业合同
    variables:
      orgId1: 98d8afcd2dd74e53b12a13434268d3b3
      accountId1: 74ada1674a4d441eb0120c789f1b9300  #管理员
      accountGid1: 21901c7012e74dafa3fb94826f8047a9
      account1: <EMAIL>
      accountName1: 大白
      accountId2: 276b5a8f2a964142bb1ebcf0dd82bf72  #法人


- test:
    name: 被隔离的管理员在待归档合同列表修改非经办合同的合同备注无权限
    api: api/contract/add-remark.yml
    variables:
      tenantId: $orgId1
      OperatorId: $accountId1
      menuId: -1
      processId: "fc2e81bad51947779d1c7686e45c8b05"
      remark: "测试"
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 用户无操作权限]

- test:
    name: 数据处理-归档合同
    api: api/contract-manager/grouping-processes.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      json:
        {
          "processIdList": [
            "fc2e81bad51947779d1c7686e45c8b05"
          ],
          "accountId": $accountId2,
          "menuIdList": [
            "be472abaae9e4c1ea891f2ff6fdb7e04"
          ]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 被隔离的管理员在已归档全部合同列表修改非经办合同的合同备注无权限
    api: api/contract/add-remark.yml
    variables:
      tenantId: $orgId1
      OperatorId: $accountId1
      menuId: MENU_ALL
      processId: "fc2e81bad51947779d1c7686e45c8b05"
      remark: "测试"
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 用户无操作权限]

- test:
    name: 被隔离的管理员在已归档分类合同列表修改非经办合同的合同备注无权限
    api: api/contract/add-remark.yml
    variables:
      tenantId: $orgId1
      OperatorId: $accountId1
      menuId: "be472abaae9e4c1ea891f2ff6fdb7e04"
      processId: "fc2e81bad51947779d1c7686e45c8b05"
      remark: "测试"
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 用户无操作权限]

- test:
    name: 被隔离的管理员在已归档分类合同列表修改合同的AI提取值-查看提取值
    api: api/contract-ledger/get-form-data.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      menuId: "be0b20cb27a64e9b8b8010c3d407afe5"
      processId: "fa2bc528cbf74dbd89fba1d1af6682ea"
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 用户无操作权限]
