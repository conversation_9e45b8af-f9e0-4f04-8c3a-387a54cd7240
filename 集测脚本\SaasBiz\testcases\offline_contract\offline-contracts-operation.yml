- config:
    name: 纸质合同相关操作
    variables:
      account1: ***********
      accountName1: 隆多
      orgId1_name: esigntest你是真的皮
      orgId1: c2b9e9ff57f548e09e822959c64f43c9  #esigntest你是真的皮
      menuId1: 83c72cbff34643cbbbaa7a24f60d9f9c #你是真的皮，企业下分类
      OperatorId1: 69f8e39686744698ba5ffe303c8d1501
      fileId1: 62667773396a476b9e5108ca7f49112c
      fileKey1: ${ENV(offline_fileKey)}


- test:
    name: 查询线下合同导入记录列表
    variables:
        tenantId: $orgId1
        OperatorId: $OperatorId1
        pageNum: 1
        pageSize: 10
    api: api/offline_contract/list-records.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#导入线下合同
- test:
    name: 导入线下合同-手动录入方式
    variables:
        tenantId: $orgId1
        OperatorId: $OperatorId1
        menuId: $menuId1
        importWay: BATCH_PDF
        extractWay: MANUAL_INPUT
        contracts:
          - contractFiles:
              - categoryId: ''
                categoryName: ''
                contractNo: ${getTimeStamp()}
                fileId: $fileId1
                fileName: 产品销售合同-刑天.pdf
                fileType: CONTRACT
            processInfo:
              contractValidity: *************
              signers:
                - account: $account1
                  accountName: $accountName1
                  subjectName: $orgId1_name
                  subjectType: 1
              title: 产品销售合同-${getTimeStamp()}
        extractConfig: null

    api: api/offline_contract/import-offline-contracts.yml
    extract:
        - recordId1: content.data.recordId
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

##导入线下合同
#- test:
#    name: 导入线下合同-AI提取方式
#    variables:
#        tenantId: $orgId1
#        OperatorId: $OperatorId1
#        menuId: $menuId1
#        importWay: BATCH_PDF
#        extractWay: AI_EXTRACT
#        contracts:
#          - contractFiles:
#              - categoryId: ''
#                categoryName: ''
#                contractNo: ''
#                fileId: $fileId1
#                fileName: 产品销售合同-刑天.pdf
#                fileType: CONTRACT
#            processInfo:
#              contractValidity: null
#              signers:
#                - account: ''
#                  accountName: ''
#                  subjectName: ''
#                  subjectType: 0
#              title: 产品销售合同AI提取-${getTimeStamp()}
#        extractConfig:
#          fields:
#            - fieldCode: contractNo
#              enable: true
#            - fieldCode: signerName
#              enable: true
#            - fieldCode: signerOrgName
#              enable: true
#            - fieldCode: contractValidity
#              enable: true
#          signers:
#            - signerKeyword: 甲方
#              signerSubjectType: 1
#            - signerKeyword: 乙方
#              signerSubjectType: 0
#
#    api: api/offline_contract/import-offline-contracts.yml
#    extract:
#        - recordId2: content.data.recordId
#    validate:
#        - eq: ["content.code",0]
#        - contained_by: ["content.message", ["成功","缺少参数"]]
#    teardown_hooks:
#      - ${hook_sleep_n_secs(1)}

- test:
    name: 查询线下合同导入记录基本信息
    variables:
        tenantId: $orgId1
        OperatorId: $OperatorId1
        recordId: $recordId1
        withMenuPath: true
    api: api/offline_contract/record-info.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
        - eq: ["content.data.menuId",$menuId1]
        - eq: ["content.data.status",IMPORTED]

- test:
    name: 查询线下合同导入记录合同文件信息-手动录入方式
    variables:
        tenantId: $orgId1
        OperatorId: $OperatorId1
        recordId: $recordId1
        withExtract: true
        pageNum: 1
        pageSize: 10
    api: api/offline_contract/record-contract-files.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
        - ne: ["content.data.contracts.0.contractFiles.0.contractNo",null]
        - eq: ["content.data.contracts.0.processInfo.signers.0.accountName",$accountName1]
        - eq: ["content.data.contracts.0.processInfo.signers.0.subjectName",$orgId1_name]
        - eq: ["content.data.contracts.0.processInfo.signers.0.account", "***********"]

    extract:
        - recordProcessId1: content.data.contracts.0.recordProcessId

#- test:
#    name: 查询线下合同导入记录合同文件信息-AI提取方式
#    variables:
#        tenantId: $orgId1
#        OperatorId: $OperatorId1
#        recordId: $recordId2
#        withExtract: true
#        pageNum: 1
#        pageSize: 10
#    api: api/offline_contract/record-contract-files.yml
#    validate:
#        - eq: ["content.code",0]
#        - contained_by: ["content.message", ["成功","缺少参数"]]
#        - eq: ["content.data.contracts.0.contractFiles.0.contractNo","**************"]
#        - eq: ["content.data.contracts.0.processInfo.signers.0.accountName","esigntest北川"]
#        - eq: ["content.data.contracts.0.processInfo.signers.0.subjectName","esigntest北川集团总部A"]
#        - eq: ["content.data.contracts.0.processInfo.signers.1.subjectName", "esigntest刑天"]
#        - eq: ["content.data.contracts.0.processInfo.signers.1.subjectType", 0]
#    extract:
#        - recordProcessId1: content.data.contracts.0.recordProcessId

- test:
    name: 删除线下合同导入记录
    variables:
        tenantId: $orgId1
        OperatorId: $OperatorId1
        recordIds:
          - $recordId1
#          - $recordId2
    api: api/offline_contract/delete-records.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 下载线下合同合同信息录入excel模板
    variables:
        tenantId: $orgId1
        OperatorId: $OperatorId1
        contractFileNames:
          - 产品销售合同-图灵
          - 产品销售合同-新新
          - 产品销售合同-刑天
        signerConfigs:
          - signerKeyword: ''
            signerSubjectType: 1
          - signerKeyword: ''
            signerSubjectType: 0

    api: api/offline_contract/download-contract-excel.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.data.downloadUrl", "esignoss.esign.cn"]

- test:
    name: 解析线下合同合同信息录入excel
    variables:
        tenantId: $orgId1
        OperatorId: $OperatorId1
        fileKey: $fileKey1
        errorDataHandleWay: NONE
        signerConfigs:
          - signerKeyword: ''
            signerSubjectType: 1
          - signerKeyword: ''
            signerSubjectType: 0

    api: api/offline_contract/parse-contract-excel.yml
    validate:
        - eq: ["content.code",0]
        - contains: ["content.data.processInfos.0.title", "产品销售合同"]
        - eq: ["content.data.processInfos.0.signers.0.account", "<EMAIL>"]
        - eq: ["content.data.processInfos.0.signers.1.accountName", "张三"]
        - eq: ["content.data.processInfos.1.signers.0.accountName", "青莲"]


- test:
    name: 查询纸质合同分类数据
    variables:
        tenantId: $orgId1
        OperatorId: $OperatorId1
        pageNum: 1
        pageSize: 10
        menuId: $menuId1
        matching: '[{"key":"processCreateTime","value":null,"sort":"","isPublic":false},{"key":"personName","value":[""],"sort":"","isPublic":false}]'
        withApproving: true

    api: api/offline_contract/grouping_lists.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
    extract:
        - ProcessId1: content.data.groupingProcessList.0.processId
#        - ProcessId2: content.data.groupingProcessList.1.processId
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 将纸质合同移出分类
    variables:
        tenantId: $orgId1
        OperatorId: $OperatorId1
        targetMenuId: $menuId1
        processIds:
          - $ProcessId1
#          - $ProcessId2
        menuIdList:
          - $menuId1

    api: api/offline_contract/removeMenu.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

