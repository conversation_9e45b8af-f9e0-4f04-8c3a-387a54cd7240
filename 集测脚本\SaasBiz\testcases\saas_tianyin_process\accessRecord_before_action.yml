- config:
    name: 获取上一个访问记录URL


- test:
    name: 获取上一个访问记录URL
    api: api/saas_tianyin_process/accessRecord_before.yml
    variables:
      accountId: ${ENV(gray_user_oid)}
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
#      - ne: ["content.data.redirectUrl", ""]


- test:
    name: 获取上一个访问记录URL-accountId不存在
    api: api/saas_tianyin_process/accessRecord_before.yml
    variables:
      accountId: '123456'
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
      - eq: [ "content.data.redirectUrl", "" ]

