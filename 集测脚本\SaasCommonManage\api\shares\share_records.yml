name: 获取分享记录
variables:
  pageSize: 10
  pageNum: 1
  operateStartTime: ""
  operateEndTime: ""
  resourceName: ""
  shareTargetSearch: ""
  operatorSearch: ""
  appid: ${ENV(appid)}
request:
  url: ${ENV(inner_open_url)}/v1/saas-common/shares/records
  method: GET
  headers: ${gen_headers($appid, X-Tsign-Open-Tenant-Id=$tenantId)}
  params:
    pageSize: $pageSize
    pageNum: $pageNum
    status: $status
    operateStartTime: $operateStartTime
    operateEndTime: $operateEndTime
    resourceName: $resourceName
    shareTargetSearch: $shareTargetSearch
    operatorSearch: $operatorSearch
    resourceId: $resourceId
