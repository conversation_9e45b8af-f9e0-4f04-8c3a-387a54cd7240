 #查询编号规则列表
    request:
        url: ${ENV(saas_common_manage_url)}/pageQuery/tenantGid/pageSize/pageNum?tenantGid=$tenantGid&pageSize=$pageSize&pageNum=$pageNum
        method: GET
        headers:
            Content-Type: application/json
#            X-Tsign-Open-Operator-Id: $operatorid
#            X-Tsign-Open-Tenant-Id: $tenantid
#            X-Tsign-Open-App-Id: ${ENV(appid)}
#            X-Tsign-Service-Group: ${ENV(Groupid)}
#            X-Tsign-Open-Auth-Mode: simple
#
#        json:
#          {
#              "alias": $alias,
#              "bottomText": $bottomText,
#              "color": $color,
#              "horizontalText": $horizontalText,
#              "opacity": $opacity,
#              "style": $style,
#              "surroundTextInner": $surroundTextInner,
#              "templateType": $templateType,
#              "widthHeight": $widthHeight
#          }
