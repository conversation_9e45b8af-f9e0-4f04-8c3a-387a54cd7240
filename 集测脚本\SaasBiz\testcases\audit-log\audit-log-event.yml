- config:
    name: 查询事件列表


- test:
    name: 查询事件列表
    api: api/audit-log/audit-log-event.yml
    variables:
        module: "saas_contract_template"
        orgId_lige: "752cbb97b722461d891f4682042a15c4 "
    validate:
        - eq: ["content.code", 0]
        - contains: ["content.message", 成功]

- test:
    name: 查询事件列表-非企业成员
    api: api/audit-log/audit-log-event.yml
    variables:
        module: "saas_contract_template"
        orgId_lige: "00ccae92ea86444ca5fbadb42026c943"
    validate:
        - eq: ["content.code", 10000015]
        - contains: ["content.message", "您不是该企业成员，请联系企业管理员加入企业。"]