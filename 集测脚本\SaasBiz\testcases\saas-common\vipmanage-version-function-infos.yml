- config:
    name: 获取当前账号会员版本功能信息
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}

- test:
    name: 获取当前账号会员版本功能信息
    api: api/saas-common/vipmanage-version-function-infos.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.functionInfos", []]