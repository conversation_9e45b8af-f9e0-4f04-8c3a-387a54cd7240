- config:
    name: 生成水印模板临时快照


- test:
    name: 生成水印模板临时快照-超过字数限制
    api: api/watermark-template/snapshot.yml
    variables:
      watermarkId: 0cf059e9f02242c8ba272c9d2cb629f6
      content: "明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照明月松间照明明月松间照明月松间照"
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      operatorid: 66787bd97bc343f0835a593be185d245
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 120000004]
      - eq: ["content.message", "水印内容最多100个字符"]

- test:
    name: 生成水印模板临时快照
    api: api/watermark-template/snapshot.yml
    variables:
      watermarkId: 0cf059e9f02242c8ba272c9d2cb629f6
      content: "明月松间照明明月松间照明月松间照明月松间照"
      tenantId: d9ec3169a6e04d148e5a8cc08ab3c13d
      operatorid: 66787bd97bc343f0835a593be185d245
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]