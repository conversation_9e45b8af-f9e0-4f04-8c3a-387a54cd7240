 #二次授权新增
    variables:
        appid: ${ENV(appid)}
    request:
        url: ${ENV(inner_open_url)}/v1/saas-common/rules-grant/seals/add-second
        method: POST
        headers: ${gen_headers($appid, X-Tsign-Open-Tenant-Id=$tenantid, X-Tsign-Open-Operator-Id=$operatorid)}
        json:
          {
            "appScheme": $appScheme,
            "authKey": $authKey,
            "autoFall": $autoFall,
            "effectiveTime": $effectiveTime,
            "expireTime": $expireTime,
            "grantedAccountIds": $grantedAccountIds,
            "h5": $h5,
            "roleKey": $roleKey,
            "scopeList": $scopeList,
            "sealGrantBizId": $sealGrantBizId,
            "token": $token
          }
