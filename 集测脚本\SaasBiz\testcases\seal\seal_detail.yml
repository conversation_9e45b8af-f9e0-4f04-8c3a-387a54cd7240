- config:
    name: 印章详情页场景
    variables:
      accountId1: ${ENV(nss_accountId1)}
      account1: ***********
      accountName1: 隆多
      accountId2: ${ENV(nss_accountId2)}
      orgId1: ${ENV(nss_orgid1)}
      sealId1: 15e2446b-8109-4c77-8ac2-f97f6e720a56


- test:
    name: 查询企业印章详情-通用接口
    api: api/seal/seal-detail.yml
    variables:
      tenantId: $orgId1
      OperatorId: $accountId2
      sealId: $sealId1

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.sealId", $sealId1]
      - eq: ["content.data.oid", $orgId1]


- test:
    name: 用印统计查询
    api: api/seal/usage-list.yml
    variables:
      tenantId: $orgId1
      OperatorId: $accountId2
      grantType: 1
      logTimeEnd: ${getTimeStamp_ms()}
      logTimeStart: ${get_next_month_x3_timestamp()}
      pageNo: 1
      pageSize: 10
      sealId: $sealId1
      signerName: ''

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 授权企业/成员列表
    api: api/seal/rule-grant-list.yml
    variables:
      tenantId: $orgId1
      OperatorId: $accountId2
      orgId: $orgId1
      resourceId: $sealId1
      ruleGrantStatus: ALL
      offset: 0
      size: 20
      type: 1

    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
