- config:
    name: 台账串场景-合同类型AI智能提取
    variables:
      orgId1: 67e1edfc41be46dfa824c8af811ca844
      accountId1: ${ENV(mx_accountId)}
      categoryId1: cabf8b438c5a4e35bd63740adbc4b479
      categoryId2: 07cfeb8d838749c08ba25250414eb565
      fieldId1: 8f8cb5c240ae45858b74d29ce58ee97e
      fieldId2: 729ce3ff4ffa4e8e9ae5ceaa52897027
      formName1: 合同类型-${getTimeStamp_ms()}
      db_name: contract_analysis
      orgGid1: 693110cc535a4212a99c1ef79f0b6e1a

- test:
    name: 保存台账-提取方式选择合同类型AI智能提取
    api: api/contract-ledger/save-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: [$categoryId1]
      conditions: []
      extractType: 2
      fieldIds: [$fieldId1]
      formName: $formName1
      ledgerTemplateMapping: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - formId1: content.data

- test:
    name: 台账列表
    api: api/contract-ledger/form-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId1
      categoryIds: []
      extractType: 2
      fomName: ""
      matching: $formId1
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.conditionMappings.0.conditionId", $categoryId1]
      - ne: ["content.data.list.0.conditionMappings.0.conditionName", null]

- test:
    name: 运行台账
    api: api/contract-ledger/rerun-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取台账进度
    api: api/contract-ledger/get-form-progress.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ge: ["content.data.completed", 0]

- test:
    name: 停止台账运行
    api: api/contract-ledger/stop-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - contained_by: ["content.code", [0,*********]]
      - contained_by: ["content.message", ["成功","参数错误: 已经运行完毕，无法停止，您可以修改设置重新运行"]]

- test:
    name: 查询台账
    api: api/contract-ledger/get-form-detail.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.conditionMappings.0.conditionId", $categoryId1]
      - eq: ["content.data.fields.0.fieldId", $fieldId1]

- test:
    name: 修改台账-修改合同类型和提取字段
    api: api/contract-ledger/update-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: [$categoryId2]
      conditions: []
      extractType: 2
      fieldIds: [$fieldId2]
      formId: $formId1
      formName: $formName1
      ledgerTemplateMapping: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 台账列表
    api: api/contract-ledger/form-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: $categoryId2
      categoryIds: []
      extractType: 2
      fomName: ""
      matching: $formId1
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.conditionMappings.0.conditionId", $categoryId2]
      - ne: ["content.data.list.0.conditionMappings.0.conditionName", null]

- test:
    name: 查询台账
    api: api/contract-ledger/get-form-detail.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.conditionMappings.0.conditionId", $categoryId2]
      - eq: ["content.data.fields.0.fieldId", $fieldId2]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 关闭台账
    api: api/contract-ledger/close-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除台账
    api: api/contract-ledger/delete-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      sql1: "SELECT form_id FROM contract_analysis.form where tenant_gid='$orgGid1' and form_name='$formName1' limit 1;"
      formId: ${select_sql($sql1, $db_name)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
