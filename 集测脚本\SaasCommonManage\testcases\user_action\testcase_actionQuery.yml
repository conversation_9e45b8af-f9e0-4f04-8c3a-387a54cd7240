- config:
    name: 查询用户行为
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 查询用户行为--个人oid
    api: api/user_action/action_query.yml
    variables:
      accountId: 565a742760cc485185bbd3cfc1e47e80
      action: lansheng_test
    validate:
      - eq: [content.code, 0]
      - eq: [content.message, 成功]

- test:
    name: 用户的行为不存在
    api: api/user_action/action_query.yml
    variables:
      accountId: 565a742760cc485185bbd3cfc1e47e80
      action: aaa_testsss
    validate:
      - eq: [content.code, 0]
      - eq: [content.message, 成功]


- test:
    name: 查询PC端登录页的banner
    api: api/footstone-user-api/loginPageChineseList.yml
    variables:
      appId: ${ENV(appid)}
    validate:
      - eq: [content.code, 0]
      - eq: [content.message, 成功]