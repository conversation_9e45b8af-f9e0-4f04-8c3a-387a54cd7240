- config:
    name: 获取橱窗下商品信息
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}

- test:
    name: 企业下获取橱窗下商品信息-成功
    api: api/saas-common/bills-commodity-packages.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      json:
        {
          "productId": "64"
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.commodityDetailList", []]

- test:
    name: 个人下获取橱窗下商品信息-成功
    api: api/saas-common/bills-commodity-packages.yml
    variables:
      tenantId: $accountId1
      operatorId: $accountId1
      json:
        {
          "productId": "64"
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.commodityDetailList", []]

- test:
    name: 获取橱窗下商品信息-productId不存在
    api: api/saas-common/bills-commodity-packages.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      json:
        {
          "productId": "000"
        }
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "参数错误: 对应的商品不存在"]

- test:
    name: 获取橱窗下商品信息-productCode不存在
    api: api/saas-common/bills-commodity-packages.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      json:
        {
          "productCode": "test"
        }
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "参数错误: 对应的商品不存在"]

- test:
    name: 获取橱窗下商品信息-productId为空
    api: api/saas-common/bills-commodity-packages.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      json:
        {
          "productId": ""
        }
    validate:
      - eq: ["content.code", *********]
#      - eq: ["content.message", "参数错误: productId 和 productCode 不能同时为空"]