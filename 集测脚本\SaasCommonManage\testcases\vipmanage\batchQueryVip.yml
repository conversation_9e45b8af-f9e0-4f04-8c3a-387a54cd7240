- config:
    name: 批量获取用户会员及会员功能列表
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      accountId1: ${ENV(accountId1)}
      accountId5: ${ENV(accountId5)}


- test:
    name: 批量获取用户会员及会员功能列表-userIdList为空
    api: api/vipmanage/batchQueryVip.yml
    variables:
      operatorId: $accountId1
      userIdList: []
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 用户列表不能为空]


- test:
    name: 批量获取用户会员及会员功能列表-userId不存在
    api: api/vipmanage/batchQueryVip.yml
    variables:
      operatorId: $accountId1
      userIdList: ["123"]
    validate:
      - contained_by: ["content.code", [********,********]]      #这里的错误码和错误信息取决于一个后端的鉴权配置开关
      - contained_by: ["content.message", ["账号不存在或已注销","检测到您不是部分企业的成员，请联系企业管理员加入企业。"]]


- test:
    name: 批量获取用户会员及会员功能列表-不查会员功能列表
    api: api/vipmanage/batchQueryVip.yml
    variables:
      operatorId: $accountId1
      userIdList: ["${ENV(orgId1)}","${ENV(orgId4)}"]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.functions", []]
      - eq: ["content.data.1.functions", []]


- test:
    name: 批量获取用户会员及会员功能列表-查会员功能列表
    api: api/vipmanage/batchQueryVip.yml
    variables:
      operatorId: $accountId1
      userIdList: ["${ENV(orgId1)}","${ENV(orgId4)}"]
      withFuncs: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.0.functions", []]
      - ne: ["content.data.1.functions", []]
- test:
    name: 批量获取用户会员及会员功能列表-查会员功能列表2-过期
    api: api/vipmanage/batchQueryVip.yml
    variables:
      operatorId: $accountId5
      userIdList: ["${ENV(orgId5)}"]
      withFuncs: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.0.functions", []]
#      - ne: ["content.data.1.functions", []]

