- config:
    name: 流程体验签署发起
#    base_url: ${ENV(contract_manager_url)}
    variables:
      name1: 直接发起-${getTimeStamp()}-不指定签署位置
      accountId1: ${ENV(mx_accountId)}
      tenantId: ${ENV(gray_user_oid)}
      orgId1: ${ENV(mx_orgId)}
      account1: ${ENV(mx_account)}
      accountName1: 明绣
      orgName1: esigntest明绣测试1205
      contentMd5: m49PzxWxAG8sMwNkZrlJDw==
      convert2Pdf: false
      fileName: "test.pdf"
      fileSize: 359815
      resolveForm: false
      saasbiz: saas_biz
      sql1: update saas_biz.account_novice_operate set status = 0 where account_oid = '8723abe263f142009d98471ce7a70bd8' and name = 'complete_experience_start';



- test:
    name: 文件直传创建文件1
    api: api/Novice-task/Novice-task-list/get_UploadUrl.yml
    variables:
      tenantId: $tenantId
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileId1: content.data.fileId
      - uploadUrl1: content.data.uploadUrl


- test:
    name: 上传文件1
    api: api/Novice-task/Novice-task-list/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl1
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}


- test:
    name: 直接发起-不指定签署位置
    api: api/Novice-task/Novice-task-list/processes_experience_start.yml
    setup_hooks:
      - ${hook_db_data($sql1, $saasbiz)}
    variables:
      - taskName: $name1
      - fileId: $fileId1
      - role1: "3"
      - role2: "3"
      - scene: 1
      - instances1: [
      {
        account: $account1,
        accountOid: $accountId1,
        accountName: $accountName1,
        accountRealName: true,
        comment: "",
        subjectId: $accountId1,
        subjectName: $accountName1,
        subjectRealName: true,
        subjectType: 0,
        preFillValues: null,
        subTaskName: ""
      }
      ]
      - instances2: [
      {
        account: $account1,
        accountOid: $accountId1,
        accountName: $accountName1,
        accountRealName: true,
        comment: "",
        subjectId: $orgId1,
        subjectName: $orgName1,
        subjectRealName: true,
        subjectType: 1,
        preFillValues: null,
        subTaskName: ""
      }
      ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.processId", null]


#- config:
#    name:  发起签署
##    base_url: ${ENV(contract_manager_url)}
#    variables:
#      - name: 发起签署-${getTimeStamp()}-批量催办
#
#
#- test:
#    name: 发起签署
#    api: api/Novice-task/Novice-task-list/processes_experience_start.yml
#    variables:
#      - taskName: $name