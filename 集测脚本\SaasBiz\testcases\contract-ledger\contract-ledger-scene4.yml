- config:
    name: 台账串场景-我收到的合同AI智能提取
    variables:
      orgId1: 67e1edfc41be46dfa824c8af811ca844
      accountId1: ${ENV(mx_accountId)}
      fieldId1: 8f8cb5c240ae45858b74d29ce58ee97e
      db_name: contract_analysis
      formName1: 我收到的-${getTimeStamp_ms()}
      orgGid1: 693110cc535a4212a99c1ef79f0b6e1a

- test:
    name: 归档系统条件
    api: api/auto-archive/system-rule-config.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      bizType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - sys_fieldId1: content.data.0.fieldId

- test:
    name: 保存台账-提取方式选择我收到的合同AI智能提取
    api: api/contract-ledger/save-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      conditionIds: []
      conditions:
        [
        {
          "fieldId":"$sys_fieldId1",
          "key":"${getTimeStamp_ms()}",
          "value":[
            "我收到的"
          ],
          "startNum":"",
          "endNum":"",
          "childOperatorType":2,
          "matchType":3,
          "operatorType":1,
          "childOperators":[
          {
            "operatorType":2,
            "conditionParams":"[\"我收到的\"]"
          }
          ],
          "fieldType":3
        }
        ]
      extractType: 4
      fieldIds: [$fieldId1]
      formName: $formName1
      ledgerTemplateMapping: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - formId1: content.data

- test:
    name: 台账列表
    api: api/contract-ledger/form-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      categoryId: ""
      categoryIds: []
      extractType: 4
      fomName: ""
      matching: $formId1
      pageNum: 1
      pageSize: 10
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.conditionMappings", null]

- test:
    name: 运行台账
    api: api/contract-ledger/rerun-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取台账进度
    api: api/contract-ledger/get-form-progress.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ge: ["content.data.completed", 0]

- test:
    name: 停止台账运行
    api: api/contract-ledger/stop-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - contained_by: ["content.code", [0,*********]]
      - contained_by: ["content.message", ["成功","参数错误: 已经运行完毕，无法停止，您可以修改设置重新运行"]]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 查询台账
    api: api/contract-ledger/get-form-detail.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.fields.0.fieldId", $fieldId1]
      - eq: ["content.data.conditions.0.fieldId", $sys_fieldId1]
      - eq: ["content.data.conditions.0.childOperators.0.conditionParams", '["我收到的"]']
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 关闭台账
    api: api/contract-ledger/close-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 删除台账
    api: api/contract-ledger/delete-form.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      sql1: "SELECT form_id FROM contract_analysis.form where tenant_gid='$orgGid1' and form_name='$formName1' limit 1;"
      formId: ${select_sql($sql1, $db_name)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
