- config:
    name: 申请试用及查询申请记录
    variables:
      orgId1: ${ENV(orgId1)}
      accountId1: ${ENV(accountId1)}
      accountId2: ${ENV(accountId4)}
      functionCode1: info_collect
      db_name1: saas_base_manage


- test:
    name: 申请试用-functionCode为空
    api: api/apply-trial/apply-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      functionCode: null
      phone: null
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", "must not be blank"]

- test:
    name: 申请试用-functionCode不存在
    api: api/apply-trial/apply-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      functionCode: 123
      phone: null
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 申请的功能不存在]

- test:
    name: 申请试用-phone格式不正确
    api: api/apply-trial/apply-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: $functionCode1
      phone: 123
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 请输入正确的手机号]

- test:
    name: 申请试用-操作人未绑定登录和联系手机号，未传手机号
    api: api/apply-trial/apply-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: $functionCode1
      phone: null
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.needPhone", true]

- test:
    name: 申请试用-操作人未绑定登录和联系手机号，传入的手机号未校验通过
    api: api/apply-trial/apply-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: $functionCode1
      phone: ***********
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 未校验的手机号]

- test:
    name: 发送手机验证码-成功
    api: api/phone-code/sendCode.yml
    variables:
      operatorId: $accountId1
      bizTag: apply-trial
      phone: ***********
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - authCodeId1: content.data.authCodeId

- test:
    name: 验证手机验证码-code错误校验不通过
    api: api/phone-code/authCode.yml
    variables:
      operatorId: $accountId1
      authCodeId: $authCodeId1
      code: 111111
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pass", false]

- test:
    name: 验证手机验证码-code正确校验通过
    api: api/phone-code/authCode.yml
    variables:
      operatorId: $accountId1
      authCodeId: $authCodeId1
      code: 123456
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pass", true]

- test:
    name: 验证手机验证码-authCodeId已使用过
    api: api/phone-code/authCode.yml
    variables:
      operatorId: $accountId1
      authCodeId: $authCodeId1
      code: 123456
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 请重新获取验证码]

- test:
    name: 申请试用-操作人未绑定登录和联系手机号，申请成功
    api: api/apply-trial/apply-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionCode: $functionCode1
      phone: ***********
      sql1: "delete from saas_base_manage.apply_trial_log where func_code='$functionCode1' and person_oid='$accountId1';"
    setup_hooks:
        - ${hook_db_data($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询最近申请记录-accountId1
    api: api/apply-trial/recentApplyRecord.yml
    variables:
      operatorId: $accountId1
      funcCode: $functionCode1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.phone", "***********"]
      - eq: ["content.data.enterprise", esigntest明绣测试1205]

- test:
    name: 申请试用-操作人已绑定登录或联系手机号，申请成功
    api: api/apply-trial/apply-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      functionCode: $functionCode1
      phone: null
      sql1: "delete from saas_base_manage.apply_trial_log where func_code='$functionCode1' and person_oid='$accountId2';"
    setup_hooks:
      - ${hook_db_data($sql1, $db_name1)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 申请试用-操作人已绑定登录或联系手机号，30天内已申请过
    api: api/apply-trial/apply-trial.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      functionCode: $functionCode1
      phone: null
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 您的试用申请已提交]

- test:
    name: 查询最近申请记录-accountId2
    api: api/apply-trial/recentApplyRecord.yml
    variables:
      operatorId: $accountId2
      funcCode: $functionCode1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.phone", "***********"]
      - eq: ["content.data.enterprise", esigntest明绣测试1205]
