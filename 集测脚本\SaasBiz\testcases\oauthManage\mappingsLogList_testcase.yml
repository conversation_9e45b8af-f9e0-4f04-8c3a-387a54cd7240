- config:
    name: SaaS页面接口-查询授权日志-分页接口
    variables:
      - appId_0: "${ENV(authAppId_1)}"
      - psnId: "${ENV(auth_admin_oid)}"
      - psnId2: "${ENV(auth_psnId)}"
      - orgId: "${ENV(auth_orgId)}"

- test:
    name: appId不存在
    variables:
      - headers: "${gen_headers_rest($orgId,$psnId)}"
      - pageNum: 1
      - pageSize: 10
      - appId: "aaaa"
    api: api/oauthManage/mappingsLogList_api.yml
    validate:
        - contains: [ "content.message", "appId无效" ]
        - ne: [ "content.code", 0 ]

- test:
    name: 操作员在系统中不存在
    variables:
      - psn_str: "aaaa"
      - headers: "${gen_headers_rest($orgId,$psn_str)}"
      - pageNum: 1
      - pageSize: 10
      - appId: $appId_0
    api: api/oauthManage/mappingsLogList_api.yml
    validate:
        - contains: [ "content.message","账号不存在或已注销"]

- test:
    name: 操作员无权限
    variables:
        - headers: "${gen_headers_rest($orgId,$psnId2)}"
        - pageNum: 1
        - pageSize: 10
        - appId: $appId_0
    api: api/oauthManage/mappingsLogList_api.yml
    validate:
        - ne: [ "content.code", 0 ]
        - eq: [ "content.message", "用户无操作权限" ]

- test:
    name: 查询企业应用授权记录-企业不存在
    variables:
        - headers: "${gen_headers_rest($org_str,$psnId)}"
        - pageNum: 1
        - pageSize: 10
        - appId: $appId_0
        - org_str: "aaa"
    api: api/oauthManage/mappingsLogList_api.yml
    validate:
        - ne: [ "content.code", 0 ]
        - eq: [ "content.message", "账号不存在或已注销" ]

- test:
    name: 查询企业应用授权记录-操作员非法
    variables:
        - psn_str: "aaaa"
        - headers: "${gen_headers_rest($orgId,$psn_str)}"
        - pageNum: 1
        - pageSize: 10
        - appId: $appId_0
    api: api/oauthManage/mappingsLogList_api.yml
    validate:
        - ne: [ "content.code", 0 ]
        - eq: [ "content.message", "账号不存在或已注销" ]


- test:
    name: 查询企业用户-应用授权记录日志
    variables:
        - headers: "${gen_headers_rest($orgId,$psnId)}"
        - pageNum: 1
        - pageSize: 10
        - appId: $appId_0
    api: api/oauthManage/mappingsLogList_api.yml
    validate:
        - ne: ["content.data.authName",null]
        - eq: ["content.data.authOid",$orgId]
        - ne: ["content.data.appName",null]
        - ne: ["content.data.appLogo",null]
        - eq: ["content.data.appId",$appId]
        - gt: ["content.data.count",0]
        - ne: ["content.data.authLogList.0.operatorLoginAccount",null]
        - contains: ["content.data.authLogList.0","afterScopes"]
        - contains: ["content.data.authLogList.0","beforeScopes"]
        - ne: ["content.data.authLogList.0.operatorOid",null]
        - ne: ["content.data.authLogList.0.operatorName",null]
        - ne: ["content.data.authLogList.0.operatorSourceDesc",null]
#        - contains: ["content.data.authLogList.0","operatorTime"]
        - ne: ["content.data.authLogList.0.operatorSourceDesc",null]


- test:
    name: 查询个人用户-应用授权记录日志
    variables:
        - headers: "${gen_headers_rest($psnId2,$psnId2)}"
        - pageNum: 1
        - pageSize: 10
        - appId: $appId_0
    api: api/oauthManage/mappingsLogList_api.yml
    validate:
        - ne: ["content.data.authName",null]
        - eq: ["content.data.authOid",$psnId2]
        - ne: ["content.data.appName",null]
        - ne: ["content.data.appLogo",null]
        - eq: ["content.data.appId",$appId]
        - ge: ["content.data.count",0]
        - ne: ["content.data.authLogList.0.operatorLoginAccount",null]
        - contains: ["content.data.authLogList.0","afterScopes"]
        - contains: ["content.data.authLogList.0","beforeScopes"]
        - ne: ["content.data.authLogList.0.operatorOid",null]
        - ne: ["content.data.authLogList.0.operatorName",null]
        - ne: ["content.data.authLogList.0.operatorSourceDesc",null]
#        - contains: ["content.data.authLogList.0","operatorTime"]
        - ne: ["content.data.authLogList.0.operatorSourceDesc",null]




