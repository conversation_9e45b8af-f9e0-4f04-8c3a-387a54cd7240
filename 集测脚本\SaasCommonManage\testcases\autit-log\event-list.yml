- config:
    name: 查询事件列表
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 查询事件列表--事件存在
    api: api/audit-log/event-list.yml
    variables:
      module: 'saas_contract_template'
#    validate:
#       - eq: ["content.code", 0]
#       - eq: ["content.message", 成功]
#       - eq: ["content.data.eventList" , ["停用模板","新增模板","启用模板","删除模板","修改模板"]]

- test:
    name: 查询事件列表--事件不存在
    api: api/audit-log/event-list.yml
    variables:
      module: '不存在的模块'
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#      - eq: ["content.data.eventList" , [ ]]

- test:
    name: 查询事件列表--模块为空
    api: api/audit-log/event-list.yml
    variables:
      module: ''
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#      - eq: ["content.data.eventList" , null]



