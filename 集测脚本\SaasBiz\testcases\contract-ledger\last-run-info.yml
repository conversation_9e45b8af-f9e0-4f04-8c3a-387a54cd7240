- config:
    name: 查询最后一次台账运行信息
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      orgId2: 913ed9f6ac5742a999ca8c002775f490
      formId1: aa11061a77f44b019146d6575a05e912
      formId2: 9f690f83c1044836a97aa638607c237b

- test:
    name: 查询最后一次台账运行信息
    api: api/contract-ledger/last-run-info.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询最后一次台账运行信息-台账id不存在
    api: api/contract-ledger/last-run-info.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      formId: 123
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 台账不存在]

- test:
    name: 查询最后一次台账运行信息-操作人不是企业成员
    api: api/contract-ledger/last-run-info.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      formId: $formId1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 企业成员不存在]