- config:
    name: 合同比对嵌入-签署
    variables:
      taskName1: 直接发起-${getTimeStamp()}
      accountId1: ${ENV(mx_accountId3)}
      account1: ${ENV(mx_account3)}
      accountName1: ${ENV(mx_accountName3)}
      orgId2: ${ENV(mx_orgId5)}
      orgName2: ${ENV(mx_orgName5)}
      contentType1: application/pdf
      orgId1: ${ENV(mx_orgId5)}
      approvalTemplateName1: 直接发起的合同审批-${getTimeStamp()}
      fileName1: "test.pdf"
      fileSize1: 99580
      contentMd5_1: 2FwUG2Zm3BtLVOuuYCPh7g==
      filePath1: "data/test.pdf"
      accountId2: ${ENV(mx_accountId3)}
      account2: ${ENV(mx_account3)}
      accountName2: ${ENV(mx_accountName3)}
      accountId3: ${ENV(mx_accountId3)}
- test:
    name: 创建合同比对
    api: api/contract_compare/embed-compare-create.yml
    variables:
      fileHash1: ""
      fileName1: "test3.pdf"
      fileId1: "b309d53ec81d4b8d95118bdb66bae9db"
      processId1: ac358ea5fef24c9180a8564eb0c47d03
      fileHash2: ""
      fileName2: "test4.pdf"
      fileId2: "4b58fd9a2302441586986588d2aea64d"
      processId2: ac358ea5fef24c9180a8564eb0c47d03
      tenantId: $orgId2
      operatorId: $accountId1
    extract:
      compareId: json.data.compareId
    validate:
      - eq: [ "status_code",200 ]
      - eq: [ "json.message",成功 ]
      - ne: [ "json.data.compareId",null ]

- test:
    name: 查询合同比对状态-比对成功
    api: api/contract_compare/embed-compare-query-status.yml
    variables:
      compareId: $compareId
      tenantId: $orgId2
      operatorId: $accountId1
    validate:
      - eq: [ "status_code",200 ]
      - eq: [ "json.message",成功 ]
      - eq: [ "json.data.compareStatus",3 ]
