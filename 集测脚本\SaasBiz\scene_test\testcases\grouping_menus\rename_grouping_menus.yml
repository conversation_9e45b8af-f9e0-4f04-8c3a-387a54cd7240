- config:
    name: 企业合同分类重命名
    variables:
      orgId1: 26df081c55b74a4f8f080310c3252aaa
      accountId1: 7c7eefda74ff44c4b42daa833901bd78   #企业1的普通成员
      account1: ***********
      accountName1: 测试四十二
      accountId2: 8a17e0b48e5948738a990d09b190d45b   #企业1的管理员
      roleId1: f5a7e1e2280f417598caa0bc012323cb      #企业1的企业合同的重命名分类角色id
      menuName1: 分类${getTimeStamp_ms()}-新1
      menuName2: 分类${getTimeStamp_ms()}-改1
      menuName3: 分类${getTimeStamp_ms()}-改2
      menuName4: 分类${getTimeStamp_ms()}-改3
      roleId4: 7bac6ad3bc6d4872aaeeaced47a0fcd1      #企业1的普通成员角色id

- test:
    name: 造数据-新增分类
    api: api/grouping_menus/add_grouping_menus.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
      name: $menuName1
      parentMenuId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - menuId1: content.data

- test:
    name: 1分类重命名-企业管理员默认有权限
    api: api/grouping_menus/rename_grouping_menus.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      name: $menuName2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取当前用户的目录列表-企业管理员查看
    api: api/grouping_menus/grouping_menus_list.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.menuList.0.menuName", $menuName2]

- test:
    name: 2分类重命名-企业普通成员无权限
    api: api/grouping_menus/rename_grouping_menus.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId1
      name: $menuName3
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 获取可操作的角色列表
    api: api/grouping_permission/roleByAuthorizer.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
      authorizer: $accountId1
      authorizeType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - roleList1: content.data

- test:
    name: 添加目录用户及授权-给accountId1分配menuId1的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId2: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId2",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 3分类重命名-企业普通成员无删除权限
    api: api/grouping_menus/rename_grouping_menus.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId1
      name: $menuName3
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同重命名分类的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1,$roleId4]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 4分类重命名-企业普通成员有全局企业合同重命名分类的权限和menuId1的查看权限
    api: api/grouping_menus/rename_grouping_menus.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId1
      name: $menuName3
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取当前用户的目录列表-企业管理员查看
    api: api/grouping_menus/grouping_menus_list.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.menuList.0.menuName", $menuName3]

- test:
    name: 添加目录用户及授权-取消accountId1的menuId1查看权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId2: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId2",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 5分类重命名-企业普通成员无查看权限
    api: api/grouping_menus/rename_grouping_menus.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId1
      name: $menuName4
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同重命名分类的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId4]
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 添加目录用户及授权-给accountId1分配menuId1的分类管理员的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId3: ${getValue($roleList1, roleKey, MENU_ADMIN, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId3",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 6分类重命名-企业普通成员有menuId1的分类管理员的权限
    api: api/grouping_menus/rename_grouping_menus.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId1
      name: $menuName4
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取当前用户的目录列表-企业管理员查看
    api: api/grouping_menus/grouping_menus_list.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.menuList.0.menuName", $menuName4]

- test:
    name: 清理数据-删除分类
    api: api/grouping_menus/delete_grouping_menus.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
