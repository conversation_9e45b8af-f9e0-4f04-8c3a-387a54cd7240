- config:
    name: 查询是否展示合同比对按钮
    variables:
      taskName1: 直接发起-${getTimeStamp()}
      accountId1: ${ENV(mx_accountId3)}
      account1: ${ENV(mx_account3)}
      accountName1: ${ENV(mx_accountName3)}
      orgId2: ${ENV(mx_orgId5)}
      orgName2: ${ENV(mx_orgName5)}
      contentType1: application/pdf
      orgId1: ${ENV(mx_orgId5)}
      approvalTemplateName1: 直接发起的合同审批-${getTimeStamp()}
      fileName1: "test.pdf"
      fileSize1: 99580
      contentMd5_1: 2FwUG2Zm3BtLVOuuYCPh7g==
      filePath1: "data/test.pdf"
      accountId2: ${ENV(mx_accountId3)}
      account2: ${ENV(mx_account3)}
      accountName2: ${ENV(mx_accountName3)}
      accountId3: ${ENV(mx_accountId3)}
- test:
    name: 查询是否展示合同比对按钮
    api: api/contract_compare/embed-compare-check-show.yml
    variables:
        processId: ac358ea5fef24c9180a8564eb0c47d03
        tenantId: $orgId2
        operatorId: $accountId1
    validate:
        - eq: [ "status_code",200 ]
        - eq: [ "json.message",成功 ]
        - eq: [ "json.data.show",true ]
