- config:
    name: 保存数据范围
#    base_url: ${ENV(contract_manager_url)}

- test:
    name: 保存数据范围
    api: api/flowTemplates/save-template-auth-config.yml
    variables:
        configValue: "force"
        authId: "e911556aee074ce69d60a29a0e402fbb"
        tenantId: "9eef7945e968420294dbdd30a89a12a5"
        operatorId: "a690089d48a14707a1cb78c453a0d991"
        deleteFlowTemplateIdList: []
        insertFlowTemplateIdList: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]