- config:
    name: 查询用户是否支持某项功能
    variables:
      orgId1: 634f518fe6834f21a0a226ff326e9770
      accountId1: 276b5a8f2a964142bb1ebcf0dd82bf72

- test:
    name: 查询用户是否支持某项功能-functionKey不存在
    api: api/function/function-support.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      functionKey: 123
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]