 #查询编号规则详情
    request:
        url: ${ENV(saas_common_manage_url)}/getByRuleId/tenantGid/ruleId?tenantGid=$tenantGid&ruleId=$ruleId
        method: GET
        headers:
            Content-Type: application/json
#            X-Tsign-Open-Operator-Id: $operatorid
#            X-Tsign-Open-Tenant-Id: $tenantid
#            X-Tsign-Open-App-Id: ${ENV(appid)}
#            X-Tsign-Service-Group: ${ENV(Groupid)}
#            X-Tsign-Open-Auth-Mode: simple
#
#        json:
#          {
#              "alias": $alias,
#              "bottomText": $bottomText,
#              "color": $color,
#              "horizontalText": $horizontalText,
#              "opacity": $opacity,
#              "style": $style,
#              "surroundTextInner": $surroundTextInner,
#              "templateType": $templateType,
#              "widthHeight": $widthHeight
#          }
