- config:
    name: 获取用户会员及会员功能列表
#    base_url: ${ENV(saas_common_manage_url)}


- test:
    name: 获取用户会员及会员功能列表-userId不存在
    api: api/vipmanage/queryVip.yml
    variables:
      userId: "123"
      operatorId: 8723abe263f142009d98471ce7a70bd8
    validate:
      - eq: ["content.code", 70000003]
      - eq: ["content.message", 账号不存在或已注销]


- test:
    name: 获取用户会员及会员功能列表-不查会员功能列表
    api: api/vipmanage/queryVip.yml
    variables:
      userId: ${ENV(orgId1)}
      operatorId: 8723abe263f142009d98471ce7a70bd8
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.functions", []]


- test:
    name: 获取用户会员及会员功能列表-查会员功能列表
    api: api/vipmanage/queryVip.yml
    variables:
      userId: ${ENV(orgId1)}
      operatorId: 8723abe263f142009d98471ce7a70bd8
      withFuncs: true
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.functions", []]
