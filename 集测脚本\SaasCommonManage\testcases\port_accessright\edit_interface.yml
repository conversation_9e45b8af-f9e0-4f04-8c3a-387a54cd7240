- config:
    name: 批量更新接口转发及参数替换配置状态
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      accountId: ${ENV(accountId1)}


- test:
    name: 批量更新接口转发及参数替换配置状态-成功
    api: api/port_accessright/edit_interface.yml
    variables:
      "id": 1046
      "body": {  }
      "header": {  }
      "query": {  }
      "desc": 测试
      "forwardMethod": ""
      "forwardType": 1
      "forwardUri": /Integration/text/port
      "method": ""
      "tags": ""
      "uri": /Integration/text/port
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
