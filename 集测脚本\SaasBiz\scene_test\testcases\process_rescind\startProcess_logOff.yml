- config:
    name: 对于签署方已经注销的场景，发起合同解约
    variables:
      - accountId: ${ENV(mx_accountId1)}
        orgId: ${ENV(mx_orgId1)}
        #原流程
        processId: 76fbdd4076df4363a37f9607786e4340
        fileKey: ${ENV(filekeynew)}


#- test:
#    name: 解约流程发起页面原流程信息回填
#    api: api/contract-manage/rescindBackfill.yml
#    variables:
#      processId: $processId
#      tenantId: $orgId
#      operatorId: $accountId
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#    extract:
#      - participants1: content.data.participants

- test:
    name: 发起解约流程
    api: api/contract_manage/startProcess.yml
    variables:
      tenantId: $orgId
      operatorId: $accountId
      businessType: 1
      ccs: []
      files: [
        {
          "fileId": "564fb153de414a96b9098813257d14d6",
          "fileType": 1,
          "fileKey": $fileKey,
          "fileName": "ReportViewPdf-解除协议.pdf",
          "from": 2,
          "fileSecret": false,
          "supportReplace": false,
          "supportDelete": false,
          "originalFileId": "",
          "order": 1
        }
             ]
      flowTemplateId: null
      initiatorAccountId: 0bc26b27ede64cc48e7a800f4c5cbac7
      originProcessId: $processId
      participants:
         [
          {
            "participantLabel": "签署方1",
            "role": "3",
            "instances": [
              {
                "ccs": [

                ],
                "subTaskBizId": "",
                "subTaskName": "",
                "accountName": "测试注销帐号",
                "accountRealName": false,
                "subjectType": 0,
                "subjectRealName": false,
                "subTaskBizType": "",
                "account": "***********",
                "subjectName": "测试注销帐号"
              }
            ],
            "willTypes": [

            ],
            "sealType": "0",
            "forceReadEnd": false,
            "type": 1,
            "signOrder": 1,
            "forceReadTime": "",
            "fillOrder": 0,
            "signSealType": "",
            "participantSubjectType": 0,
            "signSeal": "",
            "participantMode": "",
            "roleSet": 1
          },
          {
            "signRequirements": "1",
            "participantLabel": "签署方2",
            "role": "3",
            "instances": [
              {
                "accountOid": "0bc26b27ede64cc48e7a800f4c5cbac7",
                "ccs": [

                ],
                "subTaskBizId": "",
                "subTaskName": "",
                "accountName": "测试吴四",
                "accountRealName": true,
                "subjectType": 1,
                "subjectId": "08486d61823d4c2086122b26fb3e615a",
                "subjectRealName": true,
                "subTaskBizType": "",
                "account": "***********",
                "subjectName": "esigntest自动化测试企业11"
              }
            ],
            "willTypes": [

            ],
            "forceReadEnd": false,
            "type": 1,
            "signOrder": 1,
            "forceReadTime": "",
            "fillOrder": 0,
            "signSealType": 1,
            "participantSubjectType": 1,
            "signSeal": "",
            "participantMode": "",
            "roleSet": 1
          }
         ]
      rescindFileIds: ["8157d427cf144c31bf358e2109f93101"]
      scene: 1
      taskName: "ReportViewPdf-解除协议"
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - processId: content.data.processId
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 撤回流程
    api: api/contract_manage/process_operation/process_batchRevoke.yml
    variables:
      processIds: [$processId]
      accountId: $accountId
      subjectId: $orgId
      reason: 撤回流程测试
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]