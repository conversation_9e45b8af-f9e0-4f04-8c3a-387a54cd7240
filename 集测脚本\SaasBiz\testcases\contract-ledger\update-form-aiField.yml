- config:
    name: 修改台账ai提取字段
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}
      orgId2: 913ed9f6ac5742a999ca8c002775f490
      formId1: aa11061a77f44b019146d6575a05e912
      fieldId1: 3b721649b8cb40dd87ff55e6d981157c

- test:
    name: 修改台账ai提取字段-提取字段为空
    api: api/contract-ledger/update-form-aiField.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fieldIds: []
      formId: $formId1
      openLedger: false
      runModel: TRIAL
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", 字段id列表不能为空]

- test:
    name: 修改台账ai提取字段-提取字段不存在
    api: api/contract-ledger/update-form-aiField.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fieldIds: [123]
      formId: $formId1
      openLedger: false
      runModel: TRIAL
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 字段不全]

- test:
    name: 修改台账ai提取字段-台账id为空
    api: api/contract-ledger/update-form-aiField.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fieldIds: [$fieldId1]
      formId: null
      openLedger: false
      runModel: TRIAL
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", 表单id不能为空]

- test:
    name: 修改台账ai提取字段-台账id不存在
    api: api/contract-ledger/update-form-aiField.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fieldIds: [$fieldId1]
      formId: 123
      openLedger: false
      runModel: TRIAL
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 台账不存在]

- test:
    name: 修改台账ai提取字段-runModel非法
    api: api/contract-ledger/update-form-aiField.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      fieldIds: [123]
      formId: 123
      openLedger: false
      runModel: AAA
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", 未知的运行模式]

- test:
    name: 修改台账ai提取字段-操作人不是企业成员
    api: api/contract-ledger/update-form-aiField.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      fieldIds: [123]
      formId: 123
      openLedger: false
      runModel: TRIAL
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 企业成员不存在]