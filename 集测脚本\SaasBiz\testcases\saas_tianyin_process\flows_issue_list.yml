- config:
    name: 获取用户可出证的天印流程列表
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}

- test:
    name: 获取用户可出证的天印流程列表-Y
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: 10
      pageNum: 1
      appId: ""
      flowId: ""
      flowName: ""
      signerKeyWord: ""
      initiatorKeyWord: ""
      createFrom: ""
      createEnd: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户可出证的天印流程列表-根据appId精确查询
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: 10
      pageNum: 1
      appId: ${ENV(sasstianyin_appId)}
      flowId: ""
      flowName: ""
      signerKeyWord: ""
      initiatorKeyWord: ""
      createFrom: ""
      createEnd: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户可出证的天印流程列表-根据flowId精确查询
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: 10
      pageNum: 1
      appId: ""
      flowId: ${ENV(saaatianyin_flowId)}
      flowName: ""
      signerKeyWord: ""
      initiatorKeyWord: ""
      createFrom: ""
      createEnd: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户可出证的天印流程列表-根据flowName模糊查询
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: 10
      pageNum: 1
      appId: ""
      flowId: ""
      flowName: ${ENV(sasstianyin_flowName)}
      signerKeyWord: ""
      initiatorKeyWord: ""
      createFrom: ""
      createEnd: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户可出证的天印流程列表-根据signerKeyWord模糊查询
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: 10
      pageNum: 1
      appId: ""
      flowId: ""
      flowName: ""
      signerKeyWord: ${ENV(sasstianyin_signerKeyWord)}
      initiatorKeyWord: ""
      createFrom: ""
      createEnd: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户可出证的天印流程列表-根据initiatorKeyWord模糊查询
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: 10
      pageNum: 1
      appId: ""
      flowId: ""
      flowName: ""
      signerKeyWord: ""
      initiatorKeyWord: ${ENV(sasstianyin_initiatorKeyWord)}
      createFrom: ""
      createEnd: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户可出证的天印流程列表-根据createFrom匹配
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: 10
      pageNum: 1
      appId: ""
      flowId: ""
      flowName: ""
      signerKeyWord: ""
      initiatorKeyWord: ""
      createFrom: ${ENV(sasstianyin_createFrom)}
      createEnd: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户可出证的天印流程列表-根据createEnd匹配
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: 10
      pageNum: 1
      appId: ""
      flowId: ""
      flowName: ""
      signerKeyWord: ""
      initiatorKeyWord: ""
      createFrom: ""
      createEnd: ${ENV(sasstianyin_createEnd)}
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"

- test:
    name: 获取用户可出证的天印流程列表-subjectId不匹配
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: 10
      pageNum: 1
      appId: ""
      flowId: ""
      flowName: ""
      signerKeyWord: ""
      initiatorKeyWord: ""
      createFrom: ""
      createEnd: ""
      subjectId: ${ENV(sasstiany_accountId)}
      code: ********
      message: "企业不存在或已注销"

- test:
    name: 获取用户可出证的天印流程列表-subjectId为企业
    api: api/saas_tianyin_process/flows_issue_list.yml
    variables:
      pageSize: 10
      pageNum: 1
      appId: ""
      flowId: ""
      flowName: ""
      signerKeyWord: ""
      initiatorKeyWord: ""
      createFrom: ""
      createEnd: ""
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"
