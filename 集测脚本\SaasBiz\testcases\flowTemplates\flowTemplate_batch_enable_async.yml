- config:
    name: 流程模板批量开启-异步
    variables:
        - code: 0
        - message: 成功

- test:
      name: 流程模板批量开启-异步
      variables:
        flowTemplateIds: "05fc50e4ed5047e59c1cb64e26e964d4"
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
        operatorId : "a690089d48a14707a1cb78c453a0d991"

      api: api/flowTemplates/flowTemplate_batch_enable_async.yml
      validate:
          - eq: ["content.code", 0]
          - eq: ["content.message", "成功"]