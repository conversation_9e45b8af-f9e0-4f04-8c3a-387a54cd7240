- config:
    name: 子企业+批量导入+开启份数共享
    variables:
      tenantId_1: ${ENV(mx_orgId1)}                   # 当前空间主体
      operatorId_1: ${ENV(ls_oid)}                    # 操作人oid
      initiatorAccountId: ${ENV(ls_oid)}              # 发起人的oid
      account_1: ${ENV(mx_account1)}                  # 参与人手机号
      account_2: ${ENV(ls_account4)}
      account_3: ${ENV(mx_account2)}
      accountOid_1: ${ENV(mx_accountId1)}             # 参与人oid
      accountOid_2: ${ENV(ls_oid4)}
      accountOid_3: ${ENV(mx_accountId2)}
      accountName_1: ${ENV(mx_accountName1)}           # 参与人姓名
      accountName_2: ${ENV(ls_accountName4)}
      accountName_3: ${ENV(mx_accountName2)}
      subject_1: ${ENV(mx_orgId1)}                     # 参与人主体oid
      subject_2: ${ENV(ls_oid4)}
      subject_3: ${ENV(mx_accountId2)}
      subjectName_1: ${ENV(mx_orgName1)}               # 参与人的主体名称
      subjectName_2: ${ENV(ls_accountName4)}
      subjectName_3: ${ENV(mx_accountName2)}
      css_account_1: ${ENV(mx_account1)}                  # 抄送人的手机号
      css_accountOid_1: ${ENV(mx_accountId1)}             # 抄送人oid
      css_account_name_1: ${ENV(mx_accountName1)}         # 抄送人姓名
      css_subject_1: ${ENV(mx_orgId1)}                    # 抄送人主体oid
      css_subject_name_1: ${ENV(mx_orgName1)}             # 抄送人主体名称
      fileId_1: 5db38690a35a4eca868002c7e9484b22          # 模板的文件id
      file_name1: 劳动合同.pdf                            # 文件名称
      taskName: 企业_批量导入_跳过填写_合同审批${generate_random_str(3)}
      flowTemplateId: ${ENV(ls_flowTemplateId_11)}         # 流程模板ID
      approveTemplateId: ""                               # 合同审批ID
      contentType_1: application/octet-stream
      contentMd5_1: uwBuk4oaYsk1AN7C+Uhnog==
      fileName_1: "签署方2-批量导入跳过填写.xlsx"
      fileSize_1: 11460
      filePath_1: "data/签署方2-批量导入跳过填写.xlsx"
      
      
- test:
    name: 获取流程发起的详情信息
    api: api/contract_manage/processDetail.yml
    variables:
      tenantId: $tenantId_1
      operatorId: $operatorId_1
      flowTemplateId: $flowTemplateId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - participantId1: content.data.participants.0.participantId
      - participantId2: content.data.participants.1.participantId

- test:
    name: 下载批量导入表格
    api: api/multiagent-excel/excel_download.yml
    variables:
      tenantId: $tenantId_1
      json:
        {
          "participantLabel": "签署方2",
          "flowTemplateId": $flowTemplateId,
          "participantSubjectType": 0,
          "excelTemplateType": 1,
          "excelInitDataList": [],
          "files": [{
                      "fileId": $fileId_1,
                      "fileName": $fileName_1,
                      "contractNoType": 1
                    }]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      
- test:
    name: 获取文档fileKey直传地址
    api: api/footstone-doc/getFileKeyUploadUrl.yml
    variables:
      tenantId: $tenantId_1
      accountId: $operatorId_1
      contentType: $contentType_1
      contentMd5: $contentMd5_1
      convert2Pdf: true
      fileName: $fileName_1
      fileSize: $fileSize_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - fileKey_1: content.data.fileKey
      - uploadUrl_1: content.data.uploadUrl


- test:
    name: 上传文件到oss
    api: api/footstone-doc/upload_oss.yml
    variables:
      uploadUrl: $uploadUrl_1
      filePath: $filePath_1
      contentType: $contentType_1
      contentMd5: $contentMd5_1
    validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}
      
- test:
    name: 解析批量导入表格
    api: api/multiagent-excel/excel_parse.yml
    variables:
      tenantId: $tenantId_1
      participantLabel: 签署方2
      flowTemplateId: $flowTemplateId
      participantSubjectType: 0
      excelTemplateType: 1
      fileKey: $fileKey_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - preFillValues_1: content.data.processInstances.0.preFillValues
      - preFillValues_2: content.data.processInstances.1.preFillValues
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 校验预填内容完整性
    api: api/footstone-doc/checkPreFill.yml
    variables:
      tenantId: $tenantId_1
      cooperationerId: $participantId1
      values: [$preFillValues_1, $preFillValues_2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.needFill", True]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}
      
- test:
    name: 使用模板发起(已跳过填写)
    api: api/contract_manage/startProcess.yml
    variables:
      tenantId: $tenantId_1
      operatorId: $operatorId_1
      businessType: 0
      ccs: [
        {
          "account": $css_account_1,
          "accountOid": $css_accountOid_1,
          "accountName": $css_account_name_1,
          "accountNick":"",
          "accountRealName":true,
          "comment":"",
          "subjectId": $css_subject_1,
          "subjectName": $css_subject_name_1,
          "subjectRealName":true,
          "subjectType": 1
        }
      ]
      files:
        [
        {
          "fileId": $fileId_1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $file_name1,
          "from":2,                 #文件来自 1-模板文件 2-合同文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: $flowTemplateId
      initiatorAccountId: $initiatorAccountId
      approveTemplateId: $approveTemplateId
      signEndTime: null
      fileEndTime: null
      signValidityConfig:
        {
         "validityType":3,
         "durationYear":null,
         "durationMonth":null,
         "durationDay":null
        }
      fileValidityConfig:
        {
         "validityType":3,
         "durationYear":null,
         "durationMonth":null,
         "durationDay":null
        }
      participants:
        [
        {
          "participantSubjectType": 1,
          "role": "1,3",
          "sealType": null,
          "signRequirements": "1",
          "roleSet":1,
          "type": 1,
          "signSealType": 1,
          "fillOrder": 1,
          "signOrder": 1,
          "participantLabel":"签署方1",
          "participantId": $participantId1,
          "instances":[
          {
            "account": $account_1,
            "accountOid": $accountOid_1,
            "accountName": $accountName_1,
            "accountRealName":true,
            "comment":"",
            "subjectId": $subject_1,
            "subjectName": $subjectName_1,
            "subjectRealName":true,
            "subjectType": 1,
            "preFillValues": null,
            "subTaskName":""
          }
          ],
          "willTypes":[
            "FACE",
            "CODE_SMS",
            "EMAIL",
            "SIGN_PWD"
            ]
        },
        {
          "participantSubjectType": 0,
          "role": "1,3",
          "sealType": "0,1,2",
          "signRequirements": null,
          "roleSet":1,
          "type": 1,
          "signSealType": 1,
          "fillOrder": 2,
          "signOrder": 1,
          "participantLabel":"签署方2",
          "participantId": $participantId2,
          "instances":[
              {
                "account": $account_2,
                "accountOid": $accountOid_2,
                "accountName": $accountName_2,
                "accountRealName":true,
                "comment":"",
                "subjectId": $subject_2,
                "subjectName": $subjectName_2,
                "subjectRealName":true,
                "subjectType": 0,
                "preFillValues": $preFillValues_1,
                "subTaskName":""
              },
              {
                "account": $account_3,
                "accountOid": $accountOid_3,
                "accountName": $accountName_3,
                "accountRealName":true,
                "comment":"",
                "subjectId": $subject_3,
                "subjectName": $subjectName_3,
                "subjectRealName":true,
                "subjectType": 0,
                "preFillValues":$preFillValues_2,
                "subTaskName":""
              }
          ],
          "willTypes":[]
        }
        ]
      scene: 2
      taskName: $taskName
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.flowId", null]
      - ne: ["content.data.groupId", null]
    extract:
      - groupId: content.data.groupId
      - traceId1: headers.X-Tsign-Trace-Id
    teardown_hooks:
      - ${hook_sleep_n_secs(10)}
      
- test:
    name: 查询任务中心的任务列表
    api: api/saas-common-manage/taskList.yml
    variables:
      tenantId: $tenantId_1
      accountId: $operatorId_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}