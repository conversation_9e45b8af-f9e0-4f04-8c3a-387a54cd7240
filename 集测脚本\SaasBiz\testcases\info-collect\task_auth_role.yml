- config:
    name: 查询可授权的角色(已完成)

- test:
    name: 查询可授权的角色
    api: api/info-collect/task_auth_role.yml
    variables:
        tenantId: "d9ec3169a6e04d148e5a8cc08ab3c13d "
        operatorId: "d9ec3169a6e04d148e5a8cc08ab3c13d"
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]
        - ne: ["content.data.authRoles", null]

- test:
    name: 采集任务批量编辑授权
    api: api/info-collect/task_batch_edit_auth.yml
    variables:
        appId: ${ENV(appid)}
        tenantId: "c76c2f6f136d4997aaccf30f1f767f59"
        operatorId: "565a742760cc485185bbd3cfc1e47e80"
        json:
          {
              "authList": [ {
                  "authorizedEntityId": "675c4018dd544f70a5aea3be8c26127f",
                  "authorizedEntityIdAlias": "e5af807524cd4e6386e059581ead4c9d",
                  "authorizedEntityName": "测试张三",
                  "authorizedEntityType": 1,
                  "operateType": 0,
                  "roleId": "ddc17ea7e9d545ef92bb6c19f7ba12d5"
              } ],
              "formTaskRelationMap": {
                  "form934116732642430976": [ ],
                  "form933822801010069504": [ ]
              }
          }
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]
        - eq: ["content.data", null]