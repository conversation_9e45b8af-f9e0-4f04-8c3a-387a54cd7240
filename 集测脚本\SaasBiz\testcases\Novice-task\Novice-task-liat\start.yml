- config:
    name: 新注册用户体验发起



- test:
    name: 新注册用户体验发起-发起主体为空
    api: api/Novice-task/Novice-task-list/start.yml
    variables:
      oid: 345267
      platform: 7
      subjectOid:
    validate:
      - eq: ["content.code", 31202000]
      - contains: ["content.message", 发起主体不能为空]

- test:
    name: 新注册用户体验发起-发起人为空
    api: api/Novice-task/Novice-task-list/start.yml
    variables:
      oid:
      platform: 7
      subjectOid: 345267
    validate:
      - eq: ["content.code", 31202000]
      - contains: ["content.message", 发起人不能为空]

- test:
    name: 新注册用户体验发起-发起人未实名
    api: api/Novice-task/Novice-task-list/start.yml
    variables:
      oid: 4a273b62dcd0481d9fd3c7453163c930
      platform: 7
      subjectOid: 4a273b62dcd0481d9fd3c7453163c930
    validate:
      - eq: ["content.code", 312030010]
      - contains: ["content.message", 请先完成个人实名认证后再发起签署]

- test:
    name: 新注册用户体验发起-发起人未实名
    api: api/Novice-task/Novice-task-list/start.yml
    variables:
      oid: 4a273b62dcd0481d9fd3c7453163c930
      platform: 7
      subjectOid: 4a273b62dcd0481d9fd3c7453163c930
    validate:
      - eq: ["content.code", 312030010]
      - contains: ["content.message", 请先完成个人实名认证后再发起签署]

- test:
    name: 新注册用户体验发起-发起人已实名走个人发起
    api: api/Novice-task/Novice-task-list/start.yml
    variables:
      oid: 628717beb21744e7a9cd6f617e4f0b62
      platform: 7
      subjectOid: 628717beb21744e7a9cd6f617e4f0b62
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

- test:
    name: 新注册用户体验发起-发起人已实名走企业发起但不属于该企业
    api: api/Novice-task/Novice-task-list/start.yml
    variables:
      oid: 628717beb21744e7a9cd6f617e4f0b62
      platform: 7
      subjectOid: 5add8dece1e941f999e589223117b75b
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

- test:
    name: 新注册用户体验发起-发起人已实名走企业发起且属于该企业
    api: api/Novice-task/Novice-task-list/start.yml
    variables:
      oid: 628717beb21744e7a9cd6f617e4f0b62
      platform: 7
      subjectOid: 6f1daa33dc6447de8b82f75793327148
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

- test:
    name: 新注册用户体验发起-发起人已实名走企业发起但企业未实名
    api: api/Novice-task/Novice-task-list/start.yml
    variables:
      oid: 628717beb21744e7a9cd6f617e4f0b62
      platform: 7
      subjectOid: c660b5de518742228371b77e78cc76ca
    validate:
      - eq: ["content.code", 312030010]
      - contains: ["content.message", 实名认证后再发起签署]
