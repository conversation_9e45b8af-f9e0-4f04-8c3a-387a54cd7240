- config:
    name: 创建合同摘要


- test:
    name: 创建合同摘要-成功
    api: api/contract-summary/create-summary-info.yml
    variables:
      "fileId": "49a76364896a4664a28e018528c63174"
      "menuId": "sys_menuId"
      "processId": "53073a756cd9499bb7c9dc1ee1740236"
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", 成功]

- test:
    name: 创建合同摘要-文件ID为空
    api: api/contract-summary/create-summary-info.yml
    variables:
      "fileId": ""
      "menuId": "sys_menuId"
      "processId": "53073a756cd9499bb7c9dc1ee1740236"
    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: 文件ID不能为空"]

- test:
    name: 创建合同摘要-文件ID不存在
    api: api/contract-summary/create-summary-info.yml
    variables:
      "fileId": "111"
      "menuId": "sys_menuId"
      "processId": "53073a756cd9499bb7c9dc1ee1740236"
    validate:
        - eq: ["content.code", 10000001]
        - eq: ["content.message", "合同不存在"]

- test:
    name: 创建合同摘要-合同id为空
    api: api/contract-summary/create-summary-info.yml
    variables:
      "fileId": "49a76364896a4664a28e018528c63174"
      "menuId": "sys_menuId"
      "processId": ""
    validate:
        - eq: ["content.code", 120000004]
        - eq: ["content.message", "参数错误: 合同ID不能为空"]

- test:
    name: 创建合同摘要-合同id错误
    api: api/contract-summary/create-summary-info.yml
    variables:
      "fileId": "49a76364896a4664a28e018528c63174"
      "menuId": "sys_menuId"
      "processId": "111"
    validate:
        - eq: ["content.code", 120000501]
        - eq: ["content.message", "流程不存在"]

