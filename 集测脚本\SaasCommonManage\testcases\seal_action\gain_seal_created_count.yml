- config:
    name: 获取对应类型印章的已创建数量
#    base_url: ${ENV(saas_common_manage_url)}



- test:
      name: 获取对应类型印章的已创建数量-oid不存在
      api: api/seal/gain_seal_created_count.yml
      variables:
        sealType: "ORGAN_SEAL"
        tenantid: ""
      validate:
          - eq: [ "content.code", 10000005 ]
          - eq: [ "content.message", "open user不存在. ouid:null"]


- test:
      name: 获取对应类型印章的已创建数量-成功（类型是ORGAN_SEAL）
      api: api/seal/gain_seal_created_count.yml
      variables:
        sealType: "ORGAN_SEAL"
        tenantid: "391d0b87c284491eba58aba6dc505c86 "
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", "成功"]


- test:
      name: 获取对应类型印章的已创建数量-成功（类型是LEGAL_SEAL）
      api: api/seal/gain_seal_created_count.yml
      variables:
        sealType: "LEGAL_SEAL"
        tenantid: "391d0b87c284491eba58aba6dc505c86 "
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", "成功"]


