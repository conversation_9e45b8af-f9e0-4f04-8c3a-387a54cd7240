- config:
    name:  获取用户新手任务配置



- test:
    name: 获取用户新手任务配置-个人获取成功
    api: api/Novice-task/Novice-task-list/novices_config.yml
    variables:
      accountId: "bdda6dd3cef34b678d12e3a960da7f1d"
      subjectType: "0"
      cacheKey: "contract-manager:core:service:noviceChannelConfig:**********:1:0"
    #validate:
     # - eq: ["content.code", 0]
      #- contains: ["content.message", 成功]
    #setup_hooks:
     # - ${redis_val($cacheKey)}


- test:
    name: 获取用户新手任务配置-企业获取成功
    api: api/Novice-task/Novice-task-list/novices_config.yml
    variables:
      accountId: "bdda6dd3cef34b678d12e3a960da7f1d"
      subjectType: "1"
      cacheKey: "contract-manager:core:service:noviceChannelConfig:**********:1:1"
    #validate:
     # - eq: ["content.code", 0]
      #- contains: ["content.message", 成功]
    #setup_hooks:
     # - ${redis_val($cacheKey)}


- test:
    name: 获取用户新手任务配置-id错误
    api: api/Novice-task/Novice-task-list/novices_config.yml
    variables:
      accountId: "bdda6dd3cef34b678d12e3a960da7f1"
      subjectType: ""
    #validate:
     # - eq: ["content.code", *********]
      #- contains: ["content.message", 账号不存在或已注销]