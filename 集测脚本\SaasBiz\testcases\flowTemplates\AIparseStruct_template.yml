- config:
    name: AI识别流程模板中文件的控件（完成）
#    base_url: ${ENV(contract_manager_url)}
    variables:
        - code: 0
        - message: 成功

- test:
      name: AI识别流程模板的控件-flowTemplateId为空
      variables:
        flowTemplateId: " "
        fileId: ${ENV(fileId_AI_template)}
        tenantId: ${ENV(orgid21)}
        operatorId : "c15702b5eae0426d81ba70e58e90fcad"

      api: api/flowTemplates/AIparseStruct_template.yml
      validate:
          - eq: ["content.code", 120000506]
          - eq: ["content.message", "flowTemplateId不能为空"]

- test:
      name: AI识别流程模板的控件-flowTemplateId不存在
      variables:
        flowTemplateId: "xxxx618noexit"
        fileId: ${ENV(fileId_AI_template)}
        tenantId: ${ENV(orgid21)}
        operatorId : "c15702b5eae0426d81ba70e58e90fcad"

      api: api/flowTemplates/AIparseStruct_template.yml
      validate:
          - contained_by: ["content.code", [120000402,312050000]]
          - eq: ["content.message", "流程模板不存在"]

- test:
      name: AI识别流程模板的控件-非当前企业下的flowTemplateId
      variables:
        flowTemplateId: ${ENV(mx_templateId1)}
        fileId: ${ENV(fileId_AI_template)}
        tenantId: ${ENV(orgid21)}
        operatorId : "c15702b5eae0426d81ba70e58e90fcad"

      api: api/flowTemplates/AIparseStruct_template.yml
      validate:
          - contained_by: ["content.code", [120000403,312050001]]
          - eq: ["content.message", "流程模板不属于该账号"]
#
#- test:
#      name: AI识别流程模板的控件-已删除的flowTemplateId
#      variables:
#        flowTemplateId: ${ENV(flowTemplateId_delete)}
#        fileId: ${ENV(fileId_AI_template)}
#        tenantId: ${ENV(orgid21)}
#        operatorId : "c15702b5eae0426d81ba70e58e90fcad"
#
#      api: api/flowTemplates/AIparseStruct_template.yml
#      validate:
#          - eq: ["content.code", 120000402]
#          - eq: ["content.message", "流程模板不存在"]

- test:
      name: AI识别流程模板的控件-fileId为空
      variables:
        flowTemplateId: ${ENV(flowTemplateId_AI_template)}
        fileId: " "
        tenantId: ${ENV(orgid21)}
        operatorId : "c15702b5eae0426d81ba70e58e90fcad"

      api: api/flowTemplates/AIparseStruct_template.yml
      validate:
          - contained_by: ["content.code", [120000406,312050005]]
          - eq: ["content.message", "流程模板中不存在该文件"]

- test:
      name: AI识别流程模板的控件-fileId不存在
      variables:
        flowTemplateId: ${ENV(flowTemplateId_AI_template)}
        fileId: "xxxxxnoistfileid"
        tenantId: ${ENV(orgid21)}
        operatorId : "c15702b5eae0426d81ba70e58e90fcad"

      api: api/flowTemplates/AIparseStruct_template.yml
      validate:
          - contained_by: ["content.code", [120000406,312050005]]
          - eq: ["content.message", "流程模板中不存在该文件"]

- test:
      name: AI识别流程模板的控件-非当前模板下的fileId
      variables:
        flowTemplateId: ${ENV(flowTemplateId_AI_template)}
        fileId: ${ENV(fileId_yucheng)}
        tenantId: ${ENV(orgid21)}
        operatorId : "c15702b5eae0426d81ba70e58e90fcad"

      api: api/flowTemplates/AIparseStruct_template.yml
      validate:
          - contained_by: ["content.code", [120000406,312050005]]
          - eq: ["content.message", "流程模板中不存在该文件"]


- test:
      name: AI识别流程模板的控件-正常获取控件（file里有能识别的下划线）
      variables:
        flowTemplateId: ${ENV(flowTemplateId_AI_template)}
        fileId: ${ENV(fileId_AI_template)}
        tenantId: ${ENV(orgid21)}
        operatorId : "c15702b5eae0426d81ba70e58e90fcad"

      api: api/flowTemplates/AIparseStruct_template.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
          - eq: ["content.data.structComponents.0.type", 1]

- test:
      name: AI识别流程模板的控件-正常获取控件（file里没有能识别的下划线）
      variables:
        flowTemplateId: ${ENV(flowTemplateId_AI_none)}
        fileId: ${ENV(fileId_AI_none)}
        tenantId: ${ENV(orgid21)}
        operatorId : "c15702b5eae0426d81ba70e58e90fcad"

      api: api/flowTemplates/AIparseStruct_template.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
