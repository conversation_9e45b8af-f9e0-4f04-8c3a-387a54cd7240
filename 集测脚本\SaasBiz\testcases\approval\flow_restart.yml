- config:
    name: 发起人重新发起审批


- test:
    name: 发起人重新发起审批--不是审批中
    api: api/approval/flow_restart.yml
    variables:
      operatorId: 291e8283e53f4402baf85956d3b7f7b3
      tenantId: 60b8595f05444c7c981de67eb7540856
      approvalId : "c2efb02302c241cf94c0df67cfb22a60"
      approvalType: 2
      approvalTemplateId: "AT70537fc4a5f24a8ab4e357d812f153f5"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 1100000014]
      - eq: ["content.message", 流程不在审批中]

- test:
    name: 发起人重新发起审批--审批模板不存在
    api: api/approval/flow_restart.yml
    variables:
      operatorId: 291e8283e53f4402baf85956d3b7f7b3
      tenantId: 60b8595f05444c7c981de67eb7540856
      approvalId : "c2efb02302c241cf94c0df67cfb22a60"
      approvalType: 2
      approvalTemplateId: "123456"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 110000001]
      - eq: ["content.message", 审批流模板不存在]