- config:
    name: 企业合同分类新增和查看
    variables:
      orgId1: 26df081c55b74a4f8f080310c3252aaa
      accountId1: 7c7eefda74ff44c4b42daa833901bd78   #企业1的普通成员
      account1: ***********
      accountName1: 测试四十二
      accountId2: 8a17e0b48e5948738a990d09b190d45b   #企业1的管理员
      menuName1: 分类${getTimeStamp_ms()}-新1
      menuName2: 分类${getTimeStamp_ms()}-新2
      menuName3: 分类${getTimeStamp_ms()}-新3
      menuName4: 分类${getTimeStamp_ms()}-新4
      menuName5: 分类${getTimeStamp_ms()}-新5
      roleId1: 945d9871a3084985b5c138b6b21b7aa1      #企业1的企业合同的新增分类角色id
      roleId2: 12dbe76a96b84c79be552075ed034e4b      #企业1的企业合同的查看分类角色id
      roleId5: 7bac6ad3bc6d4872aaeeaced47a0fcd1      #企业1的普通成员角色id

- test:
    name: 新增一级分类-企业普通成员无权限
    api: api/grouping_menus/add_grouping_menus.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      name: $menuName1
      parentMenuId: ""
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 新增一级分类-企业管理员默认有权限
    api: api/grouping_menus/add_grouping_menus.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
      name: $menuName1
      parentMenuId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - menuId1: content.data

- test:
    name: 获取当前用户的目录列表-企业普通成员查看无数据
    api: api/grouping_menus/grouping_menus_list.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.menuList", []]

- test:
    name: 获取可操作的角色列表
    api: api/grouping_permission/roleByAuthorizer.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
      authorizer: $accountId1
      authorizeType: 1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - roleList1: content.data

- test:
    name: 添加目录用户及授权-给accountId1分配menuId1的查看的权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId3: ${getValue($roleList1, roleKey, DOC_SELECT, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId3",
          "operateType":0
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 获取当前用户的目录列表-企业普通成员查看
    api: api/grouping_menus/grouping_menus_list.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.menuList.0.menuId", $menuId1]

- test:
    name: 新增二级分类-企业普通成员无权限
    api: api/grouping_menus/add_grouping_menus.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      name: $menuName2
      parentMenuId: $menuId1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 无操作权限]

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同新增分类的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1,$roleId5]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 新增一级分类-企业普通成员有企业合同新增分类的权限
    api: api/grouping_menus/add_grouping_menus.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      name: $menuName2
      parentMenuId: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - menuId2: content.data

- test:
    name: 获取当前用户的目录列表-企业普通成员查看
    api: api/grouping_menus/grouping_menus_list.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - contained_by: ["content.data.menuList.0.menuId", [$menuId1, $menuId2]]
      - contained_by: ["content.data.menuList.1.menuId", [$menuId1, $menuId2]]

- test:
    name: 新增二级分类-企业管理员默认有权限
    api: api/grouping_menus/add_grouping_menus.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
      name: $menuName3
      parentMenuId: $menuId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - menuId2_1: content.data

- test:
    name: 获取当前用户的目录列表-企业普通成员查看
    api: api/grouping_menus/grouping_menus_list.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
    extract:
      - menuList1: content.data.menuList
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["${getValue($menuList1,menuId,$menuId2,childNode)}", null]

- test:
    name: 新增二级分类-企业普通成员有企业合同新增分类的权限
    api: api/grouping_menus/add_grouping_menus.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      name: $menuName4
      parentMenuId: $menuId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - menuId1_1: content.data

- test:
    name: 获取当前用户的目录列表-企业普通成员查看
    api: api/grouping_menus/grouping_menus_list.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
    extract:
      - menuList2: content.data.menuList
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["${getValue($menuList2,menuId,$menuId1,childNode)}", null]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同新增分类的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId5]
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 新增二级分类-企业普通成员是menuId2的创建人默认有分类管理员的权限
    api: api/grouping_menus/add_grouping_menus.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
      name: $menuName5
      parentMenuId: $menuId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - menuId2_2: content.data

- test:
    name: 添加目录用户及授权-取消accountId1的menuId1查看权限
    api: api/grouping_menus/grouping_menus_adduser.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
      roleId4: ${getValue($roleList1, roleKey, MENU_ADMIN, id)}
      accountList:
        [
        {
          "authorizeType":1,
          "email":"",
          "mobileNo":"$account1",
          "name":"$accountName1",
          "oid":"$accountId1",
          "roleId":"$roleId4",
          "operateType":1
        }
        ]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 更新成员所有信息-给accountId1设置全局企业合同查看分类的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId2,$roleId5]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 获取当前用户的目录列表-企业普通成员查看
    api: api/grouping_menus/grouping_menus_list.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId1
    extract:
      - menuList3: content.data.menuList
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["${get_list_len($menuList3)}", 2]

- test:
    name: 更新成员所有信息-取消accountId1的全局企业合同查看分类的权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      orgId: $orgId1
      accountId: $accountId1
      operatorId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId5]
      memberName: ""
      revokeRoleIds: [$roleId2]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 获取当前用户的目录列表-企业管理员查看
    api: api/grouping_menus/grouping_menus_list.yml
    variables:
      tenantId: $orgId1
      accountId: $accountId2
    extract:
      - menuList4: content.data.menuList
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["${get_list_len($menuList4)}", 2]

- test:
    name: 清理数据-删除分类1
    api: api/grouping_menus/delete_grouping_menus.yml
    variables:
      menuId: $menuId1
      tenantId: $orgId1
      accountId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 清理数据-删除分类2
    api: api/grouping_menus/delete_grouping_menus.yml
    variables:
      menuId: $menuId2
      tenantId: $orgId1
      accountId: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
