- config:
    name: 会员版本及投放角色调整对banner历史投放数据checkcase
    variables:
      - bannerName: 集测banner
      - bannerId: 7026
      - accountId: ${ENV(accountId6)}
      - orgId: ${ENV(orgId6)}
      - noticeType: 1
      - clientVersionCode: 500


- test:
    name: 查询pc企业实名结果页的banner
    api: api/banners/get_banners.yml
    variables:
      operatorId: $accountId
      tenantId: $orgId
      productCode: pc
      areaCode: auth_success_ent
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 查询h5签署完成页的banner
    api: api/banners/get_banners.yml
    variables:
      operatorId: $accountId
      tenantId: $orgId
      productCode: h5
      areaCode: sign_finish_bottom
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 查询微信小程序签署详情页的banner
    api: api/banners/get_banners.yml
    variables:
      operatorId: $accountId
      tenantId: $orgId
      productCode: wechat_applet
      areaCode: wechaFocusOn
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]


- test:
    name: 新增会员版本及指定角色的banner
    api: api/banners/save_banner.yml
    variables:
      "areaCode": home
      "productCode": pc
      "bannerId": $bannerId
      "linkUrl": "http://saas-microfe-h5-workspace-front-saas-iteration-new.projectk8s.tsign.cn/h5-workspace-front/month-report"
      "notifyRoles": ["ADMIN"]
      "conditionType1": "ge"
      conditionType: 0
      configMap: {
        "notifyVersions": "SENIOR"
        }
      hasSatisfy: true
      type: 3
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 上架指定banner
    api: api/banners/active_banner.yml
    variables:
      bannerId: $bannerId
      active: true
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]
- test:
    name: 下架banner
    api: api/banners/active_banner.yml
    variables:
      bannerId: $bannerId
      active: false
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]

