name: 新增接口转发及参数替换配置
variables:
  body: null
  header: null
  query: null
request:
  url: ${ENV(saas_common_manage_url)}/v1/saas-common/urimanage/forwards/add
  method: POST
  headers:
    Content-Type: application/json
    x-timevale-jwtcontent: eyJhbGlhcyI6Iuemu-atjCIsImlkIjoibGlnZSJ9
  json:
    {
      "defender": {
        "body": $body,
        "header": $header,
        "query": $query
      },
      "desc": $desc,
      "forwardMethod": $forwardMethod,
      "forwardType": $forwardType,
      "forwardUri": $forwardUri,
      "method": $method,
      "tags": $tags,
      "uri": $uri
    }
