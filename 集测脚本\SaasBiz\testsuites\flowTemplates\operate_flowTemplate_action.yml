config:
  name: 模板相关


testcases:
  -
    name: 新建模板，禁用，启用，删除
    testcase: testcases/flowTemplates/operate_flowTemplate.yml
  -
    name: 指定位置发起（epaas文档）
    testcase: testcases/flowTemplates/start-set-position-epaas.yml
  -
    name: 创建仅签静态epaas模板，使用模板发起流程
    testcase: testcases/flowTemplates/start-by-jinqian-static-epaas-template.yml
  -
    name: 创建有填写的静态epaas模板，使用模板发起流程并填写
    testcase: testcases/flowTemplates/start-by-tianqian-static-epaas-template.yml
  -
    name: 使用有填写的静态epaas模板批量发起流程
    testcase: testcases/flowTemplates/batch-start-by-tianqian-static-epaas-template.yml
  -
    name: 获取流程模板参与方节点
    testcase: testcases/contract/start-fiextime.yml
