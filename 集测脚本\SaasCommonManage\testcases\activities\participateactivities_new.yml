- config:
    name: 参与活动
#    base_url: ${ENV(saas_common_manage_url)}



- test:
    name: 活动已过期
    api: api/activities/participateactivities_new.yml
    variables:
      account: ***********
      activityCode: fsfsd
      activityShareId: 1704
      certNo: ******************
      certType: CRED_PSN_CH_IDCARD
      name: 王真真

    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 活动已过期，请下次及时参加哦]



- test:
    name: 已参与过该活动
    api: api/activities/participateactivities_new.yml
    variables:
      account: ***********
      activityCode: 1223
      activityShareId: 1704
      certNo: ******************
      certType: CRED_PSN_CH_IDCARD
      name: 王真真

    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 你已参与过此活动]


- test:
    name: 不支持个人参与
    api: api/activities/participateactivities_new.yml
    variables:
      account: ***********
      activityCode: 1224
      activityShareId: 1704
      certNo: ******************
      certType: CRED_PSN_CH_IDCARD
      name: 王真真

    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 该活动不支持个人用户参与]



- test:
    name: 缺少activityCode
    api: api/activities/participateactivities_new.yml
    variables:
      account: ***********
      activityCode:
      activityShareId: 1704
      certNo: ******************
      certType: CRED_PSN_CH_IDCARD
      name: 王真真

    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", '参数异常: 无效的活动分享']



- test:
    name: 缺少activityCode和activityShareId
    api: api/activities/participateactivities_new.yml
    variables:
      account: ***********
      activityCode:
      activityShareId:
      certNo: ******************
      certType: CRED_PSN_CH_IDCARD
      name: 王真真

    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", '参数异常: 缺少活动标识']




- test:
    name: 缺少account
    api: api/activities/participateactivities_new.yml
    variables:
      account:
      activityCode: 1212
      activityShareId: 1704
      certNo: ******************
      certType: CRED_PSN_CH_IDCARD
      name: 王真真

    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", '参数异常: 活动参与人账号不能为空']


- test:
    name: 缺少certNo
    api: api/activities/participateactivities_new.yml
    variables:
      account: ***********
      activityCode: 1212
      activityShareId: 1704
      certNo:
      certType: CRED_PSN_CH_IDCARD
      name: 王真真

    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", '参数异常: 参与主体证件号不能为空']



- test:
    name: 缺少certType
    api: api/activities/participateactivities_new.yml
    variables:
      account: ***********
      activityCode: 1212
      activityShareId: 1704
      certNo: ******************
      certType:
      name: 王真真

    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", '参数异常: 参与主体证件类型不能为空']




- test:
    name: 缺少name
    api: api/activities/participateactivities_new.yml
    variables:
      account: ***********
      activityCode: 1212
      activityShareId: 1704
      certNo: ******************
      certType: CRED_PSN_CH_IDCARD
      name:

    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", '参数异常: 参与主体姓名/名称不能为空']
