name: 查询有效关联企业列表
variables:
  appId: ${ENV(appid)}
request:
  url: ${ENV(inner_open_url)}/v2/org-auth-relation/search-effective-auth-relation
  method: GET
  headers: ${gen_headers($appId, X-Tsign-Open-Tenant-Id=$tenantId, X-Tsign-Open-Operator-Id=$operatorId)}
  params:
    pageNum: $pageNum
    pageSize: $pageSize
    searchTenantName: $searchTenantName
    bizScene: $bizScene
    querySelf: $querySelf                  #true查询自己 会把当前企业拼在第一页的第一位
