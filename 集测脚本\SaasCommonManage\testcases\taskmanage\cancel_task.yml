- config:
    name: 取消任务
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      accountId: ${ENV(accountId1)}

- test:
    name: 取消任务-任务不存在
    api: api/taskmanage/cancel_task.yml
    variables:
      taskId: 1
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 任务不存在]

- test:
    name: 取消任务
    api: api/taskmanage/cancel_task.yml
    variables:
      taskId: faf4fc3adda940d2985fa5e832a975e0
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
