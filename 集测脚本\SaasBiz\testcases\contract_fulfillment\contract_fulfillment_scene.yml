- config:
    name: 履约提醒权限场景
    variables:
      app_id: ${ENV(bzq_appid)}
      tenantId: ${ENV(mx_orgId1)}                       # 当前空间主体
      operatorId_admin: ${ENV(mx_accountId1)}                 # 操作人oid
      fulfillment_name: 履约提醒-${today_getTimeStamp_ms()}
      roleId: "2fd8a200697845fb9f9778eecb903c86" #履约规则角色roleId
      operatorId_mem: "813ddace70234148937ad47deadcdfb1" #成员账号oid

- test:
    name: 更新用户履约规则-仅添加、查看权限
    api: api/footstone-user-api/roles_privileges.yml
    variables:
      orgId: $tenantId
      operatorId: $operatorId_admin
      roleId: $roleId
      json:
        {
          "grantPrivilegeList": [
            {
              "targetClassKeyName": "履约管理",
              "targetClassKey": "FULFILLMENT",
              "operationPermit": "ADD",
              "operationPermitName": "添加",
              "operationDisc": null,
              "weight": null,
              "privilegeId": "73846d036c20456abb768cebf77b8a09",
              "objectInfluence": "a912e503d1a84de3a4d62218b111e157"
            },
            {
              "targetClassKeyName": "履约管理",
              "targetClassKey": "FULFILLMENT",
              "operationPermit": "QUERY",
              "operationPermitName": "查看",
              "operationDisc": null,
              "weight": null,
              "privilegeId": "880ac0fae81346168a599148b26575e7",
              "objectInfluence": "a912e503d1a84de3a4d62218b111e157"
            }
          ],
          "revokePrivilegeList": [
            {
              "targetClassKeyName": "履约管理",
              "targetClassKey": "FULFILLMENT",
              "operationPermit": "UPDATE",
              "operationPermitName": "编辑",
              "operationDisc": null,
              "weight": null,
              "privilegeId": "b540daf674c1408e9ffb06e05bb20a53",
              "objectInfluence": "a912e503d1a84de3a4d62218b111e157"
            },
            {
              "targetClassKeyName": "履约管理",
              "targetClassKey": "FULFILLMENT",
              "operationPermit": "DELETE",
              "operationPermitName": "删除",
              "operationDisc": null,
              "weight": null,
              "privilegeId": "f770bfd6da234d48b11533d4b472c51a",
              "objectInfluence": "a912e503d1a84de3a4d62218b111e157"
            }
          ]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: [ status_code, 200 ]

- test:
    name: 成员新增履约规则-成员仅有添加、查看权限，可编辑删除自己创建的规则
    api: api/contract_fulfillment/fulfillment_saveRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId_mem
      json:
        {
          "name": $fulfillment_name,
          "noticeRule": {
            "condition": [
              {
                "key": "createTime",
                "value": 3,
                "middleType": "after"
              }
            ],
            "reminder": {
              "accounts": [

              ],
              "type": [
                "initiator"
              ]
            }
          },
          "status": "enable",
          "type": "collection",
          "typeName": "",
          "scopeType": "all",
          "noticeChannels": [
            "INMAIL"
          ]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: [ status_code, 200 ]
    extract:
      - ruleId_1: content.data


- test:
    name: 修改自己创建的履约规则-有权限返回成功
    api: api/contract_fulfillment/fulfillment_updateRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId_mem
      json:
        {
          "name": "成员自定义规则",
          "noticeRule": {
            "condition": [
              {
                "key": "contractValidity",
                "value": "3",
                "middleType": "before"
              }
            ],
            "reminder": {
              "accounts": [

              ],
              "type": [
                "initiator",
                "signer"
              ]
            }
          },
          "status": "disable",
          "type": "custom",
          "typeName": "自定义提醒",
          "scopeType": "all",
          "noticeChannels": [
            "INMAIL"
          ],
          "ruleId": $ruleId_1
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: [ status_code, 200 ]

- test:
    name: 删除成员自己创建的履约规则-有权限返回成功
    api: api/contract_fulfillment/fulfillment_deleteRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId_mem
      ruleId: $ruleId_1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: [ status_code, 200 ]


- test:
    name: 管理员新增履约规则
    api: api/contract_fulfillment/fulfillment_saveRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId_admin
      json:
        {
          "name": $fulfillment_name,
          "noticeRule": {
            "condition": [
              {
                "key": "createTime",
                "value": 3,
                "middleType": "after"
              }
            ],
            "reminder": {
              "accounts": [

              ],
              "type": [
                "initiator"
              ]
            }
          },
          "status": "enable",
          "type": "collection",
          "typeName": "",
          "scopeType": "all",
          "noticeChannels": [
            "INMAIL"
          ]
        }
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: [ status_code, 200 ]
    extract:
      - ruleId_admin: content.data


- test:
    name: 修改其它人创建的履约规则-无权限
    api: api/contract_fulfillment/fulfillment_updateRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId_mem
      json:
        {
          "name": "测试",
          "noticeRule": {
            "condition": [
              {
                "key": "contractValidity",
                "value": "3",
                "middleType": "before"
              }
            ],
            "reminder": {
              "accounts": [

              ],
              "type": [
                "signer",
                "initiator"
              ]
            }
          },
          "status": "disable",
          "type": "expire",
          "typeName": "到期提醒",
          "scopeType": "all",
          "noticeChannels": [
            "INMAIL"
          ],
          "ruleId": $ruleId_admin
        }
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", "用户无操作权限"]
      - eq: [ status_code, 200 ]

- test:
    name: 删除其它人创建的履约规则-无权限
    api: api/contract_fulfillment/fulfillment_deleteRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId_mem
      ruleId: $ruleId_admin
    validate:
      - eq: [ "content.code", ********* ]
      - eq: [ "content.message", 用户无操作权限 ]
      - eq: [ status_code, 200 ]

- test:
    name: 启用其它人创建履约规则-无权限
    api: api/contract_fulfillment/fulfillment_updateRuleStatus.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId_mem
      json:
        {
          "ruleId": $ruleId_admin,
          "status": "enable"
        }
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 用户无操作权限]
      - eq: [ status_code, 200 ]

- test:
    name: 管理员删除自己的履约规则
    api: api/contract_fulfillment/fulfillment_deleteRule.yml
    variables:
      tenantId: $tenantId
      operatorId: $operatorId_admin
      ruleId: $ruleId_admin
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: [ status_code, 200 ]
