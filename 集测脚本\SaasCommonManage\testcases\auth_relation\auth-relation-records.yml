- config:
    name: 关联企业新增授权记录查询
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      pageSize: 10
      pageNo: 1
      orgName: ${ENV(orgName1)}
      orgGid: ${ENV(orgGid1)}



- test:
    name: 关联企业新增授权记录查询-根据主企业名称查询
    api: api/auth_relation/auth-relation-records.yml
    variables:
      parentName: $orgName
      operatorStartTime: ""
      operatorEndTime: ""
      parentGid: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - ne: ["content.data.authRelationOperatorLogDOS", []]


- test:
    name: 关联企业新增授权记录查询-根据主企业gid查询
    api: api/auth_relation/auth-relation-records.yml
    variables:
      parentName: ""
      operatorStartTime: ""
      operatorEndTime: ""
      parentGid: $orgGid
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - ne: ["content.data.authRelationOperatorLogDOS", []]
    extract:
      - operatorTime: content.data.authRelationOperatorLogDOS.0.operatorTime


- test:
    name: 关联企业新增授权记录查询-根据操作时间查询
    api: api/auth_relation/auth-relation-records.yml
    variables:
      parentName: ""
      operatorStartTime: $operatorTime
      operatorEndTime: $operatorTime
      parentGid: ""
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - gt: ["content.data.total", 0]
      - ne: ["content.data.authRelationOperatorLogDOS", []]
