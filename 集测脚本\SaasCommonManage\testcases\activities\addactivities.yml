- config:
    name: 新增活动
#    base_url: ${ENV(saas_common_manage_url)}

- test:
    name: 新增活动,活动标识已存在
    api: api/activities/addactivities.yml
    variables:
      activityName: '测试测试'
      activityCode: 'testtest'
      presentCode: '1234'
      presentMenuId: '123'
      channelGid: aaa123
      startTime: '1649347200000'
      endTime: '1650124799000'
      pic : 'http://trial-node.oss-cn-hangzhou.aliyuncs.com/upload/c1def064-e518-5272-ae16-a153a760b44a!!4-8.png'
      url : 'https://miniapp-test.tsign.cn/share-guide/testtest/home'
    validate:
      - eq: ["content.code", 70000404]
      - eq: ["content.message", '已存在相同活动标识的其他活动， 请修改活动标识后重新提交']


#- test:
#    name: 新增活动,活动标识不存在
#    api: api/activities/addactivities.yml
#    variables:
#      activityName: '测试测试1'
#      activityCode: 'testtest1'
#      presentCode: '1234'
#      presentMenuId: '123'
#      startTime: '1649347200000'
#      endTime: '1650124799000'
#      pic : 'http://trial-node.oss-cn-hangzhou.aliyuncs.com/upload/c1def064-e518-5272-ae16-a153a760b44a!!4-8.png'
#      url : 'https://miniapp-test.tsign.cn/share-guide/testtest/home'
#    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", '成功']
