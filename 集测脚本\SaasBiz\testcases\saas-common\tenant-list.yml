- config:
    name: 根据租户id查询租户信息,包含已注销的
    variables:
      orgId1: ${ENV(mx_orgId)}
      accountId1: ${ENV(mx_accountId)}

- test:
    name: 根据租户id查询租户信息-查企业
    api: api/saas-common/tenant-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      oidList: [$orgId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.organ", true]

- test:
    name: 根据租户id查询租户信息-查个人
    api: api/saas-common/tenant-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      oidList: [$accountId1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.organ", false]

- test:
    name: 根据租户id查询租户信息-oidList为空
    api: api/saas-common/tenant-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      oidList: []
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
#      - eq: ["content.data", None]

- test:
    name: 根据租户id查询租户信息-oid不存在
    api: api/saas-common/tenant-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      oidList: [123]
    validate:
      - eq: ["content.code", *********]
      - eq: ["content.message", 账号不存在或已注销]

- test:
    name: 根据租户id查询租户信息-oid已注销
    api: api/saas-common/tenant-list.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      oidList: [b6095286db7e4e45b3c09a805d0bc5c1]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
