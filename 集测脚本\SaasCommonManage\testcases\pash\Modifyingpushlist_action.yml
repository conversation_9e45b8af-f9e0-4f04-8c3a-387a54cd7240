- config:
    name: 修改推送名单相关操作
#    base_url: ${ENV(saas_common_manage_url)}



- test:
      name: 增加banner推送名单，addUserIds为空
      api: api/pash/modifyingpush_list.yml
      variables:
        contextId: ${ENV(contextId1)}
        "addUserIds": [""]
        "dataType": 2
        "delUserIds": [ ]
        "type": 1
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", 成功 ]

- test:
      name: 删除banner推送名单，delUserIds为空
      api: api/pash/modifyingpush_list.yml
      variables:
        contextId: ${ENV(contextId1)}
        "addUserIds": []
        "dataType": 2
        "delUserIds": [""]
        "type": 1
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", 成功 ]

- test:
      name: 增加banner推送名单
      api: api/pash/modifyingpush_list.yml
      variables:
        contextId: ${ENV(contextId1)}
        "addUserIds": ["17630942307"]
        "dataType": 2
        "delUserIds": [ ]
        "type": 1
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", 成功 ]

- test:
      name: 删除banner推送名单
      api: api/pash/modifyingpush_list.yml
      variables:
        contextId: ${ENV(contextId1)}
        "addUserIds": []
        "dataType": 2
        "delUserIds": ["17630942307"]
        "type": 1
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", 成功 ]

- test:
      name: 增加弹窗推送名单，addUserIds为空
      api: api/pash/modifyingpush_list.yml
      variables:
        contextId: ${ENV(contextId2)}
        "addUserIds": [""]
        "dataType": 2
        "delUserIds": [ ]
        "type": 1
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", 成功 ]

- test:
      name: 删除弹窗推送名单，delUserIds为空
      api: api/pash/modifyingpush_list.yml
      variables:
        contextId: ${ENV(contextId2)}
        "addUserIds": []
        "dataType": 2
        "delUserIds": [""]
        "type": 1
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", 成功 ]

- test:
      name: 增加弹窗推送名单
      api: api/pash/modifyingpush_list.yml
      variables:
        contextId: ${ENV(contextId2)}
        "addUserIds": ["17630942307"]
        "dataType": 2
        "delUserIds": [ ]
        "type": 1
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", 成功 ]

- test:
      name: 删除弹窗推送名单
      api: api/pash/modifyingpush_list.yml
      variables:
        contextId: ${ENV(contextId2)}
        "addUserIds": []
        "dataType": 2
        "delUserIds": ["17630942307"]
        "type": 1
      validate:
          - eq: [ "content.code", 0 ]
          - eq: [ "content.message", 成功 ]
