- config:
    name: 获取天印流程批量签署列表

- test:
    name: 获取天印流程批量签署列表
    api: api/saas_tianyin_process/get_batchsignLIst.yml
    variables:
      accountId: 03dffec4f1bf45c2998b4023a7ff29f4
      batchSerialId: ""
      flowIds: [ "d711eacaf42147a5964db682bc7b6c1b" ]
      tenantId: 03dffec4f1bf45c2998b4023a7ff29f4
      operator: 03dffec4f1bf45c2998b4023a7ff29f4
    validate:
      - eq: [ content.code, ********* ]
      - eq: [ content.message, 没有可批量签署的流程 ]



- test:
    name: 获取天印流程批量签署列表
    api: api/saas_tianyin_process/get_batchsignLIst.yml
    variables:
      accountId: 03dffec4f1bf45c2998b4023a7ff29f4
      batchSerialId: ""
      flowIds: [ ]
      tenantId: 03dffec4f1bf45c2998b4023a7ff29f4
      operator: 03dffec4f1bf45c2998b4023a7ff29f4
    validate:
      - eq: [ content.code, ********* ]
      - contains: [ content.message, 流程id列表不能为空 ]



- test:
    name: 获取天印流程批量签署列表
    api: api/saas_tianyin_process/get_batchsignLIst.yml
    validate:
      - eq: [ content.code, 0 ]
      - eq: [ content.message, 成功 ]
    extract:
      - batchSerialId: content.data.batchSerialId


- test:
    name: 获取天印流程批量签署列表
    api: api/saas_tianyin_process/get_batchsignLIst.yml
    variables:
      batchSerialId: $batchSerialId
    validate:
      - eq: [ content.code, 0 ]
      - eq: [ content.message, 成功 ]
    extract:
      - flowId: content.data.batchSignList.0.flows.0.flowId


- test:
    name: 获取天印流程批量签署地址
    api: api/saas_tianyin_process/get_batchsignUrl.yml
    variables:
      flowIds:
        - $flowId
    validate:
      - eq: [ content.code, 120000303 ]
      - eq: [ content.message, 当前来源不支持批量签署 ]


- test:
    name: 根据签署序列id获取天印流程批量签署流程信息
    api: api/saas_tianyin_process/get_batchsignInfo.yml
    validate:
      - eq: [ content.code, 120000304 ]
      - contains: [ content.message, 批量签署已超时， 需返回列表页重新发起 ]