config:
  name: 信息采集2.0接口集测

testcases:
  -
    name: 获取某些部门是否为隔离部门
    testcase: testcases/info-collect/checkMenuAuthDeptBelongMultiBiz.yml

#  -
#    name: 批量审核
#    testcase: testcases/info-collect/collect_data_batch_approval.yml

#  -
#    name: 下载导入模版
#    testcase: testcases/info-collect/collect_data_dowload_import_template.yml
  -
    name: 复制表单
    testcase: testcases/info-collect/form_copy.yml

  -
    name: 获取复制表单凭证
    testcase: testcases/info-collect/form_copy_key.yml

#  -
#    name: 创建表单，并删除
#    testcase: testcases/info-collect/form_create.yml

#  -
#    name: 删除表单
#    testcase: testcases/info-collect/form_delete.yml
#
#  -
#    name: 表单详情
#    testcase: testcases/info-collect/form_detail.yml

#  -
#    name: 添加默认表单接口
#    testcase: testcases/info-collect/form_init.yml

#  -
#    name: 查询表单可搜索字段
#    testcase: testcases/info-collect/form_search_field.yml

#  -
#    name: 编辑表单
#    testcase: testcases/info-collect/form_update.yml

  -
    name: 查询已授权的列表
    testcase: testcases/info-collect/task_auth_list.yml

  -
    name: 查询可授权的角色
    testcase: testcases/info-collect/task_auth_role.yml

  -
    name: 编辑授权
    testcase: testcases/info-collect/task_edit_auth.yml

  - name: 信息采集任务的结果页
    testcase: testcases/info-collect/task_result.yml
