- config:
    name: 审查历史操作
    variables:
        appId: "7876701657"
        groupId: "d1c22dbd271b4f5f953af94e9f9c3346"
        fileId: "b41835cb48eb44148e91acf158fdb914"
        recordId2: "167acde8e49347ca93572399164e5bf4"

- test:
    name: 上传文件生成合同审查地址
    api: api/contract-review-part1/review-record/record-create-url.yml
    variables:
        appId: $appId
        fileId: $fileId
    validate:
        - eq: ["content.code", 0]
        - ne: ["content.data.reviewUrl", null]
        - eq: ["content.message", 成功]
- test:
    name: 审查记录列表
    api: api/contract-review-part1/review-record/record-page.yml
    validate:
        - eq: [ "content.code", 0 ]
        - eq: [ "content.message", 成功 ]
    extract:
        recordId1: content.data.inventoryList.0.recordId

- test:
    name: 根据审查记录获取合同审查地址
    api: api/contract-review-part1/review-record/record-review-url.yml
    variables:
        recordId: $recordId1
    validate:
        - eq: [ "content.code", 0 ]
        - ne: [ "content.data.reviewUrl", null ]
        - eq: [ "content.message", 成功 ]
- test:
    name: 根据审查记录下载文件-原文件
    api: api/contract-review-part1/review-record/record-download.yml
    variables:
        "downloadOriginFile": true
        recordId: $recordId2
        "contentDisposition": "attachment"
    validate:
        - eq: [ "content.code", 0 ]
        - eq: [ "content.message", 成功 ]
- test:
    name: 根据审查记录下载文件-批注后文件
    api: api/contract-review-part1/review-record/record-download.yml
    variables:
        "downloadOriginFile": false
        recordId: $recordId2
        "contentDisposition": "attachment"
    validate:
        - eq: [ "content.code", 0 ]
        - eq: [ "content.message", 成功 ]
- test:
    name: 根据审查记录下载文件-没有审查完成的文件
    api: api/contract-review-part1/review-record/record-download.yml
    variables:
        recordId: $recordId1
        "contentDisposition": "attachment"
        "downloadOriginFile": true
    validate:
        - eq: [ "content.code", 120001604 ]
        - eq: [ "content.message", 审查未完成，无法下载 ]

- test:
    name: 删除合同审查记录
    api: api/contract-review-part1/review-record/record-del.yml
    variables:
        recordId: $recordId1
    validate:
        - eq: [ "content.code", 0 ]
        - eq: [ "content.message", 成功 ]


