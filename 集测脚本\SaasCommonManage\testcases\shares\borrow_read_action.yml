- config:
    name: 分享给非流程参与人场景
#    base_url: ${ENV(saas_common_manage_url)}
    variables:
      - accountId1: ${ENV(accountId1)}
      - shareOperateType: BORROW_READ
      - orgId1: ${ENV(orgId1)}
      - shareType: 2
      - resourceType: PROCESS
      - router: share
      - shareEndTime1: ${todayEnd_getTimeStamp_ms()}
      - shareEndTime2: ${today_getTimeStamp_ms()}
      - processId1: ${ENV(processId1)}
      - processId2: ${ENV(processId3)}
      - processId3: ${ENV(processId4)}
      - menuId1: ${ENV(menuId1)}
      - accountId2: ${ENV(accountId2)}
      - account2: ***********
      - accountGid2: 4523a2b90ac24311af0e01f61ac6d6c1
      - accountName2: 谢佳
      - account3: <EMAIL>
      - accountName3: ${getTimeStamp_ms()}
      - accountId3: 847eaf47fd234aff9c0aa0414799fdb5
      - tenantId: ${ENV(orgId1)}


- test:
    name: 经办合同列表已完成的流程进行分享
    api: api/shares/create_share.yml
    variables:
      accountId: $accountId1
      callSorce: CONTRACT
      menuId: ""
      resourceId: $processId2
      subjectId: $orgId1
      shareEndTime: $shareEndTime2
      shareTargets:
        -
          targetOperatorAccount: $account2
          targetOperatorGid: $accountGid2
          targetOperatorName: $accountName2
          targetOperatorOid: $accountId2
          targetSubjectGid: $accountGid2
          targetSubjectOid: $accountId2
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.resourceShareId", null]
      - ne: ["content.data.shareUrl", null]


- test:
    name: 借阅-流程未完成
    api: api/shares/create_share.yml
    variables:
      accountId: $accountId1
      callSorce: CONTRACT
      menuId: ""
      resourceId: $processId1
      subjectId: $orgId1
      shareEndTime: $shareEndTime1
      shareTargets:
        -
          targetOperatorAccount: $account2
          targetOperatorGid: $accountGid2
          targetOperatorName: $accountName2
          targetOperatorOid: $accountId2
          targetSubjectGid: $accountGid2
          targetSubjectOid: $accountId2
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", 未完成的合同状态不允许分享]


- test:
    name: 借阅-当前操作人无权限
    api: api/shares/create_share.yml
    variables:
      accountId: $accountId2
      callSorce: CONTRACT
      menuId: ""
      resourceId: $processId2
      subjectId: $orgId1
      shareEndTime: $shareEndTime1
      shareTargets:
        -
          targetOperatorAccount: $account2
          targetOperatorGid: $accountGid2
          targetOperatorName: $accountName2
          targetOperatorOid: $accountId2
          targetSubjectGid: $accountGid2
          targetSubjectOid: $accountId2
    validate:
      - eq: ["content.code", ********]
      - eq: ["content.message", "您没有该功能的权限，请联系管理员获取"]


- test:
    name: 企业合同待归档列表已完成的流程进行分享
    api: api/shares/create_share.yml
    variables:
      accountId: $accountId1
      callSorce: ORG_WAITING_ARCHIVE
      menuId: ""
      resourceId: $processId3
      subjectId: $orgId1
      shareEndTime: $shareEndTime1
      shareTargets:
        -
          targetOperatorAccount: $account3
          targetOperatorName: $accountName3
          targetOperatorOid: $accountId3
          targetSubjectOid: $accountId3
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.shareUrl", null]
    extract:
      - resourceShareId1: content.data.resourceShareId


- test:
    name: 获取分享记录
    api: api/shares/share_records.yml
    variables:
      status: 1
      resourceId: $processId3
      shareTargetSearch: $accountName3
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.totalSize", 1]
      - eq: ["content.data.recordsResponses.0.resourceShareId", $resourceShareId1]


- test:
    name: 获取分享对象信息
    api: api/shares/getShareTargets.yml
    variables:
      resourceShareId: $resourceShareId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.shareTargetContact", $account3]


- test:
    name: 根据资源分享id获取资源分享信息
    api: api/shares/shareInfo_getByResourceShareId.yml
    variables:
      resourceShareId: $resourceShareId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.resourceId", $processId3]


- test:
    name: 获取资源最终地址-当前操作人不是分享对象
    api: api/shares/getResourceUrl.yml
    variables:
      accountId: $accountId2
      subjectId: $orgId1
      resourceShareId: $resourceShareId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.resourceUrl", null]
      - eq: ["content.data.hasAuth", false]


- test:
    name: 获取资源最终地址-当前操作人非流程参与人,但是分享对象
    api: api/shares/getResourceUrl.yml
    variables:
      accountId: $accountId3
      subjectId: $orgId1
      resourceShareId: $resourceShareId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.resourceUrl", null]
      - eq: ["content.data.hasAuth", true]


- test:
    name: 停止分享资源
    api: api/shares/invalidShare.yml
    variables:
      resourceShareId: $resourceShareId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取资源最终地址-非流程参与人,但是分享对象
    api: api/shares/getResourceUrl.yml
    variables:
      accountId: $accountId2
      subjectId: $orgId1
      resourceShareId: $resourceShareId1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.resourceUrl", null]
      - eq: ["content.data.hasAuth", false]
