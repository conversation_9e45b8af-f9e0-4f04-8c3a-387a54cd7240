- config:
    name: 印章授权相关操作
    variables:
      orgId: "0001095aa4bb4f178dafd418ac274a62"  #企业oid 阮小五测试企业
      adminId: "a46d29697c50498eb9ff75655ba9bad1"  #管理员oid   阮小五
      oid1: "219461ffede84f629235751ca4967b8e" #企业成员  阮小七
      effectiveTime0: "${getTimeStamp()}" #生效开始时间
      expireTime0: "${getTimeStamp()}"  #有效期截止时间
      data: ${ENV(data)}
      refuse_fileId: "66f9b050-588f-4d23-bbee-7751127716c5"
      flowtemplateId: "029626cede054b8a921411541fa35585"  #流程模板
      grantedUserCode1: "912345098765432345"
      grantedUserName1: "esigntest你是真的秀"
      orgId2: "52b72c6d9ac941bebdd0431d97f2f8ab" # esigntest你是真的秀


#创建企业模板印章
- test:
    name: 创建企业模板印章-人事章
    variables:
        tenantid: $orgId
        alias: "企业人事章${getTimeStamp()}"
        bottomText: ren
        color: RED
        horizontalText: shi
        opacity: 80
        style: NONE
        surroundTextInner:
        templateType: PERSONNEL_ROUND_STAR
        widthHeight: "38_38"
    api: api/seal/add_organizationstemplate.yml
    extract:
        - Cu_sealId: content.data.sealId
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

#新增印章授权
- test:
    name: 印章授权-授权印章使用权限
    variables:
        tenantid: $orgId
        operatorid: $adminId
        appScheme: null
        autoFall: null
        effectiveTime: *************
        expireTime: *************
        fallType: null
        grantRedirectUrl: ""
        grantType: 1
        grantedAccountIds:
           - $adminId
        grantedUserCode: null
        grantedUserName: null
        grantedUserRole: null
        h5: null
        notifySetting: false
        notifyUrl: ""
        orgId: $orgId
        resourceId: $Cu_sealId
        roleKey: "SEAL_USER"
        scopeList:
           - $flowtemplateId
        token: ""
    api: api/seal-grant/add-rule-grants.yml
    extract:
        - ruleGrantId1: content.data.ruleGrantIds.0
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

#新增印章授权
- test:
    name: 印章授权-授权印章审批权限
    variables:
        tenantid: $orgId
        operatorid: $adminId
        appScheme: null
        autoFall: null
        effectiveTime: *************
        expireTime: *************
        fallType: null
        grantRedirectUrl: ""
        grantType: 1
        grantedAccountIds:
           - $oid1
        grantedUserCode: null
        grantedUserName: null
        grantedUserRole: null
        h5: null
        notifySetting: false
        notifyUrl: ""
        orgId: $orgId
        resourceId: $Cu_sealId
        roleKey: "SEAL_EXAMINER"
        scopeList:
           - $flowtemplateId
        token: ""
    api: api/seal-grant/add-rule-grants.yml
    extract:
        - ruleGrantId3: content.data.ruleGrantIds.0
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#设置接受审批通知
- test:
    name: 授权列表-设置接受审批通知
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        ruleGrantedId: $ruleGrantId3
        shouldNotify: true

    api: api/seal-grant/setting-rule-grant-notify.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#编辑印章授权
- test:
    name: 编辑授权-编辑企业内印章授权
    variables:
        tenantid: $orgId
        operatorid: $adminId
        appScheme: null
        authKey: ""
        autoFall: null
        effectiveTime: *************
        expireReason: ""
        expireTime: *************
        fallType: null
        grantRedirectUrl: ""
        grantType: 1
        grantedAccountIds:
           - $oid1
        grantedUser: $oid1
        grantedUserCode: null
        grantedUserName: "阮小七"
        granter: ""
        granterCode: ""
        granterName: ""
        h5: null
        notifySetting: false
        resourceType: ""
        orgId: $orgId
        resourceId: $Cu_sealId
        roleKey: "SEAL_EXAMINER"
        ruleGrantedId: $ruleGrantId3
        scope: $flowtemplateId
        token: ""
        templateKey: "SEAL_AUTH"
    api: api/seal-grant/update-rule-grant.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]


#新增印章授权
- test:
    name: 印章授权-授权全部成员&自动落章
    variables:
        tenantid: $orgId
        operatorid: $adminId
        appScheme: null
        autoFall: true
        effectiveTime: *************
        expireTime: *************
        fallType: null
        grantRedirectUrl: ""
        grantType: 1
        grantedAccountIds:
           - "ALL"
        grantedUserCode: null
        grantedUserName: null
        grantedUserRole: null
        h5: null
        notifySetting: true
        notifyUrl: ""
        orgId: $orgId
        resourceId: $Cu_sealId
        roleKey: "SEAL_USER"
        scopeList:
           - $flowtemplateId
        token: ""
    api: api/seal-grant/add-rule-grants.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#新增印章授权
- test:
    name: 印章授权-授权全部合同
    variables:
        tenantid: $orgId
        operatorid: $adminId
        appScheme: null
        autoFall: false
        effectiveTime: *************
        expireTime: *************
        fallType: null
        grantRedirectUrl: ""
        grantType: 1
        grantedAccountIds:
           - $oid1
        grantedUserCode: null
        grantedUserName: null
        grantedUserRole: null
        h5: null
        notifySetting: true
        notifyUrl: ""
        orgId: $orgId
        resourceId: $Cu_sealId
        roleKey: "SEAL_USER"
        scopeList:
           - "ALL"
        token: ""
    api: api/seal-grant/add-rule-grants.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#授权企业/成员列表
- test:
    name: 授权列表-企业内授权列表
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        resourceId: $Cu_sealId
        ruleGrantStatus: INVALID
        offset: 0
        size: 20
        type: 1

    api: api/seal-grant/rule-grant-list.yml
    extract:
        - ruleGrantId_a: content.data.grantList.0.ruleGrantId
        - ruleGrantId_b: content.data.grantList.1.ruleGrantId
        - ruleGrantId_c: content.data.grantList.2.ruleGrantId
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#批量编辑印章授权
- test:
    name: 批量编辑印章授权-企业内授权
    variables:
        tenantid: $orgId
        operatorid: $adminId
        appScheme: ""
        authKey: ""
        grantRedirectUrl: ""
        h5: ""
        orgId: $orgId
        resourceId: $Cu_sealId
        token: ""
        type: 1
        ruleGrantedList:
            - effectiveTime: *************
              expireTime: *************
              ruleGrantedId: $ruleGrantId_a
            - effectiveTime: *************
              expireTime: *************
              ruleGrantedId: $ruleGrantId_b
            - effectiveTime: *************
              expireTime: *************
              ruleGrantedId: $ruleGrantId_c
    api: api/seal-grant/update-rule-grants.yml
    validate:
        - eq: ["content.code",0]
        - eq: ["content.data.successCount",3]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#获取印章授权书下载地址
- test:
    name: 获取印章授权书下载地址-印章授权书未签署（企业内授权）
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        ruleGrantedId: $ruleGrantId1
        sealOwnerOid: ""

    api: api/seal-grant/download-rule-grant.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","印章授权书未签署"]]

#删除企业内授权
- test:
    name: 删除授权-企业内授权
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        ruleGrantedId: $ruleGrantId1

    api: api/seal-grant/delete-rule-grant.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#新增印章授权
- test:
    name: 印章授权-跨企业授权
    variables:
        tenantid: $orgId
        operatorid: $adminId
        appScheme: null
        autoFall: false
        effectiveTime: *************
        expireTime: *************
        fallType: null
        grantRedirectUrl: ""
        grantType: 2
        grantedAccountIds: null
        grantedUserCode: $grantedUserCode1
        grantedUserName: $grantedUserName1
        grantedUserRole: null
        h5: null
        notifySetting: true
        notifyUrl: ""
        orgId: $orgId
        resourceId: $Cu_sealId
        roleKey: "SEAL_USER"
        scopeList:
           - "ALL"
        token: ""
    api: api/seal-grant/add-rule-grants.yml
    extract:
        - ruleGrantId2: content.data.ruleGrantIds.0
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#授权企业/成员列表
- test:
    name: 授权企业列表-跨企业授权列表
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        resourceId: $Cu_sealId
        ruleGrantStatus: ALL
        offset: 0
        size: 20
        type: 2

    api: api/seal-grant/rule-grant-list.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#获取印章授权书下载地址
- test:
    name: 获取印章授权书下载地址-印章授权书未签署（跨企业授权）
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        ruleGrantedId: $ruleGrantId2
        sealOwnerOid: ""

    api: api/seal-grant/download-rule-grant.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","印章授权书未签署"]]

#编辑印章授权
- test:
    name: 编辑授权-编辑企业外印章授权
    variables:
        tenantid: $orgId
        operatorid: $adminId
        appScheme: null
        authKey: ""
        autoFall: null
        effectiveTime: *************
        expireReason: ""
        expireTime: *************
        fallType: null
        grantRedirectUrl: ""
        grantType: 2
        grantedAccountIds: null
        grantedUser: $orgId2
        grantedUserCode: $grantedUserCode1
        grantedUserName: $grantedUserName1
        granter: $adminId
        granterCode: ""
        granterName: ""
        h5: null
        notifySetting: false
        resourceType: "SEAL"
        orgId: $orgId
        resourceId: $Cu_sealId
        roleKey: "SEAL_EXAMINER"
        ruleGrantedId: $ruleGrantId2
        scope: "ALL"
        token: ""
        templateKey: "SEAL_AUTH"
    api: api/seal-grant/update-rule-grant.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

#删除企业外授权
- test:
    name: 删除授权-跨企业授权
    variables:
        tenantid: $orgId
        operatorid: $adminId
        orgId: $orgId
        ruleGrantedId: $ruleGrantId2

    api: api/seal-grant/delete-rule-grant.yml
    validate:
        - eq: ["content.code",0]
        - contained_by: ["content.message", ["成功","缺少参数"]]

- test:
    name: 删除印章rpc接口-清数据
    variables:
        Rpc_sealId: $Cu_sealId
    api: api/seal/deleteSeal_rpc.yml
    validate:
        - eq: ["status_code",200]
        - contains: ["content.message","执行成功"]
