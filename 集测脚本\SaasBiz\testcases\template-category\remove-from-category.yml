- config:
    name: 移除分类下的模板
    variables:
      orgId1: ${ENV(orgId2)}                   #基础版以上的企业
      accountId1: ${ENV(accountId1)}           #orgId1和orgId2的管理员
      categoryId1: ${ENV(categoryId1)}         #orgId1下的模板分类
      flowTemplateId1: ${ENV(flowTemplateId1)} #orgId1下的流程模板
      accountId2: ${ENV(accountId2)}           #orgId1下的普通成员
      accountId3: ${ENV(accountId3)}           #不是orgId1下的成员
      roleId1: ${ENV(roleId1)}                 #orgId1企业下编辑模板权限的角色id
      orgId2: ${ENV(orgId3)}                   #基础版的企业
      categoryId2: ${ENV(categoryId2)}         #orgId2下的模板分类
      flowTemplateId2: ${ENV(flowTemplateId3)} #orgId2下的流程模板


- test:
    name: 移除分类下的模板-flowTemplateId为空
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: ""
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", flowTemplateId不能为空]

- test:
    name: 移除分类下的模板-flowTemplateId不存在
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: 123
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 流程模板不存在]

- test:
    name: 移除分类下的模板-flowTemplateId不属于当前企业
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId2
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
#      - contains: ["content.message", 流程模板拥有者不匹配]

- test:
    name: 移除分类下的模板-categoryId为空
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
      categoryId: ""
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类id不能为空]

- test:
    name: 移除分类下的模板-categoryId不存在
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
      categoryId: 123
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类不存在]

- test:
    name: 移除分类下的模板-categoryId不属于当前企业
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
      categoryId: $categoryId2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 分类不存在]

- test:
    name: 移除分类下的模板-操作人不是企业成员
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId3
      flowTemplateId: $flowTemplateId1
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", "企业成员不存在"]

#- test:
#    name: 移除分类下的模板-操作人无该模板的编辑权限
#    api: api/template-category/remove-from-category.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId2
#      flowTemplateId: $flowTemplateId1
#      categoryId: $categoryId1
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", *********]
#      - contains: ["content.message", "您没有模板编辑权限"]


- test:
    name: 根据分组获取可操作的角色列表
    api: api/role/getRoleList.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      group: TEMP_GROUP
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - roleList: content.data     #企业下模板授权的roleList

- test:
    name: 流程模板批量授权-给accountId2设置模板的编辑权限
    api: api/template-manage/batchAuth.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      roleId2: ${getValue($roleList, roleKey, TEMP_USE, id)}  #企业下可使用的roleId
      roleId3: ${getValue($roleList, roleKey, TEMP_UPDATE, id)}  #企业下可编辑的roleId
      flowTemplateAuthList:
        [
        {
          "authList":[
          {
            "authId":"ALL",
            "accountOid":"",
            "roleId":"$roleId2",
            "roleKey":"TEMP_USE",
            "type":1
          },
          {
            "authId":null,
            "accountOid":"$accountId2",
            "roleId":"$roleId3",
            "roleKey":"TEMP_UPDATE",
            "type":2
          }
          ],
          "flowTemplateId":"$flowTemplateId1"
        }
        ]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

#- test:
#    name: 移除分类下的模板-操作人有该模板的编辑权限
#    api: api/template-category/remove-from-category.yml
#    variables:
#      tenantId: $orgId1
#      operatorId: $accountId2
#      flowTemplateId: $flowTemplateId1
#      categoryId: $categoryId1
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]

- test:
    name: 流程模板批量授权-取消accountId2的模板编辑权限
    api: api/template-manage/batchAuth.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      roleId2: ${getValue($roleList, roleKey, TEMP_USE, id)}  #企业下可使用的roleId
      flowTemplateAuthList:
        [
        {
          "authList":[
          {
            "authId":"ALL",
            "accountOid":"",
            "roleId":"$roleId2",
            "roleKey":"TEMP_USE",
            "type":1
          }
          ],
          "flowTemplateId":"$flowTemplateId1"
        }
        ]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-给accountId2设置全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: [$roleId1]
      memberName: ""
      revokeRoleIds: []
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 移除分类下的模板-操作人有全局模板编辑权限
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId2
      flowTemplateId: $flowTemplateId1
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 更新成员所有信息-取消accountId2的全局模板编辑权限
    api: api/footstone-user-api/members-updateOverall.yml
    variables:
      operatorId: $accountId1
      orgId: $orgId1
      accountId: $accountId2
      employeeId: ""
      grantRoleIds: []
      memberName: ""
      revokeRoleIds: [$roleId1]
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 移除分类下的模板-管理员默认有权限
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId1
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId1
      categoryId: $categoryId1
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 移除分类下的模板-基础版企业不支持
    api: api/template-category/remove-from-category.yml
    variables:
      tenantId: $orgId2
      operatorId: $accountId1
      flowTemplateId: $flowTemplateId2
      categoryId: $categoryId2
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", *********]
      - contains: ["content.message", 版本功能不支持]
