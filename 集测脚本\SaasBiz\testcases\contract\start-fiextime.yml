- config:
    name: 获取流程模板参与方节点
    variables:
      orgOid1: 714902c36f2440e281b2ff984b6e29f4         #发起主体企业
      accountOid1: b4d26d9fd5e644959cadb9434c9c41c6     #发起人无需合同审批
      file_name1: "test.pdf"
      accountOid2: a959b44e7e0f47c18e205cec5bdd861e     #企业签署人
      account2: ***********
      accountName2: 自动化测试零三
      orgOid2: bc7a0b1bf2bf42f1b99818f660b97420         #签署主体企业
      orgName2: esigntest自动化测试企业2
      accountOid3: c0757ce4e3e3476196fd22d0c56bc1f2    #个人签署人
      account3: ***********
      accountName3: 自动化测试零二
      templateName1: 定时发起模板-${getTimeStamp_ms()}
      accountOid4: 3d02a7c203fc436d8f5baf86a5481235     #发起主体企业的管理员
      orgGid1: 73f94e148b994586b8de56f89210150b
      #      fileId1:
      #      pwd: C06DB68E819BE6EC3D26C6038D8E8D1F             #签署人/审批人签署密码
      db_name1: contract_manager

- test:
    name: 上传文件
    testcase: testcases/upload_file.yml
    variables:
      tenantId1: $orgOid1
      accountId1: $accountOid4
      contentMd5: m49PzxWxAG8sMwNkZrlJDw==
      contentType: application/pdf
      convert2Pdf: false
      fileName1: $file_name1
      fileSize1: 99580
      filePath1: "data/test.pdf"
    output:
      - fileId1
- test:
    name: 保存流程模板
    api: api/contract-manager2/save_flowTemplate.yml
    variables:
      tenantId: $orgOid1
      businessType: 0
      ccs: []
      files:
        [
        {
          "fileId": $fileId1,
          "fileType":1,             #文件类型，1-合并文件 2-附件
          "fileName": $file_name1,
          "from":2,                 #文件来自 1-模板文件 2-合同文件
          "fileSecret":false        #文件是否保密
        }
        ]
      flowTemplateId: ""
      initiatorAccountId: $accountOid4
      scheduledInitiateTime: ${timestamp_after_n_seconds(5)}
      participants:
        [
        {
          "participantSubjectType":0,
          "role":"3",
          "sealType":"0,1",
          "signRequirements":null,
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方1",
          "participantId":null,
          "instances":
            [
            {
              "account": $account3,
              "accountOid": $accountOid3,
              "accountNick": "",
              "accountName": $accountName3,
              "accountRealName": true,
              "subjectId": $accountOid3,
              "subjectName": $accountName3,
              "subjectRealName": true,
              "subjectType": 0
            }
            ],
          "willTypes":[]
        },
        {
          "participantSubjectType":1,
          "role":"3",
          "sealType":null,
          "signRequirements":"1",
          "roleSet":1,
          "fillOrder":1,
          "signOrder":1,
          "participantLabel":"签署方2",
          "participantId":null,
          "instances":
          [
          {
            "account": $account2,
            "accountOid": $accountOid2,
            "accountNick": "",
            "accountName": $accountName2,
            "accountRealName": true,
            "subjectId": $orgOid2,
            "subjectName": $orgName2,
            "subjectRealName": true,
            "subjectType": 1
          }
          ],
          "willTypes":[]
        }
        ]
      previewType: 0
      scene: 4
      taskName: $templateName1
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - ne: ["content.data.processId", null]
      - eq: ["content.data.realProcessId", null]
    extract:
      - flowTemplateId1: content.data.processId

- test:
    name: 返回操作按钮
    api: api/flow-templates/participant-nodes.yml
    variables:
      operatorId: $accountOid4
      tenantId: $orgOid1
      flowTemplateId: $flowTemplateId1
    validate:
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", 成功 ]